"""
WMS客户端系统 - UI演示程序
展示企业风格：正式、简约、大气的UI设计
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from PyQt6.QtWidgets import QApplication, QStackedWidget
from PyQt6.QtCore import Qt

from ui.windows.login_window import LoginWindow
from ui.windows.dashboard_window import DashboardWindow
from ui.windows.main_window import MainWindow


class WMSApplication:
    """WMS应用程序主类"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self._setup_app()
        self._setup_windows()
        self._setup_connections()
    
    def _setup_app(self):
        """设置应用程序"""
        # 设置应用程序信息
        self.app.setApplicationName("WMS库房自助出入库客户端系统")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("贵州睿云慧通科技有限公司")
        self.app.setOrganizationDomain("www.gzryht.com")
        
        # 设置应用程序样式
        self.app.setStyle("Fusion")
        
        # 设置高DPI支持 (PyQt6中这些属性已经默认启用，不需要手动设置)
        # self.app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
        # self.app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)
    
    def _setup_windows(self):
        """设置窗口"""
        # 创建登录窗口
        self.login_window = LoginWindow()

        # 创建4卡片主窗口
        self.dashboard_window = DashboardWindow()

        # 创建全功能主窗口
        self.main_window = MainWindow()

        # 初始状态下隐藏所有窗口
        self.dashboard_window.hide()
        self.main_window.hide()
    
    def _setup_connections(self):
        """设置信号连接"""
        # 登录成功后切换到4卡片主窗口
        self.login_window.login_success.connect(self._on_login_success)

        # 管理员登录成功后切换到全功能主窗口
        self.dashboard_window.admin_login_success.connect(self._on_admin_login_success)
    
    def _on_login_success(self, user_info: dict):
        """登录成功处理"""
        print(f"✅ 用户登录成功: {user_info}")

        # 关闭登录窗口
        self.login_window.hide()

        # 显示4卡片主窗口并最大化
        self.dashboard_window.show()
        self.dashboard_window.showMaximized()

        print("📋 已切换到4卡片主窗口")

    def _on_admin_login_success(self, admin_info: dict):
        """管理员登录成功处理"""
        print(f"🔐 管理员登录成功: {admin_info}")

        # 关闭4卡片窗口
        self.dashboard_window.hide()

        # 显示全功能主窗口并最大化
        self.main_window.show()
        self.main_window.showMaximized()

        print("🖥️ 已切换到全功能主窗口")

        # 更新主窗口用户信息
        # self.main_window.set_user_info(admin_info)
    
    def run(self):
        """运行应用程序"""
        # 显示登录窗口
        self.login_window.show()
        
        # 启动事件循环
        return self.app.exec()


def main():
    """主函数"""
    # 创建应用程序实例
    app = WMSApplication()
    
    # 运行应用程序
    sys.exit(app.run())


if __name__ == "__main__":
    main()
