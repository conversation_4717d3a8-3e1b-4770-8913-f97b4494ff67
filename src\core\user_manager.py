"""
用户管理核心业务模块
© 2024 贵州睿云慧通科技有限公司
"""

import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..models.database import db_manager
from ..models.user import User, UserRole, IdentityCard, FaceRecord, UserSession
from ..utils.logger import get_logger
from ..utils.exceptions import AuthenticationError, ValidationError, PermissionError

logger = get_logger(__name__)

class UserManager:
    """用户管理器"""
    
    def __init__(self):
        self.session_timeout = 3600  # 会话超时时间(秒)
    
    def authenticate(self, username: str, password: str, ip_address: str = None) -> Dict[str, Any]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            ip_address: IP地址
            
        Returns:
            认证结果字典
            
        Raises:
            AuthenticationError: 认证失败
        """
        try:
            with db_manager.get_session() as session:
                # 查找用户
                user = session.query(User).filter(
                    and_(
                        User.username == username,
                        User.is_active == True
                    )
                ).first()
                
                if not user:
                    logger.warning(f"用户不存在或已禁用: {username}")
                    raise AuthenticationError("用户名或密码错误")
                
                # 验证密码
                if not user.check_password(password):
                    logger.warning(f"密码错误: {username}")
                    raise AuthenticationError("用户名或密码错误")
                
                # 更新最后登录时间
                user.last_login = datetime.now()
                session.commit()
                
                # 创建会话
                session_token = self._create_session(session, user.id, ip_address)
                
                logger.info(f"用户登录成功: {username}")
                
                return {
                    "user": user.to_dict(),
                    "session_token": session_token,
                    "permissions": self._get_user_permissions(user.role)
                }
                
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            raise AuthenticationError("认证失败")
    
    def login_with_rfid(self, card_id: str, ip_address: str = None) -> Dict[str, Any]:
        """
        RFID卡片登录
        
        Args:
            card_id: 卡片ID
            ip_address: IP地址
            
        Returns:
            登录结果字典
        """
        try:
            with db_manager.get_session() as session:
                user = session.query(User).filter(
                    and_(
                        User.rfid_card == card_id,
                        User.is_active == True
                    )
                ).first()
                
                if not user:
                    logger.warning(f"RFID卡片未绑定或用户已禁用: {card_id}")
                    raise AuthenticationError("卡片未绑定或用户已禁用")
                
                # 更新最后登录时间
                user.last_login = datetime.now()
                session.commit()
                
                # 创建会话
                session_token = self._create_session(session, user.id, ip_address)
                
                logger.info(f"RFID登录成功: {user.username}")
                
                return {
                    "user": user.to_dict(),
                    "session_token": session_token,
                    "permissions": self._get_user_permissions(user.role)
                }
                
        except Exception as e:
            logger.error(f"RFID登录失败: {e}")
            raise AuthenticationError("RFID登录失败")
    
    def login_with_face(self, face_encoding: str, ip_address: str = None) -> Dict[str, Any]:
        """
        人脸识别登录
        
        Args:
            face_encoding: 人脸特征编码
            ip_address: IP地址
            
        Returns:
            登录结果字典
        """
        try:
            with db_manager.get_session() as session:
                # 这里需要实现人脸匹配算法
                # 简化实现，实际应该使用人脸识别库进行匹配
                face_record = session.query(FaceRecord).filter(
                    and_(
                        FaceRecord.face_encoding == face_encoding,
                        FaceRecord.is_active == True
                    )
                ).first()
                
                if not face_record:
                    logger.warning("人脸识别失败")
                    raise AuthenticationError("人脸识别失败")
                
                user = session.query(User).filter(
                    and_(
                        User.id == face_record.user_id,
                        User.is_active == True
                    )
                ).first()
                
                if not user:
                    logger.warning("用户已禁用")
                    raise AuthenticationError("用户已禁用")
                
                # 更新最后登录时间
                user.last_login = datetime.now()
                session.commit()
                
                # 创建会话
                session_token = self._create_session(session, user.id, ip_address)
                
                logger.info(f"人脸识别登录成功: {user.username}")
                
                return {
                    "user": user.to_dict(),
                    "session_token": session_token,
                    "permissions": self._get_user_permissions(user.role)
                }
                
        except Exception as e:
            logger.error(f"人脸识别登录失败: {e}")
            raise AuthenticationError("人脸识别登录失败")
    
    def verify_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """
        验证会话
        
        Args:
            session_token: 会话令牌
            
        Returns:
            用户信息字典或None
        """
        try:
            with db_manager.get_session() as session:
                user_session = session.query(UserSession).filter(
                    and_(
                        UserSession.session_token == session_token,
                        UserSession.is_active == True
                    )
                ).first()
                
                if not user_session:
                    return None
                
                # 检查会话是否过期
                if (datetime.now() - user_session.last_activity).seconds > self.session_timeout:
                    user_session.is_active = False
                    session.commit()
                    return None
                
                # 更新最后活动时间
                user_session.last_activity = datetime.now()
                session.commit()
                
                # 获取用户信息
                user = session.query(User).filter(User.id == user_session.user_id).first()
                if not user or not user.is_active:
                    return None
                
                return {
                    "user": user.to_dict(),
                    "session": user_session.to_dict(),
                    "permissions": self._get_user_permissions(user.role)
                }
                
        except Exception as e:
            logger.error(f"会话验证失败: {e}")
            return None
    
    def logout(self, session_token: str) -> bool:
        """
        用户登出
        
        Args:
            session_token: 会话令牌
            
        Returns:
            是否成功
        """
        try:
            with db_manager.get_session() as session:
                user_session = session.query(UserSession).filter(
                    UserSession.session_token == session_token
                ).first()
                
                if user_session:
                    user_session.is_active = False
                    session.commit()
                    logger.info(f"用户登出成功: {user_session.user_id}")
                
                return True
                
        except Exception as e:
            logger.error(f"用户登出失败: {e}")
            return False
    
    def create_user(self, user_data: Dict[str, Any], creator_id: int) -> Dict[str, Any]:
        """
        创建用户
        
        Args:
            user_data: 用户数据
            creator_id: 创建者ID
            
        Returns:
            创建的用户信息
        """
        try:
            with db_manager.get_session() as session:
                # 验证创建者权限
                creator = session.query(User).filter(User.id == creator_id).first()
                if not creator or not creator.has_permission("user_manage"):
                    raise PermissionError("无权限创建用户")
                
                # 检查用户名是否已存在
                existing_user = session.query(User).filter(
                    User.username == user_data["username"]
                ).first()
                if existing_user:
                    raise ValidationError("用户名已存在")
                
                # 创建用户
                user = User(
                    username=user_data["username"],
                    email=user_data.get("email"),
                    full_name=user_data["full_name"],
                    role=user_data.get("role", UserRole.OPERATOR.value),
                    rfid_card=user_data.get("rfid_card"),
                    phone=user_data.get("phone"),
                    department=user_data.get("department")
                )
                user.set_password(user_data["password"])
                
                session.add(user)
                session.commit()
                
                logger.info(f"用户创建成功: {user.username}")
                
                return user.to_dict()
                
        except Exception as e:
            logger.error(f"用户创建失败: {e}")
            raise
    
    def _create_session(self, session: Session, user_id: int, ip_address: str = None) -> str:
        """创建用户会话"""
        session_token = secrets.token_urlsafe(32)
        
        user_session = UserSession(
            user_id=user_id,
            session_token=session_token,
            ip_address=ip_address
        )
        
        session.add(user_session)
        session.commit()
        
        return session_token
    
    def _get_user_permissions(self, role: str) -> List[str]:
        """获取用户权限列表"""
        role_permissions = {
            UserRole.ADMIN.value: [
                "user_manage", "system_config", "data_backup",
                "inventory_manage", "order_manage", "report_view",
                "device_manage", "log_view"
            ],
            UserRole.MANAGER.value: [
                "inventory_manage", "order_manage", "report_view",
                "user_view", "device_view"
            ],
            UserRole.OPERATOR.value: [
                "inventory_view", "order_create", "order_view"
            ],
            UserRole.VISITOR.value: [
                "inventory_view"
            ]
        }
        return role_permissions.get(role, [])
