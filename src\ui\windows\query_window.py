"""
WMS客户端系统 - 智能查询窗口
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QFrame, QApplication, QLabel, QMessageBox, QPushButton, QDialog,
    QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QSpinBox, QTextEdit, QGroupBox, QFormLayout, QTabWidget, QDateEdit
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize, QTimer, QDate
from PyQt6.QtGui import QFont, QPalette, QIcon

from ..components.base_components import (
    BaseCard, TitleLabel, BodyLabel, PrimaryButton, SecondaryButton
)
from ..styles.theme import theme


class QueryWindow(QMainWindow):
    """智能查询窗口"""
    
    # 信号定义
    back_to_dashboard = pyqtSignal()  # 返回主界面信号
    
    def __init__(self):
        super().__init__()
        self._setup_ui()
        self._setup_style()
        self._setup_connections()
    
    def _setup_ui(self):
        """设置UI - 满屏效果"""
        self.setWindowTitle("WMS库房自助出入库客户端系统 - 智能查询")
        
        # 获取屏幕信息并设置全屏
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        
        # 设置窗口为全屏
        self.setGeometry(screen_geometry)
        self.showFullScreen()
        
        # 设置窗口标志 - 无边框，置顶
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 设置背景样式
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:0.5 #2563eb, stop:1 #3b82f6);
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(30, 20, 30, 20)
        main_layout.setSpacing(20)
        
        # 顶部工具栏
        self._create_toolbar(main_layout)
        
        # 内容区域
        self._create_content_area(main_layout)
        
        # 底部状态栏
        self._create_status_bar(main_layout)
    
    def _create_toolbar(self, main_layout):
        """创建顶部工具栏"""
        toolbar = QWidget()
        toolbar.setFixedHeight(60)
        toolbar.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.95);
                border-bottom: 1px solid #e2e8f0;
                border-radius: 12px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(20, 10, 20, 10)
        
        # 返回按钮
        back_btn = QPushButton("← 返回主界面")
        back_btn.setFixedSize(120, 40)
        back_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6, stop:1 #2563eb);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563eb, stop:1 #1d4ed8);
            }
        """)
        back_btn.clicked.connect(self.back_to_dashboard.emit)
        toolbar_layout.addWidget(back_btn)
        
        # 标题
        title_label = QLabel("🔍 智能查询")
        title_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                font-size: 24px;
                font-weight: 700;
                font-family: "Microsoft YaHei UI", sans-serif;
                background-color: transparent;
                margin-left: 20px;
            }
        """)
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # 导出按钮
        export_btn = QPushButton("📊 导出数据")
        export_btn.setFixedSize(120, 40)
        export_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6, stop:1 #2563eb);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563eb, stop:1 #1d4ed8);
            }
        """)
        export_btn.clicked.connect(self._export_data)
        toolbar_layout.addWidget(export_btn)
        
        main_layout.addWidget(toolbar)
    
    def _create_content_area(self, main_layout):
        """创建内容区域"""
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:1 rgba(248, 250, 252, 0.95));
                border-radius: 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)
        
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(30, 30, 30, 30)
        content_layout.setSpacing(20)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8fafc;
                color: #374151;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: 600;
            }
            QTabBar::tab:selected {
                background-color: #3b82f6;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #e5e7eb;
            }
        """)
        
        # 库存查询选项卡
        self._create_inventory_tab()
        
        # 操作记录选项卡
        self._create_operation_tab()
        
        # 统计报表选项卡
        self._create_statistics_tab()
        
        content_layout.addWidget(self.tab_widget)
        main_layout.addWidget(content_widget)
    
    def _create_inventory_tab(self):
        """创建库存查询选项卡"""
        inventory_widget = QWidget()
        layout = QVBoxLayout(inventory_widget)
        layout.setSpacing(20)
        
        # 查询条件
        search_group = QGroupBox("查询条件")
        search_group.setStyleSheet(self._get_group_style())
        search_layout = QHBoxLayout(search_group)
        search_layout.setSpacing(15)
        
        # 商品编码
        self.inv_code_input = QLineEdit()
        self.inv_code_input.setPlaceholderText("商品编码")
        self.inv_code_input.setFixedHeight(35)
        self._style_input(self.inv_code_input)
        search_layout.addWidget(QLabel("商品编码:"))
        search_layout.addWidget(self.inv_code_input)
        
        # 商品名称
        self.inv_name_input = QLineEdit()
        self.inv_name_input.setPlaceholderText("商品名称")
        self.inv_name_input.setFixedHeight(35)
        self._style_input(self.inv_name_input)
        search_layout.addWidget(QLabel("商品名称:"))
        search_layout.addWidget(self.inv_name_input)
        
        # 存储位置
        self.inv_location_combo = QComboBox()
        self.inv_location_combo.addItems(["全部", "A区", "B区", "C区"])
        self.inv_location_combo.setFixedHeight(35)
        self._style_input(self.inv_location_combo)
        search_layout.addWidget(QLabel("存储位置:"))
        search_layout.addWidget(self.inv_location_combo)
        
        # 查询按钮
        search_btn = QPushButton("🔍 查询")
        search_btn.setFixedSize(100, 35)
        search_btn.setStyleSheet(self._get_button_style("#3b82f6"))
        search_btn.clicked.connect(self._search_inventory)
        search_layout.addWidget(search_btn)
        
        layout.addWidget(search_group)
        
        # 结果表格
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(6)
        self.inventory_table.setHorizontalHeaderLabels([
            "商品编码", "商品名称", "当前库存", "存储位置", "最后更新", "状态"
        ])
        self._style_table(self.inventory_table)
        
        # 添加示例数据
        self._load_inventory_data()
        
        layout.addWidget(self.inventory_table)
        
        self.tab_widget.addTab(inventory_widget, "📦 库存查询")
    
    def _create_operation_tab(self):
        """创建操作记录选项卡"""
        operation_widget = QWidget()
        layout = QVBoxLayout(operation_widget)
        layout.setSpacing(20)
        
        # 查询条件
        search_group = QGroupBox("查询条件")
        search_group.setStyleSheet(self._get_group_style())
        search_layout = QHBoxLayout(search_group)
        search_layout.setSpacing(15)
        
        # 操作类型
        self.op_type_combo = QComboBox()
        self.op_type_combo.addItems(["全部", "入库", "出库", "调拨", "盘点"])
        self.op_type_combo.setFixedHeight(35)
        self._style_input(self.op_type_combo)
        search_layout.addWidget(QLabel("操作类型:"))
        search_layout.addWidget(self.op_type_combo)
        
        # 开始日期
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-7))
        self.start_date.setFixedHeight(35)
        self._style_input(self.start_date)
        search_layout.addWidget(QLabel("开始日期:"))
        search_layout.addWidget(self.start_date)
        
        # 结束日期
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setFixedHeight(35)
        self._style_input(self.end_date)
        search_layout.addWidget(QLabel("结束日期:"))
        search_layout.addWidget(self.end_date)
        
        # 操作员
        self.operator_input = QLineEdit()
        self.operator_input.setPlaceholderText("操作员")
        self.operator_input.setFixedHeight(35)
        self._style_input(self.operator_input)
        search_layout.addWidget(QLabel("操作员:"))
        search_layout.addWidget(self.operator_input)
        
        # 查询按钮 - 使用统一的BaseButton组件
        from ..components.base_components import PrimaryButton

        search_btn = PrimaryButton("🔍 查询")
        search_btn.setFixedSize(100, 35)
        search_btn.clicked.connect(self._search_operations)
        search_layout.addWidget(search_btn)
        
        layout.addWidget(search_group)
        
        # 结果表格
        self.operation_table = QTableWidget()
        self.operation_table.setColumnCount(7)
        self.operation_table.setHorizontalHeaderLabels([
            "操作时间", "操作类型", "商品编码", "商品名称", "数量", "操作员", "备注"
        ])
        self._style_table(self.operation_table)
        
        # 添加示例数据
        self._load_operation_data()
        
        layout.addWidget(self.operation_table)
        
        self.tab_widget.addTab(operation_widget, "📋 操作记录")
    
    def _create_statistics_tab(self):
        """创建统计报表选项卡"""
        stats_widget = QWidget()
        layout = QVBoxLayout(stats_widget)
        layout.setSpacing(20)
        
        # 统计卡片 - 使用统一的统计组件
        from ..components.statistics_components import StatisticsGrid

        stats_grid = StatisticsGrid(columns=4)

        # 添加统计卡片数据
        cards_data = [
            {"title": "今日入库", "value": "156", "unit": "件", "color": theme.colors.SUCCESS, "trend": "比昨日 +12%"},
            {"title": "今日出库", "value": "89", "unit": "件", "color": theme.colors.WARNING, "trend": "比昨日 -5%"},
            {"title": "库存总量", "value": "12,456", "unit": "件", "color": theme.colors.SECONDARY, "trend": "正常水位"},
            {"title": "待处理", "value": "23", "unit": "单", "color": theme.colors.ERROR, "trend": "需要关注"}
        ]

        stats_grid.add_cards(cards_data)
        layout.addWidget(stats_grid)
        
        # 详细统计表格 - 使用统一的统计表格组件
        from ..components.statistics_components import StatisticsTable

        self.stats_table_component = StatisticsTable(
            title="详细统计",
            headers=["商品编码", "商品名称", "入库总量", "出库总量", "当前库存"]
        )

        # 添加示例数据
        self._load_statistics_data_to_component()

        layout.addWidget(self.stats_table_component)
        
        self.tab_widget.addTab(stats_widget, "📊 统计报表")
    
    def _create_stat_card(self, title, value, unit, color):
        """创建统计卡片 - 与系统设置风格统一"""

        card = BaseCard()
        card.setFixedHeight(120)

        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(theme.spacing.SM)

        # 标题 - 使用统一的字体大小和颜色
        title_label = BodyLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {theme.colors.TEXT_PRIMARY};
                font-size: {theme.typography.BODY_SIZE}px;
                font-weight: {theme.typography.WEIGHT_MEDIUM};
            }}
        """)
        layout.addWidget(title_label)

        # 数值 - 使用适中的字体大小
        value_label = TitleLabel(value, level=2)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: {theme.typography.H3_SIZE}px;
                font-weight: {theme.typography.WEIGHT_BOLD};
            }}
        """)
        layout.addWidget(value_label)

        # 单位 - 使用统一的辅助文字颜色
        unit_label = BodyLabel(unit)
        unit_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        unit_label.setStyleSheet(f"""
            QLabel {{
                color: {theme.colors.TEXT_SECONDARY};
                font-size: {theme.typography.BODY_SMALL_SIZE}px;
                font-weight: {theme.typography.WEIGHT_MEDIUM};
            }}
        """)
        layout.addWidget(unit_label)

        card.add_layout(layout)
        return card
    
    def _create_status_bar(self, main_layout):
        """创建状态栏"""
        status_bar = QWidget()
        status_bar.setFixedHeight(50)
        status_bar.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.95);
                border-top: 1px solid #e2e8f0;
                border-radius: 8px;
            }
        """)
        
        status_layout = QHBoxLayout(status_bar)
        status_layout.setContentsMargins(20, 10, 20, 10)
        
        # 状态信息
        self.status_label = QLabel("就绪 - 请选择查询条件")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #3b82f6;
                font-size: 14px;
                font-weight: 500;
            }
        """)
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 结果统计
        self.result_label = QLabel("查询结果: 0 条记录")
        self.result_label.setStyleSheet("""
            QLabel {
                color: #374151;
                font-size: 14px;
                font-weight: 500;
            }
        """)
        status_layout.addWidget(self.result_label)
        
        main_layout.addWidget(status_bar)
    
    def _get_group_style(self):
        """获取分组样式 - 与系统设置风格统一"""

        return f"""
            QGroupBox {{
                font-size: {theme.typography.BODY_SIZE}px;
                font-weight: {theme.typography.WEIGHT_MEDIUM};
                color: {theme.colors.TEXT_PRIMARY};
                border: 2px solid {theme.colors.BORDER};
                border-radius: {theme.border_radius.LG}px;
                margin-top: {theme.spacing.SM}px;
                padding-top: {theme.spacing.SM}px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {theme.spacing.SM}px;
                padding: 0 {theme.spacing.SM}px 0 {theme.spacing.SM}px;
            }}
        """
    
    def _get_button_style(self, color):
        """获取按钮样式 - 与系统设置风格统一"""

        return f"""
            QPushButton {{
                background-color: {color};
                color: {theme.colors.WHITE};
                border: none;
                border-radius: {theme.border_radius.LG}px;
                font-size: {theme.typography.BODY_SIZE}px;
                font-weight: {theme.typography.WEIGHT_MEDIUM};
                padding: {theme.spacing.SM}px {theme.spacing.MD}px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
            QPushButton:pressed {{
                background-color: {color}bb;
            }}
        """
    
    def _style_input(self, widget):
        """设置输入控件样式 - 与系统设置风格统一"""

        widget.setStyleSheet(f"""
            QLineEdit, QComboBox, QDateEdit {{
                background-color: {theme.colors.WHITE};
                border: 2px solid {theme.colors.BORDER};
                border-radius: {theme.border_radius.LG}px;
                padding: {theme.spacing.SM}px {theme.spacing.MD}px;
                font-size: {theme.typography.BODY_SIZE}px;
                color: {theme.colors.TEXT_PRIMARY};
                min-height: 20px;
            }}
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {{
                border-color: {theme.colors.SECONDARY};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 25px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid {theme.colors.TEXT_SECONDARY};
                margin-right: {theme.spacing.SM}px;
            }}
        """)
    
    def _style_table(self, table):
        """设置表格样式 - 与系统设置风格统一"""

        table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {theme.colors.WHITE};
                border: 1px solid {theme.colors.BORDER};
                border-radius: {theme.border_radius.MD}px;
                gridline-color: {theme.colors.BORDER};
                font-size: {theme.typography.BODY_SIZE}px;
            }}
            QTableWidget::item {{
                padding: {theme.spacing.SM}px;
                border-bottom: 1px solid {theme.colors.BORDER};
            }}
            QTableWidget::item:selected {{
                background-color: {theme.colors.SECONDARY};
                color: {theme.colors.WHITE};
            }}
            QHeaderView::section {{
                background-color: {theme.colors.BACKGROUND};
                color: {theme.colors.TEXT_PRIMARY};
                padding: {theme.spacing.MD}px;
                border: none;
                border-bottom: 2px solid {theme.colors.PRIMARY};
                font-weight: {theme.typography.WEIGHT_MEDIUM};
            }}
        """)
        
        # 设置列宽自适应
        header = table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
    
    def _load_inventory_data(self):
        """加载库存数据"""
        data = [
            ["P001", "苹果 iPhone 15", "50", "A01-01-01", "2024-01-15 14:30", "正常"],
            ["P002", "华为 Mate 60", "30", "A01-01-02", "2024-01-15 14:25", "正常"],
            ["P003", "小米 14", "25", "A01-02-01", "2024-01-15 14:20", "正常"],
            ["P004", "OPPO Find X7", "15", "B01-01-01", "2024-01-15 14:15", "库存不足"],
            ["P005", "vivo X100", "40", "B01-01-02", "2024-01-15 14:10", "正常"],
        ]
        
        self.inventory_table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(cell_data)
                if col == 5 and cell_data == "库存不足":
                    item.setBackground(QPalette().color(QPalette.ColorRole.Base))
                self.inventory_table.setItem(row, col, item)
    
    def _load_operation_data(self):
        """加载操作记录数据"""
        data = [
            ["2024-01-15 14:30:25", "入库", "P001", "苹果 iPhone 15", "10", "张三", "采购入库"],
            ["2024-01-15 14:25:10", "出库", "P002", "华为 Mate 60", "5", "李四", "销售出库"],
            ["2024-01-15 14:20:05", "入库", "P003", "小米 14", "15", "王五", "调拨入库"],
            ["2024-01-15 14:15:30", "出库", "P004", "OPPO Find X7", "8", "赵六", "销售出库"],
            ["2024-01-15 14:10:15", "入库", "P005", "vivo X100", "20", "钱七", "采购入库"],
        ]
        
        self.operation_table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                self.operation_table.setItem(row, col, QTableWidgetItem(cell_data))
    
    def _load_statistics_data_to_component(self):
        """加载统计数据到统一组件"""
        data = [
            ["P001", "苹果 iPhone 15", "100", "50", "50"],
            ["P002", "华为 Mate 60", "80", "50", "30"],
            ["P003", "小米 14", "60", "35", "25"],
            ["P004", "OPPO Find X7", "40", "25", "15"],
            ["P005", "vivo X100", "90", "50", "40"],
        ]

        self.stats_table_component.set_data(data)

    def _load_statistics_data(self):
        """加载统计数据 - 保持向后兼容"""
        # 如果还有其他地方使用旧的stats_table，保留此方法
        pass
    
    def _setup_style(self):
        """设置样式"""
        pass
    
    def _setup_connections(self):
        """设置信号连接"""
        pass
    
    def _search_inventory(self):
        """搜索库存"""
        self.status_label.setText("正在查询库存信息...")
        QTimer.singleShot(1000, lambda: self._search_complete("库存"))
    
    def _search_operations(self):
        """搜索操作记录"""
        self.status_label.setText("正在查询操作记录...")
        QTimer.singleShot(1000, lambda: self._search_complete("操作记录"))
    
    def _search_complete(self, query_type):
        """查询完成"""
        self.status_label.setText(f"{query_type}查询完成")
        self.result_label.setText("查询结果: 5 条记录")
    
    def _export_data(self):
        """导出数据"""
        QMessageBox.information(self, "导出成功", "数据已导出到 Excel 文件！")
