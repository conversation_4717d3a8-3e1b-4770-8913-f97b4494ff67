# WMS客户端系统 - 打包说明

## 📋 概述

本文档说明如何将WMS库房自助出入库客户端系统打包成Windows 10可执行文件。

## 🔧 环境要求

### 系统要求
- Windows 10/11 (64位)
- Python 3.8+ (推荐Python 3.11)
- 至少4GB可用内存
- 至少2GB可用磁盘空间

### Python环境
```bash
# 检查Python版本
python --version

# 建议使用虚拟环境
python -m venv venv
venv\Scripts\activate
```

## 🚀 快速开始

### 方法1: 使用批处理脚本（推荐）
```bash
# 双击运行或在命令行执行
build.bat
```

### 方法2: 使用Python脚本
```bash
# 安装依赖
pip install -r requirements_build.txt

# 运行打包脚本
python build_exe.py
```

### 方法3: 手动打包
```bash
# 安装PyInstaller
pip install PyInstaller

# 使用spec文件打包
pyinstaller --clean --noconfirm wms_client.spec
```

## 📦 打包选项

### 快速打包（推荐）
- 仅包含核心功能
- 打包体积较小（约100-200MB）
- 适合大多数使用场景
- 使用 `requirements_build.txt`

### 完整打包
- 包含所有功能（人脸识别、高级OCR等）
- 打包体积较大（约500MB-1GB）
- 功能最完整
- 使用 `requirements.txt`

## 📁 文件结构

```
项目根目录/
├── main.py                 # 主程序入口
├── wms_client.spec         # PyInstaller配置文件
├── version_info.txt        # 版本信息文件
├── build_exe.py           # 打包脚本
├── build.bat              # 批处理打包脚本
├── requirements.txt        # 完整依赖列表
├── requirements_build.txt  # 核心依赖列表
├── src/                   # 源代码目录
├── data/                  # 数据文件目录
├── logs/                  # 日志目录
├── config.ini             # 配置文件
└── dist/                  # 打包输出目录（生成）
    └── WMS客户端系统/      # 可执行文件目录
        ├── WMS客户端系统.exe
        ├── 启动WMS系统.bat
        └── ...其他文件
```

## 🔍 常见问题

### 1. 依赖安装失败
```bash
# 使用国内镜像源
pip install -r requirements_build.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者升级pip
python -m pip install --upgrade pip
```

### 2. 打包体积过大
- 使用快速打包模式
- 检查并移除不必要的依赖
- 使用UPX压缩（已在spec文件中启用）

### 3. 缺少DLL文件
```bash
# 安装Visual C++ Redistributable
# 下载地址: https://aka.ms/vs/17/release/vc_redist.x64.exe
```

### 4. 人脸识别功能打包失败
```bash
# 安装Visual Studio Build Tools
# 或者在spec文件中排除人脸识别相关模块
```

### 5. OCR功能异常
```bash
# 确保Tesseract OCR已安装
# 下载地址: https://github.com/UB-Mannheim/tesseract/wiki
```

## 🎯 打包优化建议

### 减少体积
1. 使用 `requirements_build.txt` 而不是 `requirements.txt`
2. 在spec文件中排除不需要的模块
3. 启用UPX压缩
4. 移除测试和开发工具

### 提高性能
1. 使用 `--onefile` 选项创建单文件版本
2. 启用代码优化选项
3. 预编译Python字节码

### 兼容性
1. 在目标系统相似的环境中打包
2. 包含必要的运行时库
3. 测试不同Windows版本的兼容性

## 📋 打包检查清单

- [ ] Python环境正确安装
- [ ] 所有依赖包已安装
- [ ] 项目代码无语法错误
- [ ] 配置文件存在且正确
- [ ] 数据库文件存在
- [ ] 资源文件完整
- [ ] 打包配置正确
- [ ] 目标系统测试通过

## 🚀 部署说明

### 单机部署
1. 将 `dist/WMS客户端系统/` 目录复制到目标机器
2. 双击 `启动WMS系统.bat` 或直接运行 `WMS客户端系统.exe`
3. 首次运行会自动初始化数据库

### 批量部署
1. 创建安装包（使用NSIS、Inno Setup等）
2. 或者创建zip压缩包分发
3. 编写自动化部署脚本

## 📞 技术支持

如果在打包过程中遇到问题，请：

1. 检查Python和依赖版本
2. 查看错误日志
3. 参考PyInstaller官方文档
4. 联系技术支持团队

---

© 2024 贵州睿云慧通科技有限公司 版权所有
