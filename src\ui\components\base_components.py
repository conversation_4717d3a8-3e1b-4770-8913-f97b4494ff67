"""
WMS客户端系统 - 基础UI组件
企业风格：正式、简约、大气
"""

from PyQt6.QtWidgets import (
    QPushButton, QLabel, QLineEdit, QTextEdit, QComboBox,
    QFrame, QVBoxLayout, QHBoxLayout, QGridLayout,
    QWidget, QScrollArea, QGroupBox, QTableWidget,
    QHeaderView, QAbstractItemView
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QFont, QPalette, QIcon, QPixmap
from typing import Optional, List, Dict, Any

from ..styles.theme import theme


class BaseButton(QPushButton):
    """基础按钮组件"""
    
    def __init__(self, text: str = "", variant: str = "primary", 
                 icon: Optional[QIcon] = None, parent: Optional[QWidget] = None):
        super().__init__(text, parent)
        self.variant = variant
        
        if icon:
            self.setIcon(icon)
            self.setIconSize(QSize(16, 16))
        
        self._setup_style()
    
    def _setup_style(self):
        """设置按钮样式"""
        stylesheet = theme.generate_button_stylesheet(self.variant)
        self.setStyleSheet(stylesheet)
        
        # 设置最小尺寸
        self.setMinimumHeight(40)
        self.setMinimumWidth(80)


class PrimaryButton(BaseButton):
    """主要按钮"""
    def __init__(self, text: str = "", icon: Optional[QIcon] = None, 
                 parent: Optional[QWidget] = None):
        super().__init__(text, "primary", icon, parent)


class SecondaryButton(BaseButton):
    """次要按钮"""
    def __init__(self, text: str = "", icon: Optional[QIcon] = None, 
                 parent: Optional[QWidget] = None):
        super().__init__(text, "secondary", icon, parent)


class SuccessButton(BaseButton):
    """成功按钮"""
    def __init__(self, text: str = "", icon: Optional[QIcon] = None, 
                 parent: Optional[QWidget] = None):
        super().__init__(text, "success", icon, parent)


class WarningButton(BaseButton):
    """警告按钮"""
    def __init__(self, text: str = "", icon: Optional[QIcon] = None, 
                 parent: Optional[QWidget] = None):
        super().__init__(text, "warning", icon, parent)


class ErrorButton(BaseButton):
    """错误按钮"""
    def __init__(self, text: str = "", icon: Optional[QIcon] = None, 
                 parent: Optional[QWidget] = None):
        super().__init__(text, "error", icon, parent)


class BaseLabel(QLabel):
    """基础标签组件"""
    
    def __init__(self, text: str = "", variant: str = "body", 
                 parent: Optional[QWidget] = None):
        super().__init__(text, parent)
        self.variant = variant
        self._setup_style()
    
    def _setup_style(self):
        """设置标签样式"""
        style = theme.get_text_style(self.variant)

        font = QFont(style['font-family'].split(',')[0])
        font.setPixelSize(int(style['font-size'].replace('px', '')))
        if 'font-weight' in style:
            font.setWeight(style['font-weight'])

        self.setFont(font)
        self.setStyleSheet(f"color: {style['color']};")


class TitleLabel(BaseLabel):
    """标题标签"""
    def __init__(self, text: str = "", level: int = 1, 
                 parent: Optional[QWidget] = None):
        variant = f"h{level}" if 1 <= level <= 4 else "h1"
        super().__init__(text, variant, parent)


class BodyLabel(BaseLabel):
    """正文标签"""
    def __init__(self, text: str = "", size: str = "normal", 
                 parent: Optional[QWidget] = None):
        variant_map = {
            "large": "body-large",
            "normal": "body",
            "small": "body-small"
        }
        variant = variant_map.get(size, "body")
        super().__init__(text, variant, parent)


class CaptionLabel(BaseLabel):
    """说明文字标签"""
    def __init__(self, text: str = "", parent: Optional[QWidget] = None):
        super().__init__(text, "caption", parent)


class BaseInput(QLineEdit):
    """基础输入框组件"""
    
    def __init__(self, placeholder: str = "", parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        if placeholder:
            self.setPlaceholderText(placeholder)
        
        self._setup_style()
    
    def _setup_style(self):
        """设置输入框样式"""
        stylesheet = theme.generate_input_stylesheet()
        self.setStyleSheet(stylesheet)
        
        # 设置最小高度
        self.setMinimumHeight(40)


class BaseTextArea(QTextEdit):
    """基础文本域组件"""
    
    def __init__(self, placeholder: str = "", parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        if placeholder:
            self.setPlaceholderText(placeholder)
        
        self._setup_style()
    
    def _setup_style(self):
        """设置文本域样式"""
        stylesheet = theme.generate_input_stylesheet()
        self.setStyleSheet(stylesheet)
        
        # 设置最小高度
        self.setMinimumHeight(80)


class BaseComboBox(QComboBox):
    """基础下拉框组件"""
    
    def __init__(self, items: Optional[List[str]] = None, 
                 parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        if items:
            self.addItems(items)
        
        self._setup_style()
    
    def _setup_style(self):
        """设置下拉框样式"""
        stylesheet = theme.generate_input_stylesheet()
        self.setStyleSheet(stylesheet)
        
        # 设置最小高度
        self.setMinimumHeight(40)


class BaseCard(QFrame):
    """基础卡片组件"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self._setup_style()
        self._setup_layout()
    
    def _setup_style(self):
        """设置卡片样式 - 优化外观与登录窗口呼应"""
        stylesheet = """
        QFrame {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.95),
                stop:1 rgba(248, 250, 252, 0.9));
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 16px;
            padding: 24px;
        }
        QFrame:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 1.0),
                stop:1 rgba(239, 246, 255, 0.95));
            border: 2px solid rgba(59, 130, 246, 0.4);
        }
        """
        self.setStyleSheet(stylesheet)

        # 设置框架样式
        self.setFrameStyle(QFrame.Shape.Box)
    
    def _setup_layout(self):
        """设置布局"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(
            theme.spacing.LG, theme.spacing.LG,
            theme.spacing.LG, theme.spacing.LG
        )
        self.layout.setSpacing(theme.spacing.MD)
    
    def add_widget(self, widget: QWidget):
        """添加组件"""
        self.layout.addWidget(widget)
    
    def add_layout(self, layout):
        """添加布局"""
        self.layout.addLayout(layout)


class BaseTable(QTableWidget):
    """基础表格组件"""
    
    def __init__(self, rows: int = 0, columns: int = 0, 
                 parent: Optional[QWidget] = None):
        super().__init__(rows, columns, parent)
        self._setup_style()
        self._setup_behavior()
    
    def _setup_style(self):
        """设置表格样式"""
        stylesheet = f"""
        QTableWidget {{
            background-color: {theme.colors.WHITE};
            border: 1px solid {theme.colors.BORDER};
            border-radius: {theme.border_radius.MD}px;
            gridline-color: {theme.colors.BORDER};
            font-size: {theme.typography.BODY_SIZE}px;
        }}
        
        QTableWidget::item {{
            padding: {theme.spacing.SM}px;
            border-bottom: 1px solid {theme.colors.BORDER};
        }}
        
        QTableWidget::item:selected {{
            background-color: {theme.colors.SECONDARY};
            color: {theme.colors.WHITE};
        }}
        
        QHeaderView::section {{
            background-color: {theme.colors.BACKGROUND};
            color: {theme.colors.TEXT_PRIMARY};
            padding: {theme.spacing.MD}px;
            border: none;
            border-bottom: 2px solid {theme.colors.PRIMARY};
            font-weight: {theme.typography.WEIGHT_MEDIUM};
        }}
        """
        self.setStyleSheet(stylesheet)
    
    def _setup_behavior(self):
        """设置表格行为"""
        # 设置选择行为
        self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        
        # 设置表头
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        # 隐藏垂直表头
        self.verticalHeader().setVisible(False)
        
        # 设置交替行颜色
        self.setAlternatingRowColors(True)


class FormGroup(QWidget):
    """表单组组件"""
    
    def __init__(self, label: str = "", parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.label_text = label
        self._setup_layout()
    
    def _setup_layout(self):
        """设置布局"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(theme.spacing.SM)
        
        if self.label_text:
            self.label = BodyLabel(self.label_text)
            self.label.setStyleSheet(f"font-weight: {theme.typography.WEIGHT_MEDIUM};")
            self.layout.addWidget(self.label)
    
    def add_widget(self, widget: QWidget):
        """添加组件"""
        self.layout.addWidget(widget)


class ActionBar(QWidget):
    """操作栏组件"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self._setup_layout()
        self._setup_style()
    
    def _setup_layout(self):
        """设置布局"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(theme.spacing.MD)
        
        # 添加弹性空间
        self.layout.addStretch()
    
    def _setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
        QWidget {{
            background-color: {theme.colors.WHITE};
            border-top: 1px solid {theme.colors.BORDER};
            padding: {theme.spacing.MD}px 0;
        }}
        """)
    
    def add_button(self, button: QPushButton):
        """添加按钮"""
        self.layout.addWidget(button)
    
    def add_stretch(self):
        """添加弹性空间"""
        self.layout.addStretch()


class StatusBar(QWidget):
    """状态栏组件"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self._setup_layout()
        self._setup_style()
    
    def _setup_layout(self):
        """设置布局"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(
            theme.spacing.MD, theme.spacing.SM,
            theme.spacing.MD, theme.spacing.SM
        )
        
        self.status_label = CaptionLabel("就绪")
        self.layout.addWidget(self.status_label)
        self.layout.addStretch()
    
    def _setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
        QWidget {{
            background-color: {theme.colors.BACKGROUND};
            border-top: 1px solid {theme.colors.BORDER};
        }}
        """)
        
        self.setFixedHeight(32)
    
    def set_status(self, message: str):
        """设置状态消息"""
        self.status_label.setText(message)


class UnifiedToolbar(QWidget):
    """统一的工具栏组件 - 与智能查询页面样式一致"""

    def __init__(self, title: str, icon: str = "", parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.title = title
        self.icon = icon
        self._setup_ui()

    def _setup_ui(self):
        """设置工具栏UI"""
        self.setFixedHeight(60)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.95);
                border-bottom: 1px solid #e2e8f0;
                border-radius: 12px;
            }
        """)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)

        # 返回按钮
        self.back_btn = QPushButton("← 返回主界面")
        self.back_btn.setFixedSize(120, 40)
        self.back_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6, stop:1 #2563eb);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563eb, stop:1 #1d4ed8);
            }
        """)
        layout.addWidget(self.back_btn)

        # 标题
        title_text = f"{self.icon} {self.title}" if self.icon else self.title
        self.title_label = QLabel(title_text)
        self.title_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                font-size: 24px;
                font-weight: 700;
                font-family: "Microsoft YaHei UI", sans-serif;
                background-color: transparent;
                margin-left: 20px;
            }
        """)
        layout.addWidget(self.title_label)

        layout.addStretch()

        # 导出按钮（可选）
        self.export_btn = QPushButton("📊 导出数据")
        self.export_btn.setFixedSize(120, 40)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #10b981, stop:1 #059669);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #059669, stop:1 #047857);
            }
        """)
        layout.addWidget(self.export_btn)

    def hide_export_button(self):
        """隐藏导出按钮"""
        self.export_btn.hide()

    def show_export_button(self):
        """显示导出按钮"""
        self.export_btn.show()
