"""
自定义异常类
© 2024 贵州睿云慧通科技有限公司
"""

class WMSException(Exception):
    """WMS系统基础异常类"""
    
    def __init__(self, message: str, code: str = None, details: dict = None):
        self.message = message
        self.code = code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "error": self.code,
            "message": self.message,
            "details": self.details
        }

class ValidationError(WMSException):
    """数据验证异常"""
    pass

class AuthenticationError(WMSException):
    """认证异常"""
    pass

class PermissionError(WMSException):
    """权限异常"""
    pass

class BusinessError(WMSException):
    """业务逻辑异常"""
    pass

class DatabaseError(WMSException):
    """数据库异常"""
    pass

class DeviceError(WMSException):
    """设备异常"""
    pass

class ConfigurationError(WMSException):
    """配置异常"""
    pass

class NetworkError(WMSException):
    """网络异常"""
    pass
