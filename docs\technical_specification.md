# 库房自助出入库客户端系统 - 技术规格说明

## 1. 系统架构详细设计

### 1.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    客户端应用程序                              │
├─────────────────────────────────────────────────────────────┤
│  表示层 (Presentation Layer)                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  登录界面    │ │  主控界面    │ │  入库界面    │ │ 出库界面 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  查询界面    │ │  设置界面    │ │  报表界面    │ │ 帮助界面 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Business Logic Layer)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 用户管理服务 │ │ 入库管理服务 │ │ 出库管理服务 │ │库存管理服务│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 系统管理服务 │ │ 日志管理服务 │ │ 报表管理服务 │ │设备管理服务│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│  数据访问层 (Data Access Layer)                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   用户DAO    │ │  产品DAO     │ │  库存DAO     │ │ 订单DAO  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│  数据持久层 (Data Persistence Layer)                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              SQLite / PostgreSQL 数据库                 │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 核心模块设计

#### 1.2.1 用户管理模块 (UserManager)
```python
class UserManager:
    - authenticate(username, password) -> bool
    - login_with_rfid(card_id) -> User
    - login_with_qr(qr_code) -> User
    - get_user_permissions(user_id) -> List[Permission]
    - update_user_info(user_id, info) -> bool
    - change_password(user_id, old_pwd, new_pwd) -> bool
```

#### 1.2.2 入库管理模块 (InboundManager)
```python
class InboundManager:
    - create_inbound_order(products) -> InboundOrder
    - scan_product(barcode) -> Product
    - validate_product(product) -> bool
    - allocate_location(product) -> Location
    - confirm_inbound(order_id) -> bool
    - generate_inbound_receipt(order_id) -> Document
```

#### 1.2.3 出库管理模块 (OutboundManager)
```python
class OutboundManager:
    - create_outbound_order(products) -> OutboundOrder
    - check_inventory(product_id, quantity) -> bool
    - reserve_inventory(product_id, quantity) -> bool
    - confirm_outbound(order_id) -> bool
    - generate_outbound_receipt(order_id) -> Document
```

#### 1.2.4 库存管理模块 (InventoryManager)
```python
class InventoryManager:
    - get_current_stock(product_id) -> int
    - update_stock(product_id, quantity, operation) -> bool
    - get_stock_by_location(location_id) -> List[Stock]
    - perform_stocktake() -> StocktakeResult
    - generate_stock_report() -> Report
```

## 2. 数据库详细设计

### 2.1 数据表结构

#### 2.1.1 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    full_name VARCHAR(100),
    role VARCHAR(20) DEFAULT 'operator',
    rfid_card VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.1.2 产品表 (products)
```sql
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sku VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INTEGER,
    unit VARCHAR(20),
    weight DECIMAL(10,3),
    dimensions VARCHAR(50),
    barcode VARCHAR(100),
    min_stock_level INTEGER DEFAULT 0,
    max_stock_level INTEGER DEFAULT 1000,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);
```

#### 2.1.3 库存表 (inventory)
```sql
CREATE TABLE inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    location_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 0,
    reserved_quantity INTEGER DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (location_id) REFERENCES locations(id),
    UNIQUE(product_id, location_id)
);
```

#### 2.1.4 入库单表 (inbound_orders)
```sql
CREATE TABLE inbound_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    supplier_id INTEGER,
    status VARCHAR(20) DEFAULT 'pending',
    total_items INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
);
```

#### 2.1.5 出库单表 (outbound_orders)
```sql
CREATE TABLE outbound_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    customer_id INTEGER,
    status VARCHAR(20) DEFAULT 'pending',
    total_items INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);
```

### 2.2 索引设计
```sql
-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_rfid_card ON users(rfid_card);

-- 产品表索引
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_barcode ON products(barcode);
CREATE INDEX idx_products_category ON products(category_id);

-- 库存表索引
CREATE INDEX idx_inventory_product ON inventory(product_id);
CREATE INDEX idx_inventory_location ON inventory(location_id);

-- 订单表索引
CREATE INDEX idx_inbound_orders_number ON inbound_orders(order_number);
CREATE INDEX idx_inbound_orders_user ON inbound_orders(user_id);
CREATE INDEX idx_outbound_orders_number ON outbound_orders(order_number);
CREATE INDEX idx_outbound_orders_user ON outbound_orders(user_id);
```

## 3. 接口设计

### 3.1 硬件设备接口

#### 3.1.1 扫码枪接口
```python
class BarcodeScanner:
    def __init__(self, port: str):
        self.port = port
        self.is_connected = False
    
    def connect(self) -> bool:
        """连接扫码枪"""
        pass
    
    def scan(self) -> str:
        """扫描条码"""
        pass
    
    def disconnect(self):
        """断开连接"""
        pass
```

#### 3.1.2 RFID读卡器接口
```python
class RFIDReader:
    def __init__(self, port: str):
        self.port = port
        self.is_connected = False
    
    def connect(self) -> bool:
        """连接RFID读卡器"""
        pass
    
    def read_card(self) -> str:
        """读取卡片ID"""
        pass
    
    def disconnect(self):
        """断开连接"""
        pass
```

### 3.2 配置管理接口
```python
class ConfigManager:
    def __init__(self, config_file: str):
        self.config_file = config_file
        self.config = {}
    
    def load_config(self) -> dict:
        """加载配置文件"""
        pass
    
    def save_config(self, config: dict) -> bool:
        """保存配置文件"""
        pass
    
    def get_value(self, section: str, key: str) -> str:
        """获取配置值"""
        pass
    
    def set_value(self, section: str, key: str, value: str) -> bool:
        """设置配置值"""
        pass
```

## 4. 安全设计

### 4.1 身份认证
- 密码加密存储（使用bcrypt）
- 会话管理
- 自动登出机制
- 多重身份验证支持

### 4.2 权限控制
- 基于角色的访问控制（RBAC）
- 功能级权限控制
- 数据级权限控制

### 4.3 数据安全
- 数据库连接加密
- 敏感数据加密存储
- 操作日志记录
- 数据备份机制

## 5. 性能优化

### 5.1 数据库优化
- 合理的索引设计
- 查询语句优化
- 连接池管理
- 分页查询

### 5.2 界面优化
- 异步操作处理
- 进度条显示
- 缓存机制
- 懒加载

### 5.3 内存管理
- 对象池模式
- 及时释放资源
- 内存泄漏检测

## 6. 错误处理

### 6.1 异常分类
- 系统异常
- 业务异常
- 硬件异常
- 网络异常

### 6.2 错误处理策略
- 统一异常处理
- 错误日志记录
- 用户友好提示
- 自动恢复机制

## 7. 日志管理

### 7.1 日志级别
- DEBUG: 调试信息
- INFO: 一般信息
- WARNING: 警告信息
- ERROR: 错误信息
- CRITICAL: 严重错误

### 7.2 日志内容
- 用户操作日志
- 系统运行日志
- 错误异常日志
- 性能监控日志

## 8. 部署要求

### 8.1 硬件要求
- CPU: Intel i3 或同等性能
- 内存: 4GB RAM
- 存储: 100GB 可用空间
- 显示器: 1024x768 分辨率

### 8.2 软件要求
- 操作系统: Windows 10/11
- Python: 3.9+
- 数据库: SQLite 3.x / PostgreSQL 12+
- 其他依赖: 详见requirements.txt

## 9. 智能化增强功能设计

### 9.1 身份证核验模块
```python
class IdentityManager:
    def read_id_card(self) -> dict:
        """读取身份证信息"""
        pass

    def verify_id_card(self, id_info: dict) -> bool:
        """验证身份证真伪"""
        pass

    def bind_user_identity(self, user_id: int, id_info: dict) -> bool:
        """绑定用户身份信息"""
        pass
```

### 9.2 人脸识别模块
```python
class FaceManager:
    def detect_face(self, image) -> List[dict]:
        """检测人脸"""
        pass

    def recognize_face(self, image) -> dict:
        """识别人脸"""
        pass

    def register_face(self, user_id: int, images: List) -> bool:
        """注册人脸"""
        pass

    def verify_liveness(self, image) -> bool:
        """活体检测"""
        pass
```

### 9.3 打印管理模块
```python
class PrintManager:
    def print_inbound_receipt(self, order_id: int) -> bool:
        """打印入库小票"""
        pass

    def print_outbound_receipt(self, order_id: int) -> bool:
        """打印出库小票"""
        pass

    def print_inventory_report(self, query_params: dict) -> bool:
        """打印库存报告"""
        pass
```

### 9.4 票据识别模块
```python
class TicketManager:
    def scan_qr_code(self, image) -> str:
        """扫描二维码"""
        pass

    def scan_barcode(self, image) -> str:
        """扫描条形码"""
        pass

    def extract_text_ocr(self, image) -> dict:
        """OCR文字识别"""
        pass

    def verify_ticket(self, ticket_info: dict) -> bool:
        """验证票据"""
        pass
```

### 9.5 新增数据表设计

#### 9.5.1 身份证信息表
```sql
CREATE TABLE identity_cards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    id_number VARCHAR(18) UNIQUE NOT NULL,
    name VARCHAR(50) NOT NULL,
    gender VARCHAR(2),
    birth_date DATE,
    address TEXT,
    issuing_authority VARCHAR(100),
    valid_from DATE,
    valid_to DATE,
    photo_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 9.5.2 人脸记录表
```sql
CREATE TABLE face_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    face_encoding TEXT NOT NULL,
    face_image_path VARCHAR(255),
    quality_score FLOAT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 9.5.3 票据信息表
```sql
CREATE TABLE tickets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_number VARCHAR(100) UNIQUE NOT NULL,
    ticket_type VARCHAR(50),
    qr_code_data TEXT,
    barcode_data VARCHAR(100),
    ocr_text TEXT,
    extracted_info JSON,
    image_path VARCHAR(255),
    status VARCHAR(20) DEFAULT 'valid',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP
);
```

### 9.6 硬件设备接口扩展

#### 9.6.1 身份证读卡器接口
```python
class IDCardReader:
    def __init__(self, port: str):
        self.port = port
        self.is_connected = False

    def connect(self) -> bool:
        """连接身份证读卡器"""
        pass

    def read_card(self) -> dict:
        """读取身份证信息"""
        pass

    def disconnect(self):
        """断开连接"""
        pass
```

#### 9.6.2 人脸识别摄像头接口
```python
class FaceCamera:
    def __init__(self, camera_id: int = 0):
        self.camera_id = camera_id
        self.is_opened = False

    def open_camera(self) -> bool:
        """打开摄像头"""
        pass

    def capture_frame(self) -> np.ndarray:
        """捕获帧"""
        pass

    def close_camera(self):
        """关闭摄像头"""
        pass
```

#### 9.6.3 热敏打印机接口
```python
class ThermalPrinter:
    def __init__(self, port: str):
        self.port = port
        self.is_connected = False

    def connect(self) -> bool:
        """连接打印机"""
        pass

    def print_text(self, text: str) -> bool:
        """打印文本"""
        pass

    def print_image(self, image_path: str) -> bool:
        """打印图片"""
        pass

    def cut_paper(self) -> bool:
        """切纸"""
        pass
```

### 9.7 技术依赖更新
```python
# 新增依赖包
opencv-python==4.8.1.78      # 计算机视觉
face-recognition==1.3.0      # 人脸识别
dlib==19.24.2                # 机器学习库
pytesseract==0.3.10          # OCR引擎
paddleocr==2.7.0.3           # 百度OCR
pyzbar==0.1.9                # 条码识别
qrcode==7.4.2                # 二维码生成
pyscard==2.0.7               # 智能卡读取
pycryptodome==3.19.0         # 加密解密
python-escpos==3.0a9         # ESC/POS打印
```
