# WMS系统安全功能指南

## 🔐 概述

WMS库房自助出入库客户端系统现已集成完整的安全功能，包括密码加盐、授权控制和用户认证管理。

## 🛡️ 安全功能特性

### 1. 密码加盐功能
- **PBKDF2算法**: 使用PBKDF2-HMAC-SHA256进行密码哈希
- **随机盐值**: 每个密码使用32字节随机盐值
- **高迭代次数**: 100,000次迭代，防止暴力破解
- **安全比较**: 使用时间常数比较防止时序攻击

### 2. 授权控制功能
- **机器绑定**: 授权与特定机器硬件ID绑定
- **时间限制**: 支持有效期控制，自动过期
- **功能权限**: 细粒度功能权限控制
- **数字签名**: 防止授权文件被篡改

### 3. 用户认证管理
- **多角色支持**: 管理员、经理、操作员、访客等角色
- **权限管理**: 基于角色的权限控制系统
- **会话管理**: 安全的会话令牌和超时控制
- **登录保护**: 防暴力破解，账户锁定机制

## 📋 授权类型

### 试用版 (Trial)
- **用户限制**: 最多2个用户
- **商品限制**: 最多100个商品
- **功能限制**: 基础库存管理、条码扫描
- **有效期**: 30天
- **价格**: 免费

### 标准版 (Standard)
- **用户限制**: 最多10个用户
- **商品限制**: 最多1,000个商品
- **功能包含**: 高级报表、人脸识别
- **有效期**: 1年
- **适用**: 中小企业

### 专业版 (Professional)
- **用户限制**: 最多50个用户
- **商品限制**: 最多10,000个商品
- **功能包含**: API访问、多仓库管理
- **有效期**: 1年
- **适用**: 大型企业

### 企业版 (Enterprise)
- **用户限制**: 无限制
- **商品限制**: 无限制
- **功能包含**: 所有功能
- **有效期**: 自定义
- **适用**: 集团企业

## 🔑 用户角色权限

### 管理员 (Admin)
- ✅ 系统管理
- ✅ 用户管理
- ✅ 库存管理
- ✅ 报表访问
- ✅ 设置访问
- ✅ 备份恢复
- ✅ 授权管理

### 经理 (Manager)
- ❌ 系统管理
- ✅ 用户管理
- ✅ 库存管理
- ✅ 报表访问
- ✅ 设置访问
- ❌ 备份恢复
- ❌ 授权管理

### 操作员 (Operator)
- ❌ 系统管理
- ❌ 用户管理
- ✅ 库存管理
- ❌ 报表访问
- ❌ 设置访问
- ❌ 备份恢复
- ❌ 授权管理

### 访客 (Viewer)
- ❌ 系统管理
- ❌ 用户管理
- ❌ 库存管理
- ✅ 报表访问
- ❌ 设置访问
- ❌ 备份恢复
- ❌ 授权管理

## 🚀 使用指南

### 首次启动
1. 系统会自动创建默认管理员账户
   - 用户名: `admin`
   - 密码: `admin123`
2. 首次登录后建议立即修改密码
3. 如无授权，系统会提示申请试用授权

### 申请试用授权
1. 点击登录界面的"🔐 授权"按钮
2. 切换到"🎯 试用授权"选项卡
3. 点击"申请30天试用"按钮
4. 系统自动生成并激活试用授权

### 激活正式授权
1. 获取正式授权密钥
2. 点击"🔐 授权"按钮打开授权管理
3. 切换到"🔑 激活授权"选项卡
4. 粘贴授权密钥并点击"激活授权"

### 用户管理
1. 使用管理员账户登录
2. 进入系统管理界面
3. 可以创建、修改、删除用户
4. 分配不同角色和权限

## 🔧 技术实现

### 密码安全
```python
# 密码哈希示例
password_hash, salt = password_manager.hash_password("user_password")

# 密码验证示例
is_valid = password_manager.verify_password("user_password", password_hash, salt)
```

### 授权验证
```python
# 检查授权状态
is_valid, license_info = license_manager.validate_license()

# 检查功能权限
has_permission = license_manager.check_feature_permission("advanced_reports")
```

### 用户认证
```python
# 用户登录
is_valid, result = auth_manager.authenticate_user("username", "password")

# 创建用户
success, message = auth_manager.create_user(
    "newuser", "password", "operator", "新用户"
)
```

## 📁 文件结构

```
data/
├── users.json          # 用户数据（加密存储）
├── sessions.json       # 会话数据
├── license.dat         # 授权文件（加密）
└── wms.db             # 主数据库

src/security/
├── __init__.py
├── password_manager.py # 密码管理器
├── license_manager.py  # 授权管理器
└── auth_manager.py     # 认证管理器
```

## 🛠️ 配置选项

### 密码策略
- 最小长度: 6位
- 最大长度: 50位
- 必须包含字母或数字
- 支持特殊字符

### 会话管理
- 会话超时: 8小时
- 自动续期: 支持
- 并发登录: 允许

### 安全设置
- 登录失败锁定: 5次失败锁定30分钟
- 密码哈希迭代: 100,000次
- 盐值长度: 32字节

## 🚨 安全建议

### 密码安全
1. 使用强密码，包含大小写字母、数字和特殊字符
2. 定期更换密码
3. 不要使用默认密码
4. 不要共享账户

### 系统安全
1. 定期备份数据
2. 及时更新系统
3. 监控登录日志
4. 限制网络访问

### 授权管理
1. 购买正版授权
2. 不要分享授权密钥
3. 定期检查授权状态
4. 及时续费避免中断

## 📞 技术支持

### 联系方式
- **公司**: 贵州睿云慧通科技有限公司
- **邮箱**: <EMAIL>
- **电话**: 400-xxx-xxxx

### 常见问题
1. **忘记密码**: 联系管理员重置
2. **授权过期**: 联系销售续费
3. **功能受限**: 检查授权类型
4. **登录失败**: 检查用户名密码

## 📝 更新日志

### v1.0.0 (2025-07-31)
- ✅ 新增密码加盐功能
- ✅ 新增授权控制系统
- ✅ 新增用户认证管理
- ✅ 新增角色权限控制
- ✅ 新增授权管理界面
- ✅ 新增安全测试工具

---

© 2024 贵州睿云慧通科技有限公司 版权所有
