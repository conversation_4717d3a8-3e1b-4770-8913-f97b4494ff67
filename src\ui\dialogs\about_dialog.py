"""
关于对话框 - 显示版权信息和软件信息
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QTabWidget, QWidget, QScrollArea
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QPixmap, QIcon

from ..components.base_components import TitleLabel, BodyLabel, PrimaryButton, SecondaryButton
from ..styles.theme import theme
from config.copyright import get_copyright_info, ABOUT_COPYRIGHT, LICENSE_INFO


class AboutDialog(QDialog):
    """关于对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.copyright_info = get_copyright_info()
        self._setup_ui()
        self._setup_style()
    
    def _setup_ui(self):
        """设置UI"""
        self.setWindowTitle("关于 WMS系统")
        self.setFixedSize(600, 500)
        self.setWindowModality(Qt.WindowModality.ApplicationModal)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(theme.spacing.LG)
        layout.setContentsMargins(theme.spacing.XL, theme.spacing.XL,
                                theme.spacing.XL, theme.spacing.XL)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 关于选项卡
        about_tab = self._create_about_tab()
        tab_widget.addTab(about_tab, "关于")
        
        # 版权选项卡
        copyright_tab = self._create_copyright_tab()
        tab_widget.addTab(copyright_tab, "版权信息")
        
        # 许可证选项卡
        license_tab = self._create_license_tab()
        tab_widget.addTab(license_tab, "许可证")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = PrimaryButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def _create_about_tab(self):
        """创建关于选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(theme.spacing.LG)
        
        # 产品图标和名称
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 产品图标（如果有的话）
        icon_label = QLabel("🏭")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("font-size: 64px;")
        header_layout.addWidget(icon_label)
        
        # 产品名称
        product_name = TitleLabel(self.copyright_info["product_name"], level=1)
        product_name.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(product_name)
        
        # 版本信息
        version_label = BodyLabel(f"版本 {self.copyright_info['product_version']}")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(version_label)
        
        layout.addLayout(header_layout)
        
        # 产品描述
        description = BodyLabel("智能仓储管理系统\n高效 · 智能 · 安全")
        description.setAlignment(Qt.AlignmentFlag.AlignCenter)
        description.setWordWrap(True)
        layout.addWidget(description)
        
        # 公司信息
        company_layout = QVBoxLayout()
        company_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        company_name = BodyLabel(f"开发商：{self.copyright_info['company_name']}")
        company_name.setAlignment(Qt.AlignmentFlag.AlignCenter)
        company_layout.addWidget(company_name)
        
        website = BodyLabel(f"网站：{self.copyright_info['company_domain']}")
        website.setAlignment(Qt.AlignmentFlag.AlignCenter)
        company_layout.addWidget(website)
        
        layout.addLayout(company_layout)
        
        # 版权声明
        copyright_text = BodyLabel(self.copyright_info["copyright_text"])
        copyright_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        copyright_text.setStyleSheet("font-weight: bold;")
        layout.addWidget(copyright_text)
        
        layout.addStretch()
        return widget
    
    def _create_copyright_tab(self):
        """创建版权信息选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 版权信息文本
        copyright_text = QTextEdit()
        copyright_text.setPlainText(ABOUT_COPYRIGHT)
        copyright_text.setReadOnly(True)
        copyright_text.setStyleSheet(f"""
        QTextEdit {{
            background-color: {theme.colors.BACKGROUND};
            border: 1px solid {theme.colors.BORDER};
            border-radius: {theme.border_radius.MD}px;
            padding: {theme.spacing.MD}px;
            font-family: 'Microsoft YaHei UI';
            font-size: 12px;
            line-height: 1.5;
        }}
        """)
        
        layout.addWidget(copyright_text)
        return widget
    
    def _create_license_tab(self):
        """创建许可证选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 许可证信息文本
        license_text = QTextEdit()
        license_text.setPlainText(LICENSE_INFO)
        license_text.setReadOnly(True)
        license_text.setStyleSheet(f"""
        QTextEdit {{
            background-color: {theme.colors.BACKGROUND};
            border: 1px solid {theme.colors.BORDER};
            border-radius: {theme.border_radius.MD}px;
            padding: {theme.spacing.MD}px;
            font-family: 'Microsoft YaHei UI';
            font-size: 12px;
            line-height: 1.5;
        }}
        """)
        
        layout.addWidget(license_text)
        return widget
    
    def _setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
        QDialog {{
            background-color: {theme.colors.WHITE};
        }}
        QTabWidget::pane {{
            border: 1px solid {theme.colors.BORDER};
            border-radius: {theme.border_radius.MD}px;
        }}
        QTabBar::tab {{
            background-color: {theme.colors.BACKGROUND};
            border: 1px solid {theme.colors.BORDER};
            padding: 8px 16px;
            margin-right: 2px;
        }}
        QTabBar::tab:selected {{
            background-color: {theme.colors.WHITE};
            border-bottom: 1px solid {theme.colors.WHITE};
        }}
        """)


def show_about_dialog(parent=None):
    """显示关于对话框"""
    dialog = AboutDialog(parent)
    return dialog.exec()


if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication([])
    app.setStyle("Fusion")
    
    dialog = AboutDialog()
    dialog.show()
    
    app.exec()
