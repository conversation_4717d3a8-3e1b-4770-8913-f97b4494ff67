"""
WMS密码管理器 - 加盐哈希密码处理
© 2024 贵州睿云慧通科技有限公司
"""

import hashlib
import secrets
import base64
import os
from typing import Tuple, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class PasswordManager:
    """密码管理器 - 提供安全的密码加盐哈希功能"""
    
    def __init__(self):
        self.salt_length = 32  # 盐值长度
        self.iterations = 100000  # PBKDF2迭代次数
        self.hash_algorithm = 'sha256'  # 哈希算法
    
    def generate_salt(self) -> str:
        """生成随机盐值"""
        salt = secrets.token_bytes(self.salt_length)
        return base64.b64encode(salt).decode('utf-8')
    
    def hash_password(self, password: str, salt: Optional[str] = None) -> <PERSON><PERSON>[str, str]:
        """
        对密码进行加盐哈希
        
        Args:
            password: 原始密码
            salt: 盐值（可选，不提供则自动生成）
            
        Returns:
            Tuple[str, str]: (哈希后的密码, 盐值)
        """
        if salt is None:
            salt = self.generate_salt()
        
        # 将盐值转换为字节
        salt_bytes = base64.b64decode(salt.encode('utf-8'))
        
        # 使用PBKDF2进行密码哈希
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt_bytes,
            iterations=self.iterations,
        )
        
        password_bytes = password.encode('utf-8')
        key = kdf.derive(password_bytes)
        
        # 将哈希结果编码为base64
        hashed_password = base64.b64encode(key).decode('utf-8')
        
        return hashed_password, salt
    
    def verify_password(self, password: str, hashed_password: str, salt: str) -> bool:
        """
        验证密码
        
        Args:
            password: 输入的密码
            hashed_password: 存储的哈希密码
            salt: 存储的盐值
            
        Returns:
            bool: 密码是否正确
        """
        try:
            # 使用相同的盐值对输入密码进行哈希
            new_hash, _ = self.hash_password(password, salt)
            
            # 使用安全的字符串比较
            return secrets.compare_digest(new_hash, hashed_password)
        except Exception:
            return False
    
    def generate_secure_key(self) -> str:
        """生成安全的加密密钥"""
        return base64.urlsafe_b64encode(Fernet.generate_key()).decode('utf-8')
    
    def encrypt_data(self, data: str, key: str) -> str:
        """
        加密数据
        
        Args:
            data: 要加密的数据
            key: 加密密钥
            
        Returns:
            str: 加密后的数据
        """
        try:
            key_bytes = base64.urlsafe_b64decode(key.encode('utf-8'))
            f = Fernet(key_bytes)
            encrypted_data = f.encrypt(data.encode('utf-8'))
            return base64.b64encode(encrypted_data).decode('utf-8')
        except Exception as e:
            raise ValueError(f"数据加密失败: {e}")
    
    def decrypt_data(self, encrypted_data: str, key: str) -> str:
        """
        解密数据
        
        Args:
            encrypted_data: 加密的数据
            key: 解密密钥
            
        Returns:
            str: 解密后的数据
        """
        try:
            key_bytes = base64.urlsafe_b64decode(key.encode('utf-8'))
            f = Fernet(key_bytes)
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = f.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
        except Exception as e:
            raise ValueError(f"数据解密失败: {e}")
    
    def generate_session_token(self) -> str:
        """生成会话令牌"""
        return secrets.token_urlsafe(32)
    
    def hash_machine_id(self) -> str:
        """生成机器标识哈希"""
        try:
            # 获取机器特征信息
            import platform
            import uuid
            
            machine_info = f"{platform.node()}-{uuid.getnode()}-{platform.machine()}"
            
            # 对机器信息进行哈希
            hash_obj = hashlib.sha256(machine_info.encode('utf-8'))
            return hash_obj.hexdigest()
        except Exception:
            # 如果获取机器信息失败，返回随机哈希
            return hashlib.sha256(secrets.token_bytes(32)).hexdigest()
    
    def create_admin_password(self, password: str = "admin123") -> Tuple[str, str]:
        """
        创建管理员密码
        
        Args:
            password: 管理员密码（默认admin123）
            
        Returns:
            Tuple[str, str]: (哈希密码, 盐值)
        """
        return self.hash_password(password)
    
    def validate_password_strength(self, password: str) -> Tuple[bool, str]:
        """
        验证密码强度
        
        Args:
            password: 要验证的密码
            
        Returns:
            Tuple[bool, str]: (是否符合要求, 提示信息)
        """
        if len(password) < 6:
            return False, "密码长度至少6位"
        
        if len(password) > 50:
            return False, "密码长度不能超过50位"
        
        # 检查是否包含特殊字符
        has_letter = any(c.isalpha() for c in password)
        has_digit = any(c.isdigit() for c in password)
        
        if not (has_letter or has_digit):
            return False, "密码应包含字母或数字"
        
        return True, "密码强度符合要求"


# 全局密码管理器实例
password_manager = PasswordManager()
