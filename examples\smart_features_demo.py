#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
库房自助出入库客户端系统 - 智能化功能演示脚本

本脚本演示了系统的四个主要智能化功能：
1. 身份证核验
2. 人脸识别
3. 小票打印
4. 票据扫码

作者: WMS开发团队
日期: 2024-01-15
版本: 1.0.0
"""

import cv2
import numpy as np
from typing import Dict, List, Optional
import json
import time
from datetime import datetime


class IdentityCardDemo:
    """身份证核验功能演示"""
    
    def __init__(self):
        self.reader_connected = False
    
    def connect_reader(self) -> bool:
        """连接身份证读卡器"""
        print("正在连接身份证读卡器...")
        # 模拟连接过程
        time.sleep(1)
        self.reader_connected = True
        print("✓ 身份证读卡器连接成功")
        return True
    
    def read_id_card(self) -> Dict:
        """读取身份证信息"""
        if not self.reader_connected:
            raise Exception("身份证读卡器未连接")
        
        print("请将身份证放在读卡器上...")
        time.sleep(2)  # 模拟读取过程
        
        # 模拟读取的身份证信息
        id_info = {
            "name": "张三",
            "id_number": "110101199001011234",
            "gender": "男",
            "birth_date": "1990-01-01",
            "address": "北京市东城区某某街道123号",
            "issuing_authority": "北京市公安局东城分局",
            "valid_from": "2020-01-01",
            "valid_to": "2030-01-01",
            "photo_path": "/data/id_photos/110101199001011234.jpg"
        }
        
        print("✓ 身份证信息读取成功:")
        for key, value in id_info.items():
            if key != "photo_path":
                print(f"  {key}: {value}")
        
        return id_info
    
    def verify_id_card(self, id_info: Dict) -> bool:
        """验证身份证真伪"""
        print("正在验证身份证真伪...")
        time.sleep(1)  # 模拟验证过程
        
        # 模拟验证逻辑
        if len(id_info["id_number"]) == 18:
            print("✓ 身份证验证通过")
            return True
        else:
            print("✗ 身份证验证失败")
            return False


class FaceRecognitionDemo:
    """人脸识别功能演示"""
    
    def __init__(self):
        self.camera = None
        self.face_database = {}  # 模拟人脸数据库
    
    def init_camera(self) -> bool:
        """初始化摄像头"""
        print("正在初始化摄像头...")
        try:
            self.camera = cv2.VideoCapture(0)
            if self.camera.isOpened():
                print("✓ 摄像头初始化成功")
                return True
            else:
                print("✗ 摄像头初始化失败")
                return False
        except Exception as e:
            print(f"✗ 摄像头初始化错误: {e}")
            return False
    
    def detect_face(self, image: np.ndarray) -> List[Dict]:
        """检测人脸"""
        print("正在检测人脸...")
        
        # 模拟人脸检测结果
        faces = [
            {
                "x": 100, "y": 100, "width": 200, "height": 200,
                "confidence": 0.95
            }
        ]
        
        if faces:
            print(f"✓ 检测到 {len(faces)} 张人脸")
        else:
            print("✗ 未检测到人脸")
        
        return faces
    
    def recognize_face(self, image: np.ndarray) -> Optional[Dict]:
        """识别人脸"""
        print("正在进行人脸识别...")
        time.sleep(1)  # 模拟识别过程
        
        # 模拟识别结果
        if self.face_database:
            user_info = {
                "user_id": 1,
                "name": "张三",
                "confidence": 0.92
            }
            print(f"✓ 人脸识别成功: {user_info['name']} (置信度: {user_info['confidence']:.2f})")
            return user_info
        else:
            print("✗ 人脸识别失败，用户不在数据库中")
            return None
    
    def register_face(self, user_id: int, name: str, images: List[np.ndarray]) -> bool:
        """注册人脸"""
        print(f"正在为用户 {name} 注册人脸...")
        time.sleep(2)  # 模拟注册过程
        
        # 模拟人脸特征提取和存储
        face_encoding = f"face_encoding_{user_id}_{int(time.time())}"
        self.face_database[user_id] = {
            "name": name,
            "encoding": face_encoding,
            "registered_at": datetime.now().isoformat()
        }
        
        print(f"✓ 人脸注册成功，用户ID: {user_id}")
        return True
    
    def verify_liveness(self, image: np.ndarray) -> bool:
        """活体检测"""
        print("正在进行活体检测...")
        time.sleep(1)  # 模拟检测过程
        
        # 模拟活体检测结果
        is_live = True  # 假设检测通过
        
        if is_live:
            print("✓ 活体检测通过")
        else:
            print("✗ 活体检测失败，请确保是真人操作")
        
        return is_live


class PrintManagerDemo:
    """打印管理功能演示"""
    
    def __init__(self):
        self.printer_connected = False
        self.print_templates = {
            "inbound": """
================================
        库房管理系统
        入库小票
================================
操作时间: {timestamp}
操作员: {operator}
订单号: {order_number}
--------------------------------
{items}
--------------------------------
总计数量: {total_quantity}
备注: {notes}
================================
            """,
            "outbound": """
================================
        库房管理系统
        出库小票
================================
操作时间: {timestamp}
操作员: {operator}
订单号: {order_number}
--------------------------------
{items}
--------------------------------
总计数量: {total_quantity}
备注: {notes}
================================
            """
        }
    
    def connect_printer(self) -> bool:
        """连接打印机"""
        print("正在连接热敏打印机...")
        time.sleep(1)
        self.printer_connected = True
        print("✓ 打印机连接成功")
        return True
    
    def print_receipt(self, receipt_type: str, data: Dict) -> bool:
        """打印小票"""
        if not self.printer_connected:
            raise Exception("打印机未连接")
        
        print(f"正在打印{receipt_type}小票...")
        
        # 格式化打印内容
        template = self.print_templates.get(receipt_type, "")
        if not template:
            print(f"✗ 未找到{receipt_type}模板")
            return False
        
        # 格式化商品列表
        items_text = ""
        for item in data.get("items", []):
            items_text += f"{item['name']}  数量:{item['quantity']}  单位:{item['unit']}\n"
        
        receipt_content = template.format(
            timestamp=data.get("timestamp", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            operator=data.get("operator", "系统管理员"),
            order_number=data.get("order_number", "WMS" + str(int(time.time()))),
            items=items_text.strip(),
            total_quantity=data.get("total_quantity", 0),
            notes=data.get("notes", "无")
        )
        
        print("打印内容预览:")
        print(receipt_content)
        
        # 模拟打印过程
        time.sleep(2)
        print("✓ 小票打印完成")
        return True


class TicketScannerDemo:
    """票据扫码功能演示"""
    
    def __init__(self):
        self.camera = None
    
    def init_scanner(self) -> bool:
        """初始化扫描设备"""
        print("正在初始化扫描设备...")
        try:
            self.camera = cv2.VideoCapture(0)
            if self.camera.isOpened():
                print("✓ 扫描设备初始化成功")
                return True
            else:
                print("✗ 扫描设备初始化失败")
                return False
        except Exception as e:
            print(f"✗ 扫描设备初始化错误: {e}")
            return False
    
    def scan_qr_code(self, image: np.ndarray) -> Optional[str]:
        """扫描二维码"""
        print("正在扫描二维码...")
        time.sleep(1)  # 模拟扫描过程
        
        # 模拟扫描结果
        qr_data = "WMS_TICKET_20240115_001"
        
        if qr_data:
            print(f"✓ 二维码扫描成功: {qr_data}")
            return qr_data
        else:
            print("✗ 未检测到二维码")
            return None
    
    def scan_barcode(self, image: np.ndarray) -> Optional[str]:
        """扫描条形码"""
        print("正在扫描条形码...")
        time.sleep(1)  # 模拟扫描过程
        
        # 模拟扫描结果
        barcode_data = "1234567890123"
        
        if barcode_data:
            print(f"✓ 条形码扫描成功: {barcode_data}")
            return barcode_data
        else:
            print("✗ 未检测到条形码")
            return None
    
    def extract_text_ocr(self, image: np.ndarray) -> Dict:
        """OCR文字识别"""
        print("正在进行OCR文字识别...")
        time.sleep(2)  # 模拟OCR过程
        
        # 模拟OCR结果
        ocr_result = {
            "text": "入库单\n日期: 2024-01-15\n供应商: ABC公司\n商品: 产品A x 10",
            "confidence": 0.89,
            "extracted_info": {
                "document_type": "入库单",
                "date": "2024-01-15",
                "supplier": "ABC公司",
                "items": [{"name": "产品A", "quantity": 10}]
            }
        }
        
        print(f"✓ OCR识别完成，置信度: {ocr_result['confidence']:.2f}")
        print(f"识别文本: {ocr_result['text']}")
        
        return ocr_result


def main():
    """主演示函数"""
    print("=" * 50)
    print("库房自助出入库客户端系统 - 智能化功能演示")
    print("=" * 50)
    
    # 1. 身份证核验演示
    print("\n1. 身份证核验功能演示")
    print("-" * 30)
    id_demo = IdentityCardDemo()
    id_demo.connect_reader()
    id_info = id_demo.read_id_card()
    id_demo.verify_id_card(id_info)
    
    # 2. 人脸识别演示
    print("\n2. 人脸识别功能演示")
    print("-" * 30)
    face_demo = FaceRecognitionDemo()
    if face_demo.init_camera():
        # 模拟图像
        dummy_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 注册人脸
        face_demo.register_face(1, "张三", [dummy_image])
        
        # 检测和识别
        faces = face_demo.detect_face(dummy_image)
        if faces:
            face_demo.verify_liveness(dummy_image)
            face_demo.recognize_face(dummy_image)
    
    # 3. 小票打印演示
    print("\n3. 小票打印功能演示")
    print("-" * 30)
    print_demo = PrintManagerDemo()
    print_demo.connect_printer()
    
    # 打印入库小票
    inbound_data = {
        "operator": "张三",
        "items": [
            {"name": "产品A", "quantity": 10, "unit": "件"},
            {"name": "产品B", "quantity": 5, "unit": "箱"}
        ],
        "total_quantity": 15,
        "notes": "正常入库"
    }
    print_demo.print_receipt("inbound", inbound_data)
    
    # 4. 票据扫码演示
    print("\n4. 票据扫码功能演示")
    print("-" * 30)
    scanner_demo = TicketScannerDemo()
    if scanner_demo.init_scanner():
        # 模拟图像
        dummy_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 扫描演示
        scanner_demo.scan_qr_code(dummy_image)
        scanner_demo.scan_barcode(dummy_image)
        scanner_demo.extract_text_ocr(dummy_image)
    
    print("\n" + "=" * 50)
    print("智能化功能演示完成！")
    print("=" * 50)


if __name__ == "__main__":
    main()
