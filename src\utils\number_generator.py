"""
单号生成工具
© 2024 贵州睿云慧通科技有限公司
"""

import time
import random
from datetime import datetime
from typing import Dict, Any

class NumberGenerator:
    """单号生成器"""
    
    def __init__(self):
        self.counters = {}  # 计数器字典
    
    def generate_order_number(self, prefix: str, date_format: str = "%Y%m%d") -> str:
        """
        生成订单号
        
        Args:
            prefix: 前缀
            date_format: 日期格式
            
        Returns:
            订单号
        """
        # 获取当前日期
        date_str = datetime.now().strftime(date_format)
        
        # 生成计数器键
        counter_key = f"{prefix}_{date_str}"
        
        # 获取或初始化计数器
        if counter_key not in self.counters:
            self.counters[counter_key] = 0
        
        # 递增计数器
        self.counters[counter_key] += 1
        
        # 生成序号（4位数字，不足补0）
        sequence = str(self.counters[counter_key]).zfill(4)
        
        # 组合订单号
        order_number = f"{prefix}{date_str}{sequence}"
        
        return order_number
    
    def generate_barcode(self, length: int = 13) -> str:
        """
        生成条码
        
        Args:
            length: 条码长度
            
        Returns:
            条码
        """
        # 生成随机数字
        digits = ''.join([str(random.randint(0, 9)) for _ in range(length - 1)])
        
        # 计算校验位（简化的EAN-13算法）
        check_digit = self._calculate_check_digit(digits)
        
        return digits + str(check_digit)
    
    def generate_batch_number(self, product_code: str = None) -> str:
        """
        生成批次号
        
        Args:
            product_code: 产品代码
            
        Returns:
            批次号
        """
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = str(random.randint(100, 999))
        
        if product_code:
            return f"{product_code}_{timestamp}_{random_suffix}"
        else:
            return f"BATCH_{timestamp}_{random_suffix}"
    
    def generate_serial_number(self, product_code: str = None) -> str:
        """
        生成序列号
        
        Args:
            product_code: 产品代码
            
        Returns:
            序列号
        """
        timestamp = str(int(time.time()))
        random_suffix = str(random.randint(10000, 99999))
        
        if product_code:
            return f"{product_code}_{timestamp}_{random_suffix}"
        else:
            return f"SN_{timestamp}_{random_suffix}"
    
    def _calculate_check_digit(self, digits: str) -> int:
        """
        计算校验位
        
        Args:
            digits: 数字串
            
        Returns:
            校验位
        """
        total = 0
        for i, digit in enumerate(digits):
            if i % 2 == 0:
                total += int(digit)
            else:
                total += int(digit) * 3
        
        return (10 - (total % 10)) % 10

# 全局单号生成器实例
_number_generator = NumberGenerator()

def generate_order_number(prefix: str) -> str:
    """
    生成订单号
    
    Args:
        prefix: 前缀（如：IN-入库，OUT-出库，ST-盘点）
        
    Returns:
        订单号
    """
    return _number_generator.generate_order_number(prefix)

def generate_barcode(length: int = 13) -> str:
    """
    生成条码
    
    Args:
        length: 条码长度
        
    Returns:
        条码
    """
    return _number_generator.generate_barcode(length)

def generate_batch_number(product_code: str = None) -> str:
    """
    生成批次号
    
    Args:
        product_code: 产品代码
        
    Returns:
        批次号
    """
    return _number_generator.generate_batch_number(product_code)

def generate_serial_number(product_code: str = None) -> str:
    """
    生成序列号
    
    Args:
        product_code: 产品代码
        
    Returns:
        序列号
    """
    return _number_generator.generate_serial_number(product_code)

# 示例使用
if __name__ == "__main__":
    # 测试单号生成
    print("入库单号:", generate_order_number("IN"))
    print("出库单号:", generate_order_number("OUT"))
    print("盘点单号:", generate_order_number("ST"))
    print("条码:", generate_barcode())
    print("批次号:", generate_batch_number("PROD001"))
    print("序列号:", generate_serial_number("PROD001"))
