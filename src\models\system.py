"""
系统相关数据模型
© 2024 贵州睿云慧通科技有限公司
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
import enum

class LogLevel(enum.Enum):
    """日志级别枚举"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class OperationLog(Base):
    """操作日志表"""
    __tablename__ = "operation_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    module = Column(String(50), nullable=False)  # 模块名称
    action = Column(String(100), nullable=False)  # 操作动作
    description = Column(Text)                   # 操作描述
    level = Column(String(20), default=LogLevel.INFO.value)
    ip_address = Column(String(45))              # IP地址
    user_agent = Column(Text)                    # 用户代理
    request_data = Column(JSON)                  # 请求数据
    response_data = Column(JSON)                 # 响应数据
    execution_time = Column(Integer)             # 执行时间(毫秒)
    status = Column(String(20), default="success")  # success, error, warning
    error_message = Column(Text)                 # 错误信息
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="operation_logs")
    
    # 索引
    __table_args__ = (
        Index('idx_operation_log_user', 'user_id'),
        Index('idx_operation_log_module', 'module'),
        Index('idx_operation_log_action', 'action'),
        Index('idx_operation_log_level', 'level'),
        Index('idx_operation_log_date', 'created_at'),
        Index('idx_operation_log_status', 'status'),
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "module": self.module,
            "action": self.action,
            "description": self.description,
            "level": self.level,
            "ip_address": self.ip_address,
            "execution_time": self.execution_time,
            "status": self.status,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = "system_configs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    category = Column(String(50), nullable=False)  # 配置分类
    key = Column(String(100), nullable=False)      # 配置键
    value = Column(Text)                           # 配置值
    data_type = Column(String(20), default="string")  # string, int, float, bool, json
    description = Column(Text)                     # 配置描述
    is_encrypted = Column(Boolean, default=False) # 是否加密存储
    is_readonly = Column(Boolean, default=False)  # 是否只读
    sort_order = Column(Integer, default=0)       # 排序
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 复合唯一约束
    __table_args__ = (
        Index('idx_system_config_category_key', 'category', 'key', unique=True),
        Index('idx_system_config_category', 'category'),
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "category": self.category,
            "key": self.key,
            "value": self.value,
            "data_type": self.data_type,
            "description": self.description,
            "is_encrypted": self.is_encrypted,
            "is_readonly": self.is_readonly,
            "sort_order": self.sort_order,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class Ticket(Base):
    """票据信息表"""
    __tablename__ = "tickets"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    ticket_number = Column(String(100), unique=True, nullable=False, index=True)
    ticket_type = Column(String(50), nullable=False)  # invoice, receipt, delivery_note, etc.
    qr_code_data = Column(Text)                       # 二维码数据
    barcode_data = Column(String(100))                # 条形码数据
    ocr_text = Column(Text)                           # OCR识别文本
    extracted_info = Column(JSON)                     # 提取的结构化信息
    image_path = Column(String(255))                  # 票据图片路径
    status = Column(String(20), default="valid")     # valid, invalid, expired, used
    source = Column(String(50))                       # 来源：scan, upload, manual
    confidence_score = Column(Integer)                # 识别置信度(0-100)
    verification_result = Column(JSON)                # 验证结果
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    verified_at = Column(DateTime)
    
    # 索引
    __table_args__ = (
        Index('idx_ticket_type', 'ticket_type'),
        Index('idx_ticket_status', 'status'),
        Index('idx_ticket_date', 'created_at'),
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "ticket_number": self.ticket_number,
            "ticket_type": self.ticket_type,
            "qr_code_data": self.qr_code_data,
            "barcode_data": self.barcode_data,
            "ocr_text": self.ocr_text,
            "extracted_info": self.extracted_info,
            "image_path": self.image_path,
            "status": self.status,
            "source": self.source,
            "confidence_score": self.confidence_score,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "verified_at": self.verified_at.isoformat() if self.verified_at else None
        }

class PrintJob(Base):
    """打印任务表"""
    __tablename__ = "print_jobs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    job_number = Column(String(50), unique=True, nullable=False)
    job_type = Column(String(50), nullable=False)  # receipt, label, report, document
    template_name = Column(String(100))            # 模板名称
    printer_name = Column(String(100))             # 打印机名称
    data = Column(JSON)                            # 打印数据
    status = Column(String(20), default="pending") # pending, printing, completed, failed
    priority = Column(Integer, default=5)          # 优先级(1-10)
    copies = Column(Integer, default=1)            # 打印份数
    user_id = Column(Integer, ForeignKey("users.id"))
    reference_type = Column(String(50))            # 关联类型
    reference_id = Column(Integer)                 # 关联ID
    error_message = Column(Text)                   # 错误信息
    created_at = Column(DateTime, default=func.now())
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # 关联关系
    user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index('idx_print_job_type', 'job_type'),
        Index('idx_print_job_status', 'status'),
        Index('idx_print_job_user', 'user_id'),
        Index('idx_print_job_date', 'created_at'),
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "job_number": self.job_number,
            "job_type": self.job_type,
            "template_name": self.template_name,
            "printer_name": self.printer_name,
            "status": self.status,
            "priority": self.priority,
            "copies": self.copies,
            "user_id": self.user_id,
            "reference_type": self.reference_type,
            "reference_id": self.reference_id,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None
        }

class DeviceInfo(Base):
    """设备信息表"""
    __tablename__ = "device_info"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    device_type = Column(String(50), nullable=False)  # scanner, printer, camera, rfid_reader, id_card_reader
    device_name = Column(String(100), nullable=False)
    device_model = Column(String(100))
    serial_number = Column(String(100))
    manufacturer = Column(String(100))
    connection_type = Column(String(20))  # usb, serial, network, bluetooth
    connection_params = Column(JSON)      # 连接参数
    status = Column(String(20), default="offline")  # online, offline, error, maintenance
    last_heartbeat = Column(DateTime)     # 最后心跳时间
    firmware_version = Column(String(50))
    driver_version = Column(String(50))
    configuration = Column(JSON)          # 设备配置
    capabilities = Column(JSON)           # 设备能力
    location = Column(String(200))        # 设备位置
    is_active = Column(Boolean, default=True)
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_device_type', 'device_type'),
        Index('idx_device_status', 'status'),
        Index('idx_device_serial', 'serial_number'),
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "device_type": self.device_type,
            "device_name": self.device_name,
            "device_model": self.device_model,
            "serial_number": self.serial_number,
            "manufacturer": self.manufacturer,
            "connection_type": self.connection_type,
            "status": self.status,
            "last_heartbeat": self.last_heartbeat.isoformat() if self.last_heartbeat else None,
            "firmware_version": self.firmware_version,
            "driver_version": self.driver_version,
            "location": self.location,
            "is_active": self.is_active,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class DataBackup(Base):
    """数据备份表"""
    __tablename__ = "data_backups"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    backup_name = Column(String(200), nullable=False)
    backup_type = Column(String(20), default="full")  # full, incremental, differential
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)                       # 文件大小(字节)
    compression_type = Column(String(20))             # 压缩类型
    encryption_type = Column(String(20))              # 加密类型
    checksum = Column(String(100))                    # 校验和
    status = Column(String(20), default="completed")  # running, completed, failed
    start_time = Column(DateTime, default=func.now())
    end_time = Column(DateTime)
    duration = Column(Integer)                        # 备份耗时(秒)
    created_by = Column(Integer, ForeignKey("users.id"))
    notes = Column(Text)
    
    # 关联关系
    creator = relationship("User")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "backup_name": self.backup_name,
            "backup_type": self.backup_type,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "compression_type": self.compression_type,
            "status": self.status,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration": self.duration,
            "created_by": self.created_by,
            "notes": self.notes
        }
