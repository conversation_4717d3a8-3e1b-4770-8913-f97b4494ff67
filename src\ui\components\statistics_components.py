"""
WMS客户端系统 - 统计数据展示组件
统一的统计卡片、图表、表格组件，确保与系统设置风格一致
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QTableWidget, QTableWidgetItem
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from typing import Optional, List, Dict, Any

from .base_components import BaseCard, TitleLabel, BodyLabel
from ..styles.theme import theme


class StatisticsCard(BaseCard):
    """统一的统计卡片组件"""
    
    def __init__(self, title: str, value: str, unit: str = "", 
                 color: str = None, trend: str = "", parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.unit = unit
        self.color = color or theme.colors.SECONDARY
        self.trend = trend
        
        self._setup_content()
    
    def _setup_content(self):
        """设置卡片内容"""
        self.setFixedHeight(120)
        
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(theme.spacing.SM)
        
        # 标题 - 使用统一的字体大小和颜色
        title_label = BodyLabel(self.title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {theme.colors.TEXT_PRIMARY};
                font-size: {theme.typography.BODY_SIZE}px;
                font-weight: {theme.typography.WEIGHT_MEDIUM};
            }}
        """)
        layout.addWidget(title_label)
        
        # 数值 - 使用适中的字体大小
        value_label = TitleLabel(self.value, level=2)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                color: {self.color};
                font-size: {theme.typography.H3_SIZE}px;
                font-weight: {theme.typography.WEIGHT_BOLD};
            }}
        """)
        layout.addWidget(value_label)
        
        # 单位和趋势
        if self.unit or self.trend:
            bottom_layout = QHBoxLayout()
            bottom_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            if self.unit:
                unit_label = BodyLabel(self.unit)
                unit_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                unit_label.setStyleSheet(f"""
                    QLabel {{
                        color: {theme.colors.TEXT_SECONDARY};
                        font-size: {theme.typography.BODY_SMALL_SIZE}px;
                        font-weight: {theme.typography.WEIGHT_MEDIUM};
                    }}
                """)
                bottom_layout.addWidget(unit_label)
            
            if self.trend:
                trend_label = BodyLabel(self.trend)
                trend_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                trend_label.setStyleSheet(f"""
                    QLabel {{
                        color: {theme.colors.TEXT_MUTED};
                        font-size: {theme.typography.CAPTION_SIZE}px;
                        font-weight: {theme.typography.WEIGHT_REGULAR};
                        margin-left: {theme.spacing.SM}px;
                    }}
                """)
                bottom_layout.addWidget(trend_label)
            
            bottom_widget = QWidget()
            bottom_widget.setLayout(bottom_layout)
            layout.addWidget(bottom_widget)
        
        self.add_layout(layout)
    
    def update_value(self, value: str, trend: str = ""):
        """更新统计值"""
        self.value = value
        self.trend = trend
        # 重新设置内容
        self._clear_layout()
        self._setup_content()
    
    def _clear_layout(self):
        """清空布局"""
        while self.layout.count():
            child = self.layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()


class StatisticsGrid(QWidget):
    """统计卡片网格组件"""
    
    def __init__(self, columns: int = 4, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.columns = columns
        self.cards = []
        
        self._setup_layout()
    
    def _setup_layout(self):
        """设置网格布局"""
        self.layout = QGridLayout(self)
        self.layout.setSpacing(theme.spacing.MD)
        
        # 设置列的拉伸因子，确保均匀分布
        for col in range(self.columns):
            self.layout.setColumnStretch(col, 1)
    
    def add_card(self, card: StatisticsCard):
        """添加统计卡片"""
        row = len(self.cards) // self.columns
        col = len(self.cards) % self.columns
        
        self.layout.addWidget(card, row, col)
        self.cards.append(card)
    
    def add_cards(self, cards_data: List[Dict[str, Any]]):
        """批量添加统计卡片"""
        for card_data in cards_data:
            card = StatisticsCard(
                title=card_data.get('title', ''),
                value=card_data.get('value', ''),
                unit=card_data.get('unit', ''),
                color=card_data.get('color', theme.colors.SECONDARY),
                trend=card_data.get('trend', '')
            )
            self.add_card(card)
    
    def clear_cards(self):
        """清空所有卡片"""
        for card in self.cards:
            card.deleteLater()
        self.cards.clear()


class StatisticsTable(BaseCard):
    """统一的统计表格组件"""
    
    def __init__(self, title: str, headers: List[str], parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.title = title
        self.headers = headers
        
        self._setup_content()
    
    def _setup_content(self):
        """设置表格内容"""
        layout = QVBoxLayout()
        
        # 表格标题
        if self.title:
            title_label = TitleLabel(self.title, level=3)
            title_label.setStyleSheet(f"""
                QLabel {{
                    color: {theme.colors.TEXT_PRIMARY};
                    font-size: {theme.typography.H4_SIZE}px;
                    font-weight: {theme.typography.WEIGHT_MEDIUM};
                    margin-bottom: {theme.spacing.SM}px;
                }}
            """)
            layout.addWidget(title_label)
        
        # 创建表格
        self.table = QTableWidget(0, len(self.headers))
        self.table.setHorizontalHeaderLabels(self.headers)
        self._style_table()
        
        layout.addWidget(self.table)
        self.add_layout(layout)
    
    def _style_table(self):
        """设置表格样式 - 与系统设置风格统一"""
        self.table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {theme.colors.WHITE};
                border: 1px solid {theme.colors.BORDER};
                border-radius: {theme.border_radius.MD}px;
                gridline-color: {theme.colors.BORDER};
                font-size: {theme.typography.BODY_SIZE}px;
            }}
            QTableWidget::item {{
                padding: {theme.spacing.SM}px;
                border-bottom: 1px solid {theme.colors.BORDER};
            }}
            QTableWidget::item:selected {{
                background-color: {theme.colors.SECONDARY};
                color: {theme.colors.WHITE};
            }}
            QHeaderView::section {{
                background-color: {theme.colors.BACKGROUND};
                color: {theme.colors.TEXT_PRIMARY};
                padding: {theme.spacing.MD}px;
                border: none;
                border-bottom: 2px solid {theme.colors.PRIMARY};
                font-weight: {theme.typography.WEIGHT_MEDIUM};
            }}
        """)
        
        # 设置列宽自适应
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
    
    def set_data(self, data: List[List[str]]):
        """设置表格数据"""
        self.table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                if col < len(self.headers):
                    self.table.setItem(row, col, QTableWidgetItem(str(cell_data)))
    
    def add_row(self, row_data: List[str]):
        """添加一行数据"""
        row_count = self.table.rowCount()
        self.table.insertRow(row_count)
        for col, cell_data in enumerate(row_data):
            if col < len(self.headers):
                self.table.setItem(row_count, col, QTableWidgetItem(str(cell_data)))
    
    def clear_data(self):
        """清空表格数据"""
        self.table.setRowCount(0)


class StatisticsSummary(BaseCard):
    """统计摘要组件 - 用于显示关键指标概览"""
    
    def __init__(self, title: str, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.title = title
        self.metrics = []
        
        self._setup_content()
    
    def _setup_content(self):
        """设置摘要内容"""
        self.main_layout = QVBoxLayout()
        
        # 摘要标题
        if self.title:
            title_label = TitleLabel(self.title, level=3)
            title_label.setStyleSheet(f"""
                QLabel {{
                    color: {theme.colors.PRIMARY};
                    font-size: {theme.typography.H4_SIZE}px;
                    font-weight: {theme.typography.WEIGHT_MEDIUM};
                    margin-bottom: {theme.spacing.MD}px;
                }}
            """)
            self.main_layout.addWidget(title_label)
        
        # 指标容器
        self.metrics_layout = QVBoxLayout()
        self.main_layout.addLayout(self.metrics_layout)
        
        self.add_layout(self.main_layout)
    
    def add_metric(self, label: str, value: str, color: str = None):
        """添加一个指标"""
        metric_layout = QHBoxLayout()
        
        # 指标标签
        label_widget = BodyLabel(label)
        label_widget.setStyleSheet(f"""
            QLabel {{
                color: {theme.colors.TEXT_PRIMARY};
                font-size: {theme.typography.BODY_SIZE}px;
                font-weight: {theme.typography.WEIGHT_REGULAR};
            }}
        """)
        metric_layout.addWidget(label_widget)
        
        metric_layout.addStretch()
        
        # 指标值
        value_widget = BodyLabel(value)
        value_color = color or theme.colors.SECONDARY
        value_widget.setStyleSheet(f"""
            QLabel {{
                color: {value_color};
                font-size: {theme.typography.BODY_SIZE}px;
                font-weight: {theme.typography.WEIGHT_BOLD};
            }}
        """)
        metric_layout.addWidget(value_widget)
        
        # 添加到指标布局
        metric_widget = QWidget()
        metric_widget.setLayout(metric_layout)
        self.metrics_layout.addWidget(metric_widget)
        
        self.metrics.append((label, value, color))
    
    def clear_metrics(self):
        """清空所有指标"""
        while self.metrics_layout.count():
            child = self.metrics_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        self.metrics.clear()
