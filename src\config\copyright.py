"""
版权信息配置
贵州睿云慧通科技有限公司
"""

from datetime import datetime

# 公司信息
COMPANY_NAME = "贵州睿云慧通科技有限公司"
COMPANY_NAME_EN = "Guizhou Ruiyun Huitong Technology Co., Ltd."
COMPANY_DOMAIN = "www.gzryht.com"
COMPANY_ADDRESS = "贵州省贵阳市"

# 产品信息
PRODUCT_NAME = "WMS库房自助出入库客户端系统"
PRODUCT_NAME_EN = "WMS Warehouse Self-Service In/Out Client System"
PRODUCT_VERSION = "1.0.0"
PRODUCT_DESCRIPTION = "智能仓储管理 · 高效 · 智能 · 安全"

# 版权信息
COPYRIGHT_YEAR = 2024
COPYRIGHT_SYMBOL = "©"
COPYRIGHT_TEXT = f"{COPYRIGHT_SYMBOL} {COPYRIGHT_YEAR} {COMPANY_NAME}"
COPYRIGHT_TEXT_EN = f"{COPYRIGHT_SYMBOL} {COPYRIGHT_YEAR} {COMPANY_NAME_EN}"

# 著作权声明
RIGHTS_STATEMENT = "版权所有 · 保留所有权利"
RIGHTS_STATEMENT_FULL = "版权所有 · 保留所有权利 · 未经许可不得复制或传播"
RIGHTS_STATEMENT_EN = "All Rights Reserved"

# 完整版权声明
FULL_COPYRIGHT = f"{COPYRIGHT_TEXT}\n{RIGHTS_STATEMENT}"
FULL_COPYRIGHT_DETAILED = f"{COPYRIGHT_TEXT}\n{RIGHTS_STATEMENT_FULL}"

# 状态栏版权信息
STATUS_COPYRIGHT = f"{COPYRIGHT_TEXT} - {PRODUCT_NAME}"

# 关于对话框版权信息
ABOUT_COPYRIGHT = f"""
{PRODUCT_NAME} v{PRODUCT_VERSION}

{PRODUCT_DESCRIPTION}

{COPYRIGHT_TEXT}
{RIGHTS_STATEMENT_FULL}

开发商：{COMPANY_NAME}
网站：{COMPANY_DOMAIN}
地址：{COMPANY_ADDRESS}

本软件受中华人民共和国著作权法保护。
未经授权，不得复制、修改、分发或以其他方式使用本软件。
"""

# 许可证信息
LICENSE_INFO = """
软件许可协议

本软件由贵州睿云慧通科技有限公司开发并拥有完全著作权。

1. 授权范围
   本软件仅授权给合法用户在约定范围内使用。

2. 使用限制
   - 不得复制、修改、反编译或逆向工程
   - 不得用于商业目的（除非另有协议）
   - 不得传播或分发给第三方

3. 免责声明
   本软件按"现状"提供，不提供任何明示或暗示的保证。

4. 技术支持
   如需技术支持，请联系：{COMPANY_DOMAIN}

{COPYRIGHT_TEXT}
{RIGHTS_STATEMENT_FULL}
"""

def get_copyright_info():
    """获取版权信息字典"""
    return {
        "company_name": COMPANY_NAME,
        "company_name_en": COMPANY_NAME_EN,
        "company_domain": COMPANY_DOMAIN,
        "product_name": PRODUCT_NAME,
        "product_version": PRODUCT_VERSION,
        "copyright_text": COPYRIGHT_TEXT,
        "rights_statement": RIGHTS_STATEMENT,
        "full_copyright": FULL_COPYRIGHT,
        "status_copyright": STATUS_COPYRIGHT,
        "about_copyright": ABOUT_COPYRIGHT,
        "license_info": LICENSE_INFO
    }

def get_window_title():
    """获取窗口标题"""
    return f"{PRODUCT_NAME} - {COMPANY_NAME}"

def get_footer_text():
    """获取页脚文本"""
    return f"{COPYRIGHT_TEXT}\n{RIGHTS_STATEMENT}"

def get_version_info():
    """获取版本信息"""
    return f"版本 {PRODUCT_VERSION}"

def print_copyright():
    """打印版权信息"""
    print("=" * 60)
    print(f"{PRODUCT_NAME}")
    print(f"{COPYRIGHT_TEXT}")
    print(f"{RIGHTS_STATEMENT_FULL}")
    print("=" * 60)

if __name__ == "__main__":
    print_copyright()
    print("\n版权信息配置:")
    info = get_copyright_info()
    for key, value in info.items():
        if "\n" not in str(value):
            print(f"  {key}: {value}")
