#!/usr/bin/env python3
"""
WMS安全功能测试脚本
© 2024 贵州睿云慧通科技有限公司
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApp<PERSON>, QWidget, QVBoxLayout, QPushButton, QLabel, QTextEdit
from src.security.password_manager import password_manager
from src.security.license_manager import license_manager
from src.security.auth_manager import auth_manager
from src.ui.dialogs.license_dialog import LicenseDialog


class SecurityTestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("WMS安全功能测试")
        self.setFixedSize(800, 600)
        self.setStyleSheet("background-color: #f8fafc;")
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 标题
        title = QLabel("🔐 WMS安全功能测试")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #1e40af;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # 测试按钮
        self.create_test_buttons(layout)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 15px;
                font-family: "Consolas", monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.result_text)
    
    def create_test_buttons(self, layout):
        """创建测试按钮"""
        buttons_layout = QVBoxLayout()
        
        # 密码管理测试
        password_btn = QPushButton("🔑 测试密码加盐功能")
        password_btn.clicked.connect(self.test_password_manager)
        password_btn.setStyleSheet(self.get_button_style("#10b981"))
        buttons_layout.addWidget(password_btn)
        
        # 授权管理测试
        license_btn = QPushButton("📜 测试授权管理功能")
        license_btn.clicked.connect(self.test_license_manager)
        license_btn.setStyleSheet(self.get_button_style("#3b82f6"))
        buttons_layout.addWidget(license_btn)
        
        # 用户认证测试
        auth_btn = QPushButton("👤 测试用户认证功能")
        auth_btn.clicked.connect(self.test_auth_manager)
        auth_btn.setStyleSheet(self.get_button_style("#8b5cf6"))
        buttons_layout.addWidget(auth_btn)
        
        # 授权对话框测试
        dialog_btn = QPushButton("🖥️ 测试授权对话框")
        dialog_btn.clicked.connect(self.test_license_dialog)
        dialog_btn.setStyleSheet(self.get_button_style("#f59e0b"))
        buttons_layout.addWidget(dialog_btn)
        
        # 生成试用授权
        trial_btn = QPushButton("🎯 生成试用授权")
        trial_btn.clicked.connect(self.generate_trial_license)
        trial_btn.setStyleSheet(self.get_button_style("#ef4444"))
        buttons_layout.addWidget(trial_btn)
        
        layout.addLayout(buttons_layout)
    
    def get_button_style(self, color):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {color}dd);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: 600;
                margin: 5px;
            }}
            QPushButton:hover {{
                background: {color};
            }}
        """
    
    def log_result(self, message):
        """记录测试结果"""
        self.result_text.append(f"[{self.get_timestamp()}] {message}")
        self.result_text.append("")  # 空行
    
    def get_timestamp(self):
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def test_password_manager(self):
        """测试密码管理器"""
        self.log_result("🔑 开始测试密码管理器...")
        
        try:
            # 测试密码哈希
            password = "test123"
            hashed, salt = password_manager.hash_password(password)
            self.log_result(f"✅ 密码哈希成功")
            self.log_result(f"   原始密码: {password}")
            self.log_result(f"   哈希结果: {hashed[:20]}...")
            self.log_result(f"   盐值: {salt[:20]}...")
            
            # 测试密码验证
            is_valid = password_manager.verify_password(password, hashed, salt)
            self.log_result(f"✅ 密码验证: {'成功' if is_valid else '失败'}")
            
            # 测试错误密码
            is_invalid = password_manager.verify_password("wrong", hashed, salt)
            self.log_result(f"✅ 错误密码验证: {'失败' if not is_invalid else '成功'}")
            
            # 测试密码强度验证
            strong_valid, strong_msg = password_manager.validate_password_strength("StrongPass123")
            self.log_result(f"✅ 强密码验证: {strong_msg}")
            
            weak_valid, weak_msg = password_manager.validate_password_strength("123")
            self.log_result(f"❌ 弱密码验证: {weak_msg}")
            
            # 测试机器ID
            machine_id = password_manager.hash_machine_id()
            self.log_result(f"🖥️ 机器ID: {machine_id[:16]}...")
            
        except Exception as e:
            self.log_result(f"❌ 密码管理器测试失败: {str(e)}")
    
    def test_license_manager(self):
        """测试授权管理器"""
        self.log_result("📜 开始测试授权管理器...")
        
        try:
            # 生成试用授权
            trial_license = license_manager.get_trial_license(30)
            self.log_result(f"✅ 生成试用授权成功")
            self.log_result(f"   授权密钥: {trial_license[:50]}...")
            
            # 验证授权
            is_valid, info = license_manager.validate_license(trial_license)
            self.log_result(f"✅ 授权验证: {'成功' if is_valid else '失败'}")
            
            if is_valid:
                self.log_result(f"   授权类型: {info.get('type_name', '未知')}")
                self.log_result(f"   剩余天数: {info.get('days_remaining', 0)}")
                self.log_result(f"   功能权限: {len(info.get('features', {}))} 项")
            
            # 保存授权
            save_result = license_manager.save_license(trial_license)
            self.log_result(f"✅ 保存授权: {'成功' if save_result else '失败'}")
            
            # 获取授权状态
            status = license_manager.get_license_status()
            self.log_result(f"📊 授权状态: {status['message']}")
            
        except Exception as e:
            self.log_result(f"❌ 授权管理器测试失败: {str(e)}")
    
    def test_auth_manager(self):
        """测试认证管理器"""
        self.log_result("👤 开始测试认证管理器...")
        
        try:
            # 测试管理员登录
            is_valid, result = auth_manager.authenticate_user("admin", "admin123")
            self.log_result(f"✅ 管理员登录: {'成功' if is_valid else '失败'}")
            
            if is_valid:
                user = result['user']
                self.log_result(f"   用户名: {user['username']}")
                self.log_result(f"   全名: {user['full_name']}")
                self.log_result(f"   角色: {user['role']}")
                self.log_result(f"   权限数: {len(user['permissions'])} 项")
            else:
                self.log_result(f"   错误: {result.get('error', '未知错误')}")
            
            # 测试错误密码
            is_invalid, invalid_result = auth_manager.authenticate_user("admin", "wrong")
            self.log_result(f"❌ 错误密码登录: {'失败' if not is_invalid else '成功'}")
            if not is_invalid:
                self.log_result(f"   错误信息: {invalid_result.get('error', '未知错误')}")
            
            # 测试创建用户
            create_result, create_msg = auth_manager.create_user(
                "testuser", "test123456", "operator", "测试用户", "<EMAIL>", "测试部门"
            )
            self.log_result(f"👥 创建用户: {'成功' if create_result else '失败'}")
            self.log_result(f"   消息: {create_msg}")
            
        except Exception as e:
            self.log_result(f"❌ 认证管理器测试失败: {str(e)}")
    
    def test_license_dialog(self):
        """测试授权对话框"""
        self.log_result("🖥️ 打开授权对话框...")
        
        try:
            dialog = LicenseDialog(self)
            dialog.exec()
            self.log_result("✅ 授权对话框测试完成")
        except Exception as e:
            self.log_result(f"❌ 授权对话框测试失败: {str(e)}")
    
    def generate_trial_license(self):
        """生成并保存试用授权"""
        self.log_result("🎯 生成试用授权...")
        
        try:
            # 生成30天试用授权
            trial_license = license_manager.get_trial_license(30)
            
            # 保存授权
            if license_manager.save_license(trial_license):
                self.log_result("✅ 试用授权生成并保存成功！")
                self.log_result("   有效期: 30天")
                self.log_result("   类型: 试用版")
                
                # 验证保存的授权
                status = license_manager.get_license_status()
                if status['status'] == 'valid':
                    info = status['info']
                    self.log_result(f"   剩余天数: {info.get('days_remaining', 0)}")
                    self.log_result("   功能权限:")
                    features = info.get('features', {})
                    for feature, enabled in features.items():
                        status_icon = "✅" if enabled else "❌"
                        self.log_result(f"     {status_icon} {feature}")
                else:
                    self.log_result(f"❌ 授权验证失败: {status['message']}")
            else:
                self.log_result("❌ 试用授权保存失败")
                
        except Exception as e:
            self.log_result(f"❌ 生成试用授权失败: {str(e)}")


def main():
    """主函数"""
    print("=" * 60)
    print("🔐 WMS安全功能测试")
    print("© 2024 贵州睿云慧通科技有限公司")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    window = SecurityTestWindow()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
