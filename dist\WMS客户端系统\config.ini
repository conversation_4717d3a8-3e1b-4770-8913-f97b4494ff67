# WMS库房自助出入库客户端系统配置文件
# © 2024 贵州睿云慧通科技有限公司

[database]
# 数据库类型: sqlite, postgresql, mysql
type = sqlite
# SQLite数据库文件路径
sqlite_path = data/wms.db
# PostgreSQL连接配置（如果使用PostgreSQL）
postgresql_host = localhost
postgresql_port = 5432
postgresql_database = wms
postgresql_username = wms_user
postgresql_password = wms_password
# 连接池配置
pool_size = 10
max_overflow = 20

[logging]
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
level = INFO
# 日志目录
log_dir = logs
# 日志文件最大大小(MB)
max_file_size = 10
# 保留的日志文件数量
backup_count = 5
# 是否启用彩色日志
enable_color = true

[ui]
# 界面主题: light, dark
theme = light
# 界面语言: zh_CN, en_US
language = zh_CN
# 字体大小
font_size = 9
# 是否显示启动画面
show_splash = true
# 启动画面显示时间(秒)
splash_duration = 5

[security]
# 会话超时时间(秒)
session_timeout = 3600
# 密码最小长度
min_password_length = 6
# 是否启用密码复杂度检查
password_complexity = false
# 登录失败最大次数
max_login_attempts = 5
# 登录失败锁定时间(秒)
lockout_duration = 300

[devices]
# 扫码枪配置
scanner_enabled = true
scanner_port = COM3
scanner_baudrate = 9600

# RFID读卡器配置
rfid_enabled = true
rfid_port = COM4
rfid_baudrate = 9600

# 摄像头配置
camera_enabled = true
camera_index = 0
camera_width = 640
camera_height = 480

# 身份证读卡器配置
id_card_reader_enabled = false
id_card_reader_port = COM5

# 打印机配置
printer_enabled = true
printer_name = default
printer_type = thermal
printer_port = COM6

[features]
# 是否启用人脸识别
face_recognition = true
# 是否启用身份证识别
id_card_recognition = false
# 是否启用票据识别
ticket_recognition = true
# 是否启用语音提示
voice_prompt = false
# 是否启用自动备份
auto_backup = true
# 自动备份间隔(小时)
backup_interval = 24

[business]
# 默认仓库ID
default_warehouse_id = 1
# 是否启用批次管理
batch_management = true
# 是否启用序列号管理
serial_management = false
# 库存预警阈值
stock_warning_threshold = 10
# 是否启用库存预留
inventory_reservation = true

[network]
# API服务器地址
api_server = http://localhost:8000
# 连接超时时间(秒)
connection_timeout = 30
# 请求超时时间(秒)
request_timeout = 60
# 是否启用SSL验证
ssl_verify = true

[performance]
# 数据库查询超时时间(秒)
db_query_timeout = 30
# 界面刷新间隔(毫秒)
ui_refresh_interval = 1000
# 缓存大小(MB)
cache_size = 100
# 是否启用数据压缩
data_compression = false

[maintenance]
# 是否启用自动清理
auto_cleanup = true
# 日志保留天数
log_retention_days = 30
# 临时文件保留天数
temp_file_retention_days = 7
# 是否启用性能监控
performance_monitoring = false
