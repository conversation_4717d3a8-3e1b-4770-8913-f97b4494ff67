#!/usr/bin/env python3
"""
WMS库房自助出入库客户端系统 - 打包脚本
© 2024 贵州睿云慧通科技有限公司

使用PyInstaller将项目打包成Windows可执行文件
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 80)
    print("🏢 WMS库房自助出入库客户端系统 - 打包工具")
    print("© 2024 贵州睿云慧通科技有限公司")
    print("=" * 80)

def check_requirements():
    """检查打包环境"""
    print("🔍 检查打包环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"📍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ Python版本过低，建议使用Python 3.8+")
        return False
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"📍 PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，请运行: pip install PyInstaller")
        return False
    
    # 检查主要依赖
    required_packages = [
        ('PyQt6', 'PyQt6'),
        ('sqlalchemy', 'sqlalchemy'),
        ('numpy', 'numpy'),
        ('opencv-python', 'cv2')
    ]
    missing_packages = []

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name} 已安装")
        except ImportError:
            missing_packages.append(package_name)
            print(f"❌ {package_name} 未安装")
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🗑️  删除目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理.pyc文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))

def create_resources_dir():
    """创建资源目录（如果不存在）"""
    print("📁 检查资源目录...")
    
    # 确保必要的目录存在
    dirs_to_create = ['data', 'logs', 'resources', 'resources/icons']
    
    for dir_name in dirs_to_create:
        if not os.path.exists(dir_name):
            print(f"📁 创建目录: {dir_name}")
            os.makedirs(dir_name, exist_ok=True)
    
    # 创建空的数据库文件（如果不存在）
    db_file = Path('data/wms.db')
    if not db_file.exists():
        print("📄 创建空数据库文件")
        db_file.touch()

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    # 使用spec文件构建
    spec_file = 'wms_client.spec'
    
    if not os.path.exists(spec_file):
        print(f"❌ 找不到spec文件: {spec_file}")
        return False
    
    # 执行PyInstaller命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',  # 清理临时文件
        '--noconfirm',  # 不询问覆盖
        spec_file
    ]
    
    print(f"🚀 执行命令: {' '.join(cmd)}")
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 执行构建
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        # 记录结束时间
        end_time = time.time()
        build_time = end_time - start_time
        
        print(f"✅ 构建成功！耗时: {build_time:.1f}秒")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print("错误输出:")
        print(e.stderr)
        return False

def post_build_tasks():
    """构建后任务"""
    print("📦 执行构建后任务...")
    
    dist_dir = Path('dist/WMS客户端系统')
    
    if not dist_dir.exists():
        print("❌ 构建目录不存在")
        return False
    
    # 复制额外文件
    extra_files = [
        ('README.md', '使用说明.txt'),
        ('config.ini', 'config.ini'),
    ]
    
    for src, dst in extra_files:
        if os.path.exists(src):
            dst_path = dist_dir / dst
            print(f"📄 复制文件: {src} -> {dst_path}")
            shutil.copy2(src, dst_path)
    
    # 创建启动脚本
    startup_script = dist_dir / '启动WMS系统.bat'
    with open(startup_script, 'w', encoding='utf-8') as f:
        f.write('@echo off\n')
        f.write('title WMS库房自助出入库客户端系统\n')
        f.write('echo 正在启动WMS系统...\n')
        f.write('"WMS客户端系统.exe"\n')
        f.write('pause\n')
    
    print(f"📄 创建启动脚本: {startup_script}")
    
    # 显示构建结果
    exe_file = dist_dir / 'WMS客户端系统.exe'
    if exe_file.exists():
        file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
        print(f"✅ 可执行文件已生成: {exe_file}")
        print(f"📊 文件大小: {file_size:.1f} MB")
        return True
    else:
        print("❌ 可执行文件未生成")
        return False

def create_installer():
    """创建安装包（可选）"""
    print("📦 是否创建安装包？(y/n): ", end='')
    
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是']:
            print("💡 提示: 可以使用NSIS、Inno Setup等工具创建安装包")
            print("💡 或者直接将dist目录打包成zip文件分发")
            
            # 创建zip包
            import zipfile
            zip_name = f"WMS客户端系统_v1.0.0_{time.strftime('%Y%m%d')}.zip"
            
            with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
                dist_dir = Path('dist/WMS客户端系统')
                for file_path in dist_dir.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(dist_dir.parent)
                        zipf.write(file_path, arcname)
            
            print(f"📦 已创建分发包: {zip_name}")
    except KeyboardInterrupt:
        print("\n⏹️  跳过创建安装包")

def main():
    """主函数"""
    print_banner()
    
    try:
        # 检查环境
        if not check_requirements():
            print("❌ 环境检查失败，请解决上述问题后重试")
            return 1
        
        # 清理构建目录
        clean_build_dirs()
        
        # 创建资源目录
        create_resources_dir()
        
        # 构建可执行文件
        if not build_executable():
            print("❌ 构建失败")
            return 1
        
        # 构建后任务
        if not post_build_tasks():
            print("❌ 构建后任务失败")
            return 1
        
        # 创建安装包
        create_installer()
        
        print("\n" + "=" * 80)
        print("🎉 打包完成！")
        print("📁 可执行文件位置: dist/WMS客户端系统/")
        print("🚀 可以直接运行 WMS客户端系统.exe")
        print("=" * 80)
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⏹️  用户取消操作")
        return 1
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
