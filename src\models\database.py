"""
数据库连接和配置管理
© 2024 贵州睿云慧通科技有限公司
"""

import os
import sqlite3
from typing import Optional, Dict, Any
from contextlib import contextmanager
from sqlalchemy import create_engine, MetaData, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import logging

logger = logging.getLogger(__name__)

# 数据库基类
Base = declarative_base()

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_url: str = None):
        """
        初始化数据库管理器
        
        Args:
            database_url: 数据库连接URL，默认使用SQLite
        """
        if database_url is None:
            # 默认使用SQLite数据库
            db_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'data')
            os.makedirs(db_dir, exist_ok=True)
            db_path = os.path.join(db_dir, 'wms.db')
            database_url = f"sqlite:///{db_path}"
        
        self.database_url = database_url
        self.engine = None
        self.SessionLocal = None
        self._initialize_engine()
    
    def _initialize_engine(self):
        """初始化数据库引擎"""
        try:
            if self.database_url.startswith('sqlite'):
                # SQLite配置
                self.engine = create_engine(
                    self.database_url,
                    poolclass=StaticPool,
                    connect_args={
                        "check_same_thread": False,
                        "timeout": 20
                    },
                    echo=False  # 生产环境设为False
                )
                
                # 启用SQLite外键约束
                @event.listens_for(self.engine, "connect")
                def set_sqlite_pragma(dbapi_connection, connection_record):
                    cursor = dbapi_connection.cursor()
                    cursor.execute("PRAGMA foreign_keys=ON")
                    cursor.execute("PRAGMA journal_mode=WAL")
                    cursor.execute("PRAGMA synchronous=NORMAL")
                    cursor.execute("PRAGMA cache_size=10000")
                    cursor.execute("PRAGMA temp_store=MEMORY")
                    cursor.close()
            else:
                # PostgreSQL或其他数据库配置
                self.engine = create_engine(
                    self.database_url,
                    pool_size=10,
                    max_overflow=20,
                    pool_pre_ping=True,
                    echo=False
                )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            logger.info(f"数据库引擎初始化成功: {self.database_url}")
            
        except Exception as e:
            logger.error(f"数据库引擎初始化失败: {e}")
            raise
    
    def create_tables(self):
        """创建所有数据表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据表创建成功")
        except Exception as e:
            logger.error(f"数据表创建失败: {e}")
            raise
    
    def drop_tables(self):
        """删除所有数据表"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("数据表删除成功")
        except Exception as e:
            logger.error(f"数据表删除失败: {e}")
            raise
    
    @contextmanager
    def get_session(self) -> Session:
        """获取数据库会话上下文管理器"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def get_session_factory(self):
        """获取会话工厂"""
        return self.SessionLocal
    
    def execute_sql(self, sql: str, params: Dict[str, Any] = None) -> Any:
        """执行原生SQL"""
        with self.get_session() as session:
            result = session.execute(sql, params or {})
            return result.fetchall()
    
    def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            if self.database_url.startswith('sqlite'):
                # SQLite备份
                db_path = self.database_url.replace('sqlite:///', '')
                import shutil
                shutil.copy2(db_path, backup_path)
                logger.info(f"数据库备份成功: {backup_path}")
                return True
            else:
                # 其他数据库备份需要具体实现
                logger.warning("非SQLite数据库备份需要具体实现")
                return False
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """恢复数据库"""
        try:
            if self.database_url.startswith('sqlite'):
                # SQLite恢复
                db_path = self.database_url.replace('sqlite:///', '')
                import shutil
                shutil.copy2(backup_path, db_path)
                logger.info(f"数据库恢复成功: {backup_path}")
                return True
            else:
                # 其他数据库恢复需要具体实现
                logger.warning("非SQLite数据库恢复需要具体实现")
                return False
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        info = {
            "database_url": self.database_url,
            "engine": str(self.engine),
            "tables": []
        }
        
        try:
            with self.get_session() as session:
                # 获取表信息
                if self.database_url.startswith('sqlite'):
                    result = session.execute(
                        "SELECT name FROM sqlite_master WHERE type='table'"
                    )
                    info["tables"] = [row[0] for row in result.fetchall()]
                
                # 获取数据库大小（SQLite）
                if self.database_url.startswith('sqlite'):
                    db_path = self.database_url.replace('sqlite:///', '')
                    if os.path.exists(db_path):
                        info["size"] = os.path.getsize(db_path)
                
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
        
        return info

# 全局数据库管理器实例
db_manager = DatabaseManager()

def get_db_session():
    """获取数据库会话（依赖注入用）"""
    return db_manager.get_session()

def init_database(database_url: str = None):
    """初始化数据库"""
    global db_manager
    if database_url:
        db_manager = DatabaseManager(database_url)
    
    # 导入所有模型以确保表被创建
    from . import user, product, inventory, order, system
    
    # 创建数据表
    db_manager.create_tables()
    
    logger.info("数据库初始化完成")

if __name__ == "__main__":
    # 测试数据库连接
    init_database()
    print("数据库连接测试成功")
    print(db_manager.get_database_info())
