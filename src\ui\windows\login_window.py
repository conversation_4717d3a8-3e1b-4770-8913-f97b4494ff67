"""
WMS全屏登录窗口 - 优化UI显示，统一风格，支持多种登录方式
© 2024 贵州睿云慧通科技有限公司
"""

import sys
import hashlib
import threading
import time
import random
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QApplication, QCheckBox, QMessageBox, QLabel,
    QLineEdit, QPushButton, QScrollArea, QDialog,
    QProgressBar, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QThread
from PyQt6.QtGui import QFont, QScreen

# 导入安全模块
try:
    from ...security.auth_manager import auth_manager
    from ...security.license_manager import license_manager
    from ..dialogs.license_dialog import LicenseDialog
except ImportError:
    # 如果导入失败，使用备用方案
    auth_manager = None
    license_manager = None
    LicenseDialog = None


class AdminPasswordDialog(QDialog):
    """管理员密码验证对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("系统管理 - 管理员验证")

        # 设置对话框大小 - 黄金比例优化
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()

        # 使用黄金比例 (1:1.618) 计算对话框尺寸
        self.golden_ratio = 1.618
        base_width = max(420, min(520, int(screen_geometry.width() * 0.32)))
        self.dialog_width = base_width
        self.dialog_height = int(base_width / self.golden_ratio)  # 黄金比例高度

        # 确保最小尺寸
        self.dialog_height = max(260, self.dialog_height)

        self.setFixedSize(self.dialog_width, self.dialog_height)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowStaysOnTopHint)
        self.setModal(True)

        # 管理员密码验证
        self.admin_password = "admin123"

        # 如果有安全模块，使用加盐验证
        if auth_manager:
            # 从认证管理器获取管理员密码哈希
            self.use_secure_auth = True
        else:
            self.use_secure_auth = False

        self._setup_ui()

    def _setup_ui(self):
        """设置UI - 黄金比例优化布局"""
        layout = QVBoxLayout(self)

        # 使用黄金比例计算间距和边距
        base_spacing = int(self.dialog_width / (self.golden_ratio * 18))  # 基础间距
        layout.setSpacing(base_spacing)

        # 边距使用黄金比例
        margin = int(self.dialog_width / (self.golden_ratio * 10))
        layout.setContentsMargins(margin, margin, margin, margin)

        # 标题容器 - 黄金比例间距
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_spacing = int(base_spacing / self.golden_ratio)  # 更小的间距
        title_layout.setSpacing(title_spacing)
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 图标 - 黄金比例字体大小
        icon_size = int(self.dialog_width / (self.golden_ratio * 9))  # 基于对话框宽度计算
        icon_label = QLabel("🔐")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: {icon_size}px;
                background-color: transparent;
                padding: {int(icon_size/10)}px;
                margin-bottom: {int(icon_size/12)}px;
            }}
        """)
        title_layout.addWidget(icon_label)

        # 标题 - 黄金比例字体大小
        title_size = int(icon_size / self.golden_ratio)  # 标题字体比图标小
        title_label = QLabel("管理员权限验证")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: #dc2626;
                font-size: {title_size}px;
                font-weight: 700;
                font-family: "Microsoft YaHei UI", sans-serif;
                background-color: transparent;
                margin-bottom: {int(title_size/4)}px;
                letter-spacing: 1px;
            }}
        """)
        title_layout.addWidget(title_label)

        # 说明文字 - 黄金比例字体大小
        info_size = int(title_size / self.golden_ratio)  # 比标题更小
        info_label = QLabel("请输入管理员密码以继续操作\n默认密码：admin123")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet(f"""
            QLabel {{
                color: #64748b;
                font-size: {info_size}px;
                font-family: "Microsoft YaHei UI", sans-serif;
                background-color: transparent;
                margin-bottom: {int(info_size * 0.8)}px;
                line-height: 1.6;
            }}
        """)
        title_layout.addWidget(info_label)

        layout.addWidget(title_container)

        # 密码输入 - 黄金比例优化
        input_height = int(self.dialog_height / (self.golden_ratio * 5))  # 基于对话框高度计算
        input_font_size = int(input_height / 3.2)  # 字体大小与输入框高度成比例
        input_padding = int(input_height / 4)  # 内边距
        input_radius = int(input_height / 4)  # 圆角半径
        border_width = max(2, int(self.dialog_width / 150))  # 边框宽度

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入管理员密码")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setFixedHeight(input_height)
        self.password_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: #f8fafc;
                border: {border_width}px solid #e2e8f0;
                border-radius: {input_radius}px;
                padding: {input_padding}px {int(input_padding * 1.3)}px;
                font-size: {input_font_size}px;
                font-family: "Microsoft YaHei UI", sans-serif;
                color: #1e293b;
                font-weight: 500;
            }}
            QLineEdit:focus {{
                border-color: #dc2626;
                background-color: white;
                border-width: 3px;
            }}
            QLineEdit::placeholder {{
                color: #94a3b8;
            }}
        """)
        self.password_input.returnPressed.connect(self._verify_password)
        layout.addWidget(self.password_input)

        # 按钮 - 黄金比例优化
        button_layout = QHBoxLayout()
        button_spacing = int(self.dialog_width / (self.golden_ratio * 12))  # 按钮间距
        button_layout.setSpacing(button_spacing)

        # 按钮尺寸计算
        button_height = int(input_height * 0.85)  # 比输入框稍小
        button_width = int(self.dialog_width / (self.golden_ratio * 2.8))  # 按钮宽度
        button_font_size = int(button_height / 3)  # 按钮字体大小
        button_radius = int(button_height / 4.5)  # 按钮圆角
        button_padding = int(button_height / 6)  # 按钮内边距

        cancel_btn = QPushButton("取消")
        cancel_btn.setFixedSize(button_width, button_height)
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6b7280, stop:1 #4b5563);
                color: white;
                border: none;
                border-radius: {button_radius}px;
                padding: {button_padding}px;
                font-size: {button_font_size}px;
                font-weight: 600;
                font-family: "Microsoft YaHei UI", sans-serif;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4b5563, stop:1 #374151);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #374151, stop:1 #1f2937);
            }}
        """)
        cancel_btn.clicked.connect(self.reject)

        verify_btn = QPushButton("验证")
        verify_btn.setFixedSize(button_width, button_height)
        verify_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626, stop:1 #b91c1c);
                color: white;
                border: none;
                border-radius: {button_radius}px;
                padding: {button_padding}px;
                font-size: {button_font_size}px;
                font-weight: 600;
                font-family: "Microsoft YaHei UI", sans-serif;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #b91c1c, stop:1 #991b1b);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #991b1b, stop:1 #7f1d1d);
            }}
        """)
        verify_btn.clicked.connect(self._verify_password)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(verify_btn)
        layout.addLayout(button_layout)

        # 对话框样式 - 黄金比例优化
        dialog_radius = int(self.dialog_width / (self.golden_ratio * 18))  # 对话框圆角
        border_width = max(2, int(self.dialog_width / 140))  # 边框宽度

        self.setStyleSheet(f"""
            AdminPasswordDialog {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(254, 242, 242, 0.98),
                    stop:0.3 rgba(255, 255, 255, 0.95),
                    stop:0.7 rgba(254, 202, 202, 0.92),
                    stop:1 rgba(252, 165, 165, 0.88));
                border-radius: {dialog_radius}px;
                border: {border_width}px solid #dc2626;
            }}
        """)

        # 设置焦点
        self.password_input.setFocus()
    
    def _verify_password(self):
        """验证密码"""
        password = self.password_input.text().strip()

        if self.use_secure_auth and auth_manager:
            # 使用安全认证
            is_valid, result = auth_manager.authenticate_user("admin", password)
            if is_valid:
                self.accept()
            else:
                error_msg = result.get('error', '密码错误')
                self._show_error(error_msg)
                self.password_input.clear()
                self.password_input.setFocus()
        else:
            # 使用传统验证
            if password == self.admin_password:
                self.accept()
            else:
                self._show_error("密码错误，请重试")
                self.password_input.clear()
                self.password_input.setFocus()

    def _show_error(self, message):
        """显示错误信息 - 黄金比例优化"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("验证失败")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Warning)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

        # 使用黄金比例计算消息框样式
        msg_font_size = int(self.dialog_width / (self.golden_ratio * 22))  # 消息框字体大小
        btn_height = int(self.dialog_height / (self.golden_ratio * 6))  # 按钮高度
        btn_width = int(self.dialog_width / (self.golden_ratio * 4))  # 按钮宽度
        btn_radius = int(btn_height / 4)  # 按钮圆角

        # 优化消息框样式
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: white;
                border-radius: {int(self.dialog_width / (self.golden_ratio * 25))}px;
                font-size: {msg_font_size}px;
                font-family: "Microsoft YaHei UI", sans-serif;
            }}
            QMessageBox QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626, stop:1 #b91c1c);
                color: white;
                border: none;
                border-radius: {btn_radius}px;
                padding: {int(btn_height/6)}px {int(btn_width/8)}px;
                font-size: {int(msg_font_size * 0.9)}px;
                font-weight: 600;
                font-family: "Microsoft YaHei UI", sans-serif;
                min-width: {btn_width}px;
                min-height: {btn_height}px;
            }}
            QMessageBox QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #b91c1c, stop:1 #991b1b);
            }}
        """)
        msg_box.exec()


class DeviceSimulator(QThread):
    """设备模拟器 - 模拟读卡器、扫码枪、人脸识别设备"""
    
    card_detected = pyqtSignal(str, str)  # 卡号, 用户名
    qr_detected = pyqtSignal(str, str)    # 二维码, 用户名
    face_detected = pyqtSignal(str, str)  # 人脸ID, 用户名
    
    def __init__(self):
        super().__init__()
        self.running = False
        self.current_mode = "password"
        
        # 模拟用户数据库
        self.users_db = {
            "CARD001": {"name": "张三", "role": "操作员", "department": "仓储部"},
            "CARD002": {"name": "李四", "role": "管理员", "department": "管理部"},
            "QR001": {"name": "王五", "role": "操作员", "department": "物流部"},
            "QR002": {"name": "赵六", "role": "主管", "department": "仓储部"},
            "FACE001": {"name": "钱七", "role": "操作员", "department": "仓储部"},
            "FACE002": {"name": "孙八", "role": "管理员", "department": "管理部"},
        }
    
    def set_mode(self, mode):
        """设置监听模式"""
        self.current_mode = mode
        print(f"🔄 切换到 {mode} 模式")
    
    def start_listening(self):
        """开始监听"""
        self.running = True
        if not self.isRunning():
            self.start()
    
    def stop_listening(self):
        """停止监听"""
        self.running = False
        if self.isRunning():
            self.quit()
            self.wait()
    
    def run(self):
        """监听线程主循环"""
        while self.running:
            try:
                if self.current_mode == "rfid":
                    self._simulate_card_reader()
                elif self.current_mode == "qr":
                    self._simulate_qr_scanner()
                elif self.current_mode == "face":
                    self._simulate_face_recognition()
                
                time.sleep(0.5)  # 500ms检测间隔
            except Exception as e:
                print(f"设备监听错误: {e}")
                break
    
    def _simulate_card_reader(self):
        """模拟读卡器"""
        if random.random() < 0.02:  # 2%概率检测到卡片
            card_ids = [k for k in self.users_db.keys() if k.startswith("CARD")]
            if card_ids:
                card_id = random.choice(card_ids)
                user_info = self.users_db[card_id]
                print(f"📱 检测到卡片: {card_id} - {user_info['name']}")
                self.card_detected.emit(card_id, user_info['name'])
    
    def _simulate_qr_scanner(self):
        """模拟扫码枪"""
        if random.random() < 0.015:  # 1.5%概率扫描到二维码
            qr_ids = [k for k in self.users_db.keys() if k.startswith("QR")]
            if qr_ids:
                qr_id = random.choice(qr_ids)
                user_info = self.users_db[qr_id]
                print(f"📷 扫描到二维码: {qr_id} - {user_info['name']}")
                self.qr_detected.emit(qr_id, user_info['name'])
    
    def _simulate_face_recognition(self):
        """模拟人脸识别"""
        if random.random() < 0.01:  # 1%概率识别到人脸
            face_ids = [k for k in self.users_db.keys() if k.startswith("FACE")]
            if face_ids:
                face_id = random.choice(face_ids)
                user_info = self.users_db[face_id]
                print(f"👤 识别到人脸: {face_id} - {user_info['name']}")
                self.face_detected.emit(face_id, user_info['name'])


class LoginWindow(QMainWindow):
    """WMS全屏登录窗口 - 兼容main.py"""

    # 信号定义 - 兼容main.py
    login_success = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.current_login_mode = "password"
        self._setup_ui()
        self._setup_device_simulator()
    
    def _setup_ui(self):
        """设置UI"""
        self.setWindowTitle("WMS客户端系统 - 登录")
        
        # 获取屏幕信息并设置全屏 - 参照4卡片窗口
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()

        print(f"📺 屏幕分辨率: {screen_geometry.width()} x {screen_geometry.height()}")

        # 设置窗口为全屏 - 与4卡片窗口保持一致
        self.setGeometry(screen_geometry)
        self.showFullScreen()

        # 设置窗口标志 - 无边框，置顶
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 设置与4卡片窗口一致的背景样式
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:0.5 #2563eb, stop:1 #3b82f6);
            }
        """)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        self._create_left_area(main_layout, screen_geometry)
        self._create_right_area(main_layout, screen_geometry)
    
    def _setup_device_simulator(self):
        """设置设备模拟器"""
        self.device_simulator = DeviceSimulator()
        self.device_simulator.card_detected.connect(self._handle_card_login)
        self.device_simulator.qr_detected.connect(self._handle_qr_login)
        self.device_simulator.face_detected.connect(self._handle_face_login)
        self.device_simulator.start_listening()
    
    def _create_left_area(self, main_layout, screen_geometry):
        """创建左侧装饰区域"""
        left_widget = QWidget()
        
        # 左侧占屏幕宽度的65%
        left_width = int(screen_geometry.width() * 0.65)
        left_widget.setMinimumWidth(left_width)
        
        left_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:0.5 #2563eb, stop:1 #3b82f6);
                border-top-right-radius: 60px;
                border-bottom-right-radius: 60px;
            }
        """)
        
        # 内容布局
        content_layout = QVBoxLayout(left_widget)
        
        # 根据屏幕尺寸动态调整边距
        margin = max(40, int(screen_geometry.height() * 0.06))
        content_layout.setContentsMargins(margin, margin, margin, margin)
        content_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_layout.setSpacing(max(25, int(screen_geometry.height() * 0.03)))
        
        # 根据屏幕尺寸动态调整字体大小
        title_font_size = max(32, int(screen_geometry.height() * 0.045))
        subtitle_font_size = max(18, int(screen_geometry.height() * 0.025))
        
        # 大标题
        title_label = QLabel("智能仓储管理系统")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: white;
                font-size: {title_font_size}px;
                font-weight: bold;
                background-color: transparent;
                padding: 10px;
                border: none;
            }}
        """)
        content_layout.addWidget(title_label)
        
        # 副标题
        subtitle_label = QLabel("现代化 · 智能化 · 自动化")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                color: rgba(255,255,255,0.95);
                font-size: {subtitle_font_size}px;
                font-weight: 300;
                background-color: transparent;
                padding: 15px;
                border: none;
            }}
        """)
        content_layout.addWidget(subtitle_label)
        
        # 特性列表
        features_container = QWidget()
        features_container.setStyleSheet("""
            QWidget {
                background-color: rgba(255,255,255,0.15);
                border-radius: 20px;
                border: 2px solid rgba(255,255,255,0.25);
                margin: 15px 0px;
            }
        """)
        
        features_layout = QVBoxLayout(features_container)
        features_layout.setSpacing(12)
        features_layout.setContentsMargins(20, 20, 20, 20)
        
        features = [
            "🔐 多重身份认证系统",
            "📦 智能库存管理", 
            "🚀 自助出入库操作",
            "📊 实时数据分析报表"
        ]
        
        feature_font_size = max(14, int(screen_geometry.height() * 0.018))
        
        for feature in features:
            feature_label = QLabel(feature)
            feature_label.setStyleSheet(f"""
                QLabel {{
                    color: rgba(255,255,255,0.9);
                    font-size: {feature_font_size}px;
                    font-weight: 400;
                    padding: 6px 10px;
                    background-color: transparent;
                    border: none;
                }}
            """)
            features_layout.addWidget(feature_label)
        
        content_layout.addWidget(features_container)
        main_layout.addWidget(left_widget)
    
    def _create_right_area(self, main_layout, screen_geometry):
        """创建右侧登录区域"""
        right_widget = QWidget()
        
        # 右侧占屏幕宽度的35%
        right_width = int(screen_geometry.width() * 0.35)
        right_widget.setFixedWidth(right_width)
        
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(30, 30, 30, 30)
        right_layout.setSpacing(0)
        
        # 顶部按钮区域
        top_layout = QHBoxLayout()

        # 授权管理按钮
        if license_manager:
            license_btn = QPushButton("🔐 授权")
            license_btn.setFixedSize(70, 35)
            license_btn.setStyleSheet("""
                QPushButton {
                    background-color: rgba(59, 130, 246, 0.8);
                    color: white;
                    border: none;
                    font-size: 12px;
                    font-weight: bold;
                    border-radius: 6px;
                    padding: 5px 10px;
                }
                QPushButton:hover {
                    background-color: rgba(37, 99, 235, 0.9);
                }
            """)
            license_btn.clicked.connect(self._show_license_dialog)
            top_layout.addWidget(license_btn)

        top_layout.addStretch()

        # 退出按钮
        exit_btn = QPushButton("退出系统")
        exit_btn.setFixedSize(80, 35)
        exit_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(220, 38, 38, 0.8);
                color: white;
                border: none;
                font-size: 12px;
                font-weight: bold;
                border-radius: 6px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: rgba(185, 28, 28, 0.9);
            }
        """)
        exit_btn.clicked.connect(self._request_exit)
        top_layout.addWidget(exit_btn)

        right_layout.addLayout(top_layout)
        
        # 添加弹性空间
        right_layout.addStretch(1)
        
        # 登录区域
        self._create_login_area(right_layout, screen_geometry)
        
        # 添加弹性空间
        right_layout.addStretch(1)
        
        main_layout.addWidget(right_widget)
    
    def _create_login_area(self, parent_layout, screen_geometry):
        """创建登录区域"""
        # 登录容器
        login_container = QWidget()
        login_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:1 rgba(248, 250, 252, 0.95));
                border-radius: 24px;
                border: 3px solid rgba(255, 255, 255, 0.4);
            }
        """)
        
        # 根据屏幕尺寸动态调整登录容器大小
        container_width = min(450, int(screen_geometry.width() * 0.28))
        container_height = min(600, int(screen_geometry.height() * 0.7))
        login_container.setFixedSize(container_width, container_height)
        
        login_layout = QVBoxLayout(login_container)
        login_layout.setSpacing(12)
        login_layout.setContentsMargins(25, 25, 25, 25)
        
        # WMS标题
        wms_label = QLabel("WMS")
        wms_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        wms_label.setStyleSheet(f"""
            QLabel {{
                color: #1e40af;
                font-size: {max(36, int(screen_geometry.height() * 0.04))}px;
                font-weight: bold;
                background-color: transparent;
                border: none;
                margin-bottom: 5px;
            }}
        """)
        login_layout.addWidget(wms_label)
        
        # 系统名称
        system_label = QLabel("库房自助出入库客户端系统")
        system_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        system_label.setStyleSheet(f"""
            QLabel {{
                color: #475569;
                font-size: {max(13, int(screen_geometry.height() * 0.015))}px;
                font-weight: 400;
                background-color: transparent;
                border: none;
                margin-bottom: 10px;
            }}
        """)
        login_layout.addWidget(system_label)
        
        # 登录方式选项卡
        self._create_login_tabs(login_layout)
        
        # 登录内容区域
        self._create_login_content(login_layout)
        
        # 版权信息
        copyright_label = QLabel("© 2024 贵州睿云慧通科技有限公司\n版权所有 · 保留所有权利")
        copyright_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: #64748b;
                font-size: 10px;
                font-weight: 300;
                background-color: transparent;
                border: none;
                margin-top: 10px;
            }
        """)
        login_layout.addWidget(copyright_label)
        
        parent_layout.addWidget(login_container, 0, Qt.AlignmentFlag.AlignCenter)

    def _create_login_tabs(self, parent_layout):
        """创建登录方式选项卡"""
        tabs_container = QWidget()
        tabs_layout = QHBoxLayout(tabs_container)
        tabs_layout.setSpacing(0)
        tabs_layout.setContentsMargins(0, 0, 0, 0)

        self.tab_buttons = {}
        tabs = [
            ("password", "账号登录", "#2563eb"),
            ("qr", "扫码登录", "#dc2626"),
            ("face", "人脸识别", "#7c3aed")
        ]

        for i, (tab_id, tab_name, color) in enumerate(tabs):
            btn = QPushButton(tab_name)
            btn.setCheckable(True)
            btn.setChecked(tab_id == "password")
            btn.clicked.connect(lambda checked, tid=tab_id: self._switch_login_mode(tid))
            btn.setFixedHeight(35)

            # 设置圆角样式
            border_radius = ""
            if i == 0:  # 第一个按钮
                border_radius = "border-top-left-radius: 8px; border-bottom-left-radius: 8px;"
            elif i == len(tabs) - 1:  # 最后一个按钮
                border_radius = "border-top-right-radius: 8px; border-bottom-right-radius: 8px;"

            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: #f1f5f9;
                    color: #64748b;
                    border: 1px solid #e2e8f0;
                    padding: 8px 12px;
                    font-size: 12px;
                    font-weight: 500;
                    {border_radius}
                }}
                QPushButton:checked {{
                    background-color: {color};
                    color: white;
                    border-color: {color};
                }}
                QPushButton:hover {{
                    background-color: #e2e8f0;
                }}
                QPushButton:checked:hover {{
                    background-color: {color};
                    opacity: 0.9;
                }}
            """)

            self.tab_buttons[tab_id] = btn
            tabs_layout.addWidget(btn)

        parent_layout.addWidget(tabs_container)

    def _create_login_content(self, parent_layout):
        """创建登录内容区域"""
        # 内容容器
        self.content_container = QWidget()
        content_layout = QVBoxLayout(self.content_container)
        content_layout.setSpacing(15)
        content_layout.setContentsMargins(0, 10, 0, 10)

        # 密码登录内容
        self.password_content = self._create_password_content()
        content_layout.addWidget(self.password_content)

        # 扫码登录内容
        self.qr_content = self._create_qr_content()
        self.qr_content.hide()
        content_layout.addWidget(self.qr_content)

        # 人脸识别内容
        self.face_content = self._create_face_content()
        self.face_content.hide()
        content_layout.addWidget(self.face_content)

        parent_layout.addWidget(self.content_container)

    def _create_password_content(self):
        """创建密码登录内容"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(12)
        layout.setContentsMargins(0, 0, 0, 0)

        # 用户名输入
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("用户名")
        self.username_input.setText("admin")
        self.username_input.setFixedHeight(40)
        self.username_input.setStyleSheet("""
            QLineEdit {
                background-color: #f8fafc;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                color: #1e293b;
            }
            QLineEdit:focus {
                border-color: #2563eb;
                background-color: white;
            }
        """)
        layout.addWidget(self.username_input)

        # 密码输入
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setFixedHeight(40)
        self.password_input.setStyleSheet("""
            QLineEdit {
                background-color: #f8fafc;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                color: #1e293b;
            }
            QLineEdit:focus {
                border-color: #2563eb;
                background-color: white;
            }
        """)
        layout.addWidget(self.password_input)

        # 登录按钮
        login_btn = QPushButton("登录")
        login_btn.setFixedHeight(42)
        login_btn.setStyleSheet("""
            QPushButton {
                background-color: #2563eb;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 15px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #1d4ed8;
            }
        """)
        login_btn.clicked.connect(lambda: self._handle_password_login(self.username_input.text(), self.password_input.text()))
        layout.addWidget(login_btn)

        return widget



    def _create_qr_content(self):
        """创建扫码登录内容"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(0, 0, 0, 0)

        # 二维码图标和提示
        icon_label = QLabel("📷")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #dc2626;
                background-color: transparent;
                padding: 20px;
            }
        """)
        layout.addWidget(icon_label)

        # 提示文字
        tip_label = QLabel("请使用扫码枪扫描二维码")
        tip_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        tip_label.setStyleSheet("""
            QLabel {
                color: #dc2626;
                font-size: 16px;
                font-weight: 500;
                background-color: transparent;
                padding: 10px;
            }
        """)
        layout.addWidget(tip_label)

        # 状态显示
        self.qr_status = QLabel("等待扫码...")
        self.qr_status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.qr_status.setStyleSheet("""
            QLabel {
                color: #64748b;
                font-size: 14px;
                background-color: #fef2f2;
                border: 1px solid #fecaca;
                border-radius: 6px;
                padding: 10px;
                margin: 10px 0px;
            }
        """)
        layout.addWidget(self.qr_status)

        return widget

    def _create_face_content(self):
        """创建人脸识别内容"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(0, 0, 0, 0)

        # 人脸图标和提示
        icon_label = QLabel("👤")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #7c3aed;
                background-color: transparent;
                padding: 20px;
            }
        """)
        layout.addWidget(icon_label)

        # 提示文字
        tip_label = QLabel("请面向摄像头进行人脸识别")
        tip_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        tip_label.setStyleSheet("""
            QLabel {
                color: #7c3aed;
                font-size: 16px;
                font-weight: 500;
                background-color: transparent;
                padding: 10px;
            }
        """)
        layout.addWidget(tip_label)

        # 状态显示
        self.face_status = QLabel("等待人脸识别...")
        self.face_status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.face_status.setStyleSheet("""
            QLabel {
                color: #64748b;
                font-size: 14px;
                background-color: #faf5ff;
                border: 1px solid #e9d5ff;
                border-radius: 6px;
                padding: 10px;
                margin: 10px 0px;
            }
        """)
        layout.addWidget(self.face_status)

        return widget

    def _switch_login_mode(self, mode):
        """切换登录模式"""
        self.current_login_mode = mode

        # 更新按钮状态
        for tab_id, btn in self.tab_buttons.items():
            btn.setChecked(tab_id == mode)

        # 显示对应内容
        self.password_content.setVisible(mode == "password")
        self.qr_content.setVisible(mode == "qr")
        self.face_content.setVisible(mode == "face")

        # 设置设备监听模式
        self.device_simulator.set_mode(mode)

        # 重置状态
        if mode == "qr":
            self.qr_status.setText("等待扫码...")
        elif mode == "face":
            self.face_status.setText("等待人脸识别...")

    def _handle_password_login(self, username, password):
        """处理密码登录 - 使用安全认证"""
        if auth_manager and license_manager:
            # 使用安全认证管理器
            is_valid, result = auth_manager.authenticate_user(username, password)

            if is_valid:
                user_info = {
                    "id": result['user']['username'],
                    "name": result['user']['full_name'],
                    "role": result['user']['role'],
                    "department": result['user']['department'],
                    "login_type": "密码登录",
                    "permissions": result['user']['permissions'],
                    "session_token": result['session_token'],
                    "license_info": result['license_info']
                }
                self._show_login_success(user_info)
            else:
                error_msg = result.get('error', '登录失败')
                self._show_login_error(error_msg)
        else:
            # 备用方案：使用传统设备账号验证
            device_accounts = {
                "admin": "admin123",       # 管理员账号
                "device01": "123456",      # 设备1号用户
                "device02": "123456",      # 设备2号用户
                "operator": "op123456",    # 操作员账号
                "warehouse": "wh123456",   # 仓库员账号
                "guest": "guest123"        # 访客账号
            }

            if username in device_accounts and device_accounts[username] == password:
                user_info = {
                    "id": username,
                    "name": self._get_device_user_display_name(username),
                    "role": self._get_device_user_role(username),
                    "department": "仓储部",
                    "login_type": "密码登录"
                }
                self._show_login_success(user_info)
            else:
                self._show_login_error("设备用户名或密码错误")

    def _get_device_user_display_name(self, username):
        """获取设备用户显示名称"""
        name_mapping = {
            "admin": "系统管理员",
            "device01": "设备1号用户",
            "device02": "设备2号用户",
            "operator": "仓库操作员",
            "warehouse": "仓库管理员",
            "guest": "访客用户"
        }
        return name_mapping.get(username, f"设备用户({username})")

    def _get_device_user_role(self, username):
        """获取设备用户角色"""
        role_mapping = {
            "admin": "管理员",
            "device01": "设备用户",
            "device02": "设备用户",
            "operator": "操作员",
            "warehouse": "仓库员",
            "guest": "访客"
        }
        return role_mapping.get(username, "设备用户")

    def _handle_card_login(self, card_id, username):
        """处理卡片登录 - 已禁用"""
        pass

    def _handle_qr_login(self, qr_id, username):
        """处理扫码登录"""
        if self.current_login_mode != "qr":
            return

        self.qr_status.setText(f"扫描到二维码: {qr_id}")

        # 模拟验证过程
        QTimer.singleShot(1000, lambda: self._complete_qr_login(qr_id, username))

    def _complete_qr_login(self, qr_id, username):
        """完成扫码登录"""
        user_info = self.device_simulator.users_db.get(qr_id)
        if user_info:
            login_info = {
                "id": qr_id,
                "name": user_info["name"],
                "role": user_info["role"],
                "department": user_info["department"],
                "login_type": "扫码登录"
            }
            self.qr_status.setText(f"验证成功: {username}")
            QTimer.singleShot(500, lambda: self._show_login_success(login_info))
        else:
            self.qr_status.setText("二维码验证失败")
            QTimer.singleShot(2000, lambda: self.qr_status.setText("等待扫码..."))

    def _handle_face_login(self, face_id, username):
        """处理人脸登录"""
        if self.current_login_mode != "face":
            return

        self.face_status.setText(f"识别到人脸: {username}")

        # 模拟验证过程
        QTimer.singleShot(1500, lambda: self._complete_face_login(face_id, username))

    def _complete_face_login(self, face_id, username):
        """完成人脸登录"""
        user_info = self.device_simulator.users_db.get(face_id)
        if user_info:
            login_info = {
                "id": face_id,
                "name": user_info["name"],
                "role": user_info["role"],
                "department": user_info["department"],
                "login_type": "人脸识别"
            }
            self.face_status.setText(f"验证成功: {username}")
            QTimer.singleShot(500, lambda: self._show_login_success(login_info))
        else:
            self.face_status.setText("人脸识别失败")
            QTimer.singleShot(2000, lambda: self.face_status.setText("等待人脸识别..."))

    def _show_login_success(self, user_info):
        """显示登录成功并发送信号"""
        print(f"✅ 用户 {user_info['name']} 通过 {user_info['login_type']} 登录成功")

        # 停止设备监听
        self.device_simulator.stop_listening()

        # 转换用户信息格式以兼容main.py
        login_data = {
            "id": user_info.get('id', user_info['name']),
            "username": user_info.get('username', user_info['name']),
            "full_name": user_info['name'],
            "role": user_info['role'],
            "department": user_info.get('department', ''),
            "login_type": user_info['login_type']
        }

        # 设置标志位，允许正常关闭
        self._login_success = True

        # 发送登录成功信号
        print(f"🔄 发送登录成功信号: {login_data}")
        self.login_success.emit(login_data)

        # 关闭登录窗口
        print("🔄 准备关闭登录窗口...")
        self.close()

    def _show_login_error(self, message):
        """显示登录错误"""
        # 创建自定义登录错误对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("登录失败")
        dialog.setFixedSize(400, 300)  # 增大对话框尺寸
        dialog.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowStaysOnTopHint)
        dialog.setModal(True)

        # 设置对话框样式
        dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #fef2f2, stop:0.5 #fee2e2, stop:1 #fecaca);
                border-radius: 15px;
                border: 2px solid #dc2626;
            }
        """)

        layout = QVBoxLayout(dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # 错误图标和标题
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setSpacing(10)
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 错误图标
        icon_label = QLabel("❌")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                background-color: transparent;
                padding: 10px;
            }
        """)
        title_layout.addWidget(icon_label)

        # 标题
        title_label = QLabel("登录失败")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #dc2626;
                font-size: 20px;
                font-weight: bold;
                background-color: transparent;
                margin-bottom: 10px;
            }
        """)
        title_layout.addWidget(title_label)

        layout.addWidget(title_container)

        # 错误信息
        error_container = QWidget()
        error_container.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 10px;
                border: 1px solid rgba(220, 38, 38, 0.2);
            }
        """)

        error_layout = QVBoxLayout(error_container)
        error_layout.setContentsMargins(20, 15, 20, 15)

        error_label = QLabel(message)
        error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        error_label.setWordWrap(True)  # 允许文字换行
        error_label.setStyleSheet("""
            QLabel {
                color: #dc2626;
                font-size: 14px;
                font-weight: 500;
                background-color: transparent;
                padding: 15px;
                line-height: 1.5;
            }
        """)
        error_layout.addWidget(error_label)

        layout.addWidget(error_container)

        # 确定按钮
        ok_button = QPushButton("确定")
        ok_button.setFixedHeight(40)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #dc2626;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 16px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #b91c1c;
            }
            QPushButton:pressed {
                background-color: #991b1b;
            }
        """)
        ok_button.clicked.connect(dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 显示对话框
        dialog.exec()

    def _show_license_dialog(self):
        """显示授权管理对话框"""
        if LicenseDialog:
            dialog = LicenseDialog(self)
            dialog.exec()
        else:
            QMessageBox.information(
                self,
                "授权管理",
                "授权管理功能暂不可用"
            )

    def _request_exit(self):
        """请求退出系统 - 需要管理员验证"""
        dialog = AdminPasswordDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            print("🔐 管理员验证成功，正在退出系统...")
            # 关闭登录窗口，让主程序处理退出逻辑
            self.close()
        else:
            print("❌ 管理员验证失败，取消退出操作")

    def closeEvent(self, event):
        """重写关闭事件"""
        # 停止设备监听
        if hasattr(self, 'device_simulator'):
            self.device_simulator.stop_listening()

        # 如果是登录成功后的正常关闭，直接接受
        if hasattr(self, '_login_success') and self._login_success:
            event.accept()
            return

        # 否则需要管理员验证
        dialog = AdminPasswordDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            print("🔐 管理员验证成功，正在退出系统...")
            event.accept()
        else:
            print("❌ 管理员验证失败，取消退出操作")
            event.ignore()

    def keyPressEvent(self, event):
        """重写按键事件 - 禁用退出快捷键"""
        if event.key() == Qt.Key.Key_F4 and event.modifiers() == Qt.KeyboardModifier.AltModifier:
            self._request_exit()
            return
        elif event.key() == Qt.Key.Key_Escape:
            self._request_exit()
            return

        super().keyPressEvent(event)


def main():
    """主函数"""
    print("=" * 80)
    print("🏢 WMS库房自助出入库客户端系统")
    print("© 2024 贵州睿云慧通科技有限公司")
    print("版本: 1.0.0")
    print("=" * 80)

    app = QApplication(sys.argv)
    app.setApplicationName("WMS登录系统")

    # 获取屏幕信息
    screen = app.primaryScreen()
    screen_geometry = screen.geometry()
    print(f"📺 检测到屏幕分辨率: {screen_geometry.width()} x {screen_geometry.height()}")
    print(f"🔐 管理员密码: admin123 (退出系统需要验证)")
    print(f"👤 登录账号: admin / admin123")
    print("📱 模拟设备: 读卡器、扫码枪、人脸识别")
    print("🎯 切换登录方式可体验不同的设备监听")
    print("=" * 80)

    window = WMSFullScreenLogin()
    window.show()

    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
