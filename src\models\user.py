"""
用户相关数据模型
© 2024 贵州睿云慧通科技有限公司
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from werkzeug.security import generate_password_hash, check_password_hash
from .database import Base
import enum

class UserRole(enum.Enum):
    """用户角色枚举"""
    ADMIN = "admin"          # 管理员
    MANAGER = "manager"      # 经理
    OPERATOR = "operator"    # 操作员
    VISITOR = "visitor"      # 访客

class User(Base):
    """用户表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    email = Column(String(100), unique=True)
    full_name = Column(String(100), nullable=False)
    role = Column(String(20), default=UserRole.OPERATOR.value, nullable=False)
    rfid_card = Column(String(50), unique=True, index=True)
    phone = Column(String(20))
    department = Column(String(100))
    is_active = Column(Boolean, default=True, nullable=False)
    last_login = Column(DateTime)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    identity_card = relationship("IdentityCard", back_populates="user", uselist=False)
    face_records = relationship("FaceRecord", back_populates="user")
    inbound_orders = relationship("InboundOrder", back_populates="user")
    outbound_orders = relationship("OutboundOrder", back_populates="user")
    operation_logs = relationship("OperationLog", back_populates="user")
    
    def set_password(self, password: str):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password: str) -> bool:
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, permission: str) -> bool:
        """检查用户权限"""
        role_permissions = {
            UserRole.ADMIN.value: [
                "user_manage", "system_config", "data_backup",
                "inventory_manage", "order_manage", "report_view"
            ],
            UserRole.MANAGER.value: [
                "inventory_manage", "order_manage", "report_view",
                "user_view"
            ],
            UserRole.OPERATOR.value: [
                "inventory_view", "order_create", "order_view"
            ],
            UserRole.VISITOR.value: [
                "inventory_view"
            ]
        }
        return permission in role_permissions.get(self.role, [])
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "full_name": self.full_name,
            "role": self.role,
            "rfid_card": self.rfid_card,
            "phone": self.phone,
            "department": self.department,
            "is_active": self.is_active,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class IdentityCard(Base):
    """身份证信息表"""
    __tablename__ = "identity_cards"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True)
    id_number = Column(String(18), unique=True, nullable=False, index=True)
    name = Column(String(50), nullable=False)
    gender = Column(String(2))
    birth_date = Column(DateTime)
    address = Column(Text)
    issuing_authority = Column(String(100))
    valid_from = Column(DateTime)
    valid_to = Column(DateTime)
    photo_path = Column(String(255))
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="identity_card")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "id_number": self.id_number,
            "name": self.name,
            "gender": self.gender,
            "birth_date": self.birth_date.isoformat() if self.birth_date else None,
            "address": self.address,
            "issuing_authority": self.issuing_authority,
            "valid_from": self.valid_from.isoformat() if self.valid_from else None,
            "valid_to": self.valid_to.isoformat() if self.valid_to else None,
            "photo_path": self.photo_path
        }

class FaceRecord(Base):
    """人脸记录表"""
    __tablename__ = "face_records"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    face_encoding = Column(Text, nullable=False)  # 人脸特征编码
    face_image_path = Column(String(255))
    quality_score = Column(Float)  # 人脸质量评分
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="face_records")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "face_image_path": self.face_image_path,
            "quality_score": self.quality_score,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class UserSession(Base):
    """用户会话表"""
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_token = Column(String(255), unique=True, nullable=False)
    login_time = Column(DateTime, default=func.now())
    last_activity = Column(DateTime, default=func.now())
    ip_address = Column(String(45))
    user_agent = Column(Text)
    is_active = Column(Boolean, default=True)
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "session_token": self.session_token,
            "login_time": self.login_time.isoformat() if self.login_time else None,
            "last_activity": self.last_activity.isoformat() if self.last_activity else None,
            "ip_address": self.ip_address,
            "is_active": self.is_active
        }
