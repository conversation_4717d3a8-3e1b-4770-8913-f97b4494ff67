# WMS库房自助出入库客户端系统

<div align="center">

**现代化智能仓储管理系统**

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![PyQt6](https://img.shields.io/badge/PyQt6-6.6+-green.svg)](https://www.riverbankcomputing.com/software/pyqt/)
[![License](https://img.shields.io/badge/License-Commercial-red.svg)](LICENSE)

</div>

## 项目简介

库房自助出入库客户端系统是一套基于Python开发的智能化仓储管理系统，旨在实现无人值守的自助式货物出入库操作，提高库房管理效率，降低人工成本。

## 主要特性

### 🔐 多重身份认证
- 账号密码登录
- RFID卡片登录
- 二维码扫描登录
- 身份证验证登录
- 人脸识别登录
- 基于角色的权限管理

### 📦 智能出入库管理
- 扫码快速入库/出库
- 批量操作支持
- 自动库位分配
- 实时库存更新

### 📊 库存管理与报表
- 实时库存查询
- 库存盘点功能
- 库存预警机制
- 多样化报表生成

### 🖥️ 友好用户界面
- 现代化图形界面
- 触摸屏支持
- 多语言支持
- 响应式设计

### 🤖 智能识别功能
- 身份证自动读取验证
- 人脸识别与活体检测
- 票据二维码/条码识别
- OCR文字自动识别

### 🖨️ 智能打印功能
- 入库/出库小票打印
- 自定义打印模板
- 热敏打印机支持
- 打印队列管理

### 🔧 硬件设备集成
- 条码扫描枪
- RFID读卡器
- 高清摄像头
- 身份证读卡器
- 热敏打印机

## 技术架构

### 核心技术栈
- **开发语言**: Python 3.9+
- **GUI框架**: PyQt6
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **ORM框架**: SQLAlchemy
- **日志管理**: Python logging
- **测试框架**: pytest
- **打包工具**: PyInstaller
- **人脸识别**: face-recognition, dlib
- **OCR识别**: pytesseract, paddleocr
- **二维码识别**: pyzbar, qrcode
- **身份证读取**: pyscard, pycryptodome
- **打印功能**: python-escpos
- **图像处理**: OpenCV, Pillow

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   表示层 (UI)    │    │   业务逻辑层     │    │   数据访问层     │
│                │    │                │    │                │
│ - 主界面        │    │ - 用户管理      │    │ - 数据库连接    │
│ - 入库界面      │◄──►│ - 入库管理      │◄──►│ - 数据模型      │
│ - 出库界面      │    │ - 出库管理      │    │ - 数据操作      │
│ - 查询界面      │    │ - 库存管理      │    │ - 事务管理      │
│ - 设置界面      │    │ - 系统管理      │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 项目结构

```
wms-front/
├── docs/                        # 文档目录
│   ├── requirements.md          # 需求文档
│   ├── technical_specification.md  # 技术规格说明
│   └── development_plan.md      # 开发计划
├── src/                         # 源代码目录
│   ├── core/                    # 核心业务逻辑
│   ├── models/                  # 数据模型
│   ├── ui/                      # 用户界面
│   ├── database/                # 数据库相关
│   ├── utils/                   # 工具函数
│   ├── devices/                 # 硬件设备接口
│   └── services/                # 服务层
├── tests/                       # 测试代码
├── resources/                   # 资源文件
├── data/                        # 数据文件
└── scripts/                     # 脚本文件
```

## 快速开始

### 环境要求
- Python 3.9 或更高版本
- Windows 10/11 操作系统
- 4GB RAM 或更多
- 100GB 可用磁盘空间

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-org/wms-front.git
cd wms-front
```

2. **创建虚拟环境**
```bash
python -m venv venv
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **初始化数据库**
```bash
python scripts/init_data.py
```

5. **运行应用**
```bash
python main.py
```

### 默认登录信息
- **管理员账号**: admin / admin123
- **操作员账号**: operator / op123

## 功能模块

### 1. 用户管理
- ✅ 用户注册与登录
- ✅ 权限管理
- ✅ 个人信息维护
- ✅ 操作历史查询

### 2. 入库管理
- ✅ 扫码入库
- ✅ 手动录入
- ✅ 批量入库
- ✅ 入库单据生成

### 3. 出库管理
- ✅ 扫码出库
- ✅ 按订单出库
- ✅ 库存检查
- ✅ 出库单据生成

### 4. 库存管理
- ✅ 实时库存查询
- ✅ 库存盘点
- ✅ 库存预警
- ✅ 报表生成

### 5. 系统管理
- ✅ 基础数据管理
- ✅ 系统配置
- ✅ 设备管理
- ✅ 数据备份

## 开发指南

### 代码规范
- 遵循 PEP 8 Python 代码规范
- 使用类型提示 (Type Hints)
- 编写详细的文档字符串
- 保持代码简洁和可读性

### 测试
```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_core/

# 生成测试覆盖率报告
pytest --cov=src tests/
```

### 构建发布版本
```bash
# 构建可执行文件
python scripts/build.py

# 创建安装包
python scripts/package.py
```

## 部署说明

### 开发环境部署
1. 按照"快速开始"步骤安装
2. 使用SQLite数据库
3. 启用调试模式
4. 使用测试数据

### 生产环境部署
1. 配置PostgreSQL数据库
2. 关闭调试模式
3. 配置日志轮转
4. 设置自动备份
5. 配置硬件设备

详细部署指南请参考 [部署文档](docs/deployment_guide.md)

## 文档

- [需求规格说明](docs/requirements.md)
- [技术规格说明](docs/technical_specification.md)
- [开发计划](docs/development_plan.md)
- [用户操作手册](docs/user_manual.md)
- [API文档](docs/api_documentation.md)

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目维护者: [Your Name](mailto:<EMAIL>)
- 项目主页: [https://github.com/your-org/wms-front](https://github.com/your-org/wms-front)
- 问题反馈: [Issues](https://github.com/your-org/wms-front/issues)

## 更新日志

### v1.0.0 (计划中)
- ✅ 完成需求分析和系统设计
- 🔄 技术选型与环境搭建
- ⏳ 数据库设计与实现
- ⏳ 用户界面开发
- ⏳ 核心功能开发
- ⏳ 系统集成与测试

### 图标说明
- ✅ 已完成
- 🔄 进行中  
- ⏳ 计划中
- ❌ 已取消

## 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**注意**: 这是一个正在开发中的项目，某些功能可能尚未完全实现。请查看开发计划了解最新进展。
