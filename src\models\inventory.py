"""
库存相关数据模型
© 2024 贵州睿云慧通科技有限公司
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Numeric, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
import enum

class LocationType(enum.Enum):
    """库位类型枚举"""
    WAREHOUSE = "warehouse"    # 仓库
    ZONE = "zone"             # 区域
    AISLE = "aisle"           # 通道
    RACK = "rack"             # 货架
    SHELF = "shelf"           # 货位
    BIN = "bin"               # 储位

class Location(Base):
    """库位表"""
    __tablename__ = "locations"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(50), unique=True, nullable=False, index=True)
    name = Column(String(200), nullable=False)
    type = Column(String(20), default=LocationType.BIN.value)
    parent_id = Column(Integer, ForeignKey("locations.id"))
    warehouse_id = Column(Integer, ForeignKey("warehouses.id"))
    zone = Column(String(50))      # 区域
    aisle = Column(String(50))     # 通道
    rack = Column(String(50))      # 货架
    shelf = Column(String(50))     # 层
    position = Column(String(50))  # 位置
    barcode = Column(String(100), unique=True)
    capacity = Column(Integer)     # 容量
    max_weight = Column(Numeric(10, 3))  # 最大承重(kg)
    dimensions = Column(String(50))      # 尺寸
    temperature_controlled = Column(Boolean, default=False)  # 温控
    humidity_controlled = Column(Boolean, default=False)     # 湿度控制
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 自关联关系
    parent = relationship("Location", remote_side=[id], back_populates="children")
    children = relationship("Location", back_populates="parent")
    
    # 其他关联关系
    warehouse = relationship("Warehouse", back_populates="locations")
    inventory_items = relationship("Inventory", back_populates="location")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "code": self.code,
            "name": self.name,
            "type": self.type,
            "parent_id": self.parent_id,
            "warehouse_id": self.warehouse_id,
            "zone": self.zone,
            "aisle": self.aisle,
            "rack": self.rack,
            "shelf": self.shelf,
            "position": self.position,
            "barcode": self.barcode,
            "capacity": self.capacity,
            "max_weight": float(self.max_weight) if self.max_weight else None,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class Warehouse(Base):
    """仓库表"""
    __tablename__ = "warehouses"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(200), nullable=False)
    address = Column(Text)
    manager = Column(String(100))
    phone = Column(String(20))
    email = Column(String(100))
    area = Column(Numeric(10, 2))  # 面积(平方米)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    locations = relationship("Location", back_populates="warehouse")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "code": self.code,
            "name": self.name,
            "address": self.address,
            "manager": self.manager,
            "phone": self.phone,
            "email": self.email,
            "area": float(self.area) if self.area else None,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class Inventory(Base):
    """库存表"""
    __tablename__ = "inventory"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    location_id = Column(Integer, ForeignKey("locations.id"), nullable=False)
    batch_id = Column(Integer, ForeignKey("product_batches.id"))
    quantity = Column(Integer, nullable=False, default=0)
    reserved_quantity = Column(Integer, default=0)  # 预留数量
    available_quantity = Column(Integer, default=0)  # 可用数量
    damaged_quantity = Column(Integer, default=0)    # 损坏数量
    frozen_quantity = Column(Integer, default=0)     # 冻结数量
    cost_price = Column(Numeric(10, 2))  # 成本价
    last_inbound_date = Column(DateTime)  # 最后入库日期
    last_outbound_date = Column(DateTime) # 最后出库日期
    last_count_date = Column(DateTime)    # 最后盘点日期
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    product = relationship("Product", back_populates="inventory_items")
    location = relationship("Location", back_populates="inventory_items")
    batch = relationship("ProductBatch")
    
    # 复合唯一约束和索引
    __table_args__ = (
        Index('idx_inventory_product_location', 'product_id', 'location_id', unique=True),
        Index('idx_inventory_product', 'product_id'),
        Index('idx_inventory_location', 'location_id'),
        Index('idx_inventory_batch', 'batch_id'),
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "product_id": self.product_id,
            "location_id": self.location_id,
            "batch_id": self.batch_id,
            "quantity": self.quantity,
            "reserved_quantity": self.reserved_quantity,
            "available_quantity": self.available_quantity,
            "damaged_quantity": self.damaged_quantity,
            "frozen_quantity": self.frozen_quantity,
            "cost_price": float(self.cost_price) if self.cost_price else None,
            "last_inbound_date": self.last_inbound_date.isoformat() if self.last_inbound_date else None,
            "last_outbound_date": self.last_outbound_date.isoformat() if self.last_outbound_date else None,
            "last_count_date": self.last_count_date.isoformat() if self.last_count_date else None,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class InventoryTransaction(Base):
    """库存事务表"""
    __tablename__ = "inventory_transactions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    location_id = Column(Integer, ForeignKey("locations.id"), nullable=False)
    transaction_type = Column(String(20), nullable=False)  # inbound, outbound, adjustment, transfer
    quantity = Column(Integer, nullable=False)
    reference_type = Column(String(50))  # order, adjustment, transfer, stocktake
    reference_id = Column(Integer)       # 关联单据ID
    batch_id = Column(Integer, ForeignKey("product_batches.id"))
    serial_number = Column(String(100))
    cost_price = Column(Numeric(10, 2))
    reason = Column(String(200))
    notes = Column(Text)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    product = relationship("Product")
    location = relationship("Location")
    batch = relationship("ProductBatch")
    user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index('idx_inventory_transaction_product', 'product_id'),
        Index('idx_inventory_transaction_location', 'location_id'),
        Index('idx_inventory_transaction_type', 'transaction_type'),
        Index('idx_inventory_transaction_reference', 'reference_type', 'reference_id'),
        Index('idx_inventory_transaction_date', 'created_at'),
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "product_id": self.product_id,
            "location_id": self.location_id,
            "transaction_type": self.transaction_type,
            "quantity": self.quantity,
            "reference_type": self.reference_type,
            "reference_id": self.reference_id,
            "batch_id": self.batch_id,
            "serial_number": self.serial_number,
            "cost_price": float(self.cost_price) if self.cost_price else None,
            "reason": self.reason,
            "notes": self.notes,
            "user_id": self.user_id,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class StocktakeOrder(Base):
    """盘点单表"""
    __tablename__ = "stocktake_orders"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    order_number = Column(String(50), unique=True, nullable=False)
    title = Column(String(200), nullable=False)
    type = Column(String(20), default="full")  # full, partial, cycle
    status = Column(String(20), default="draft")  # draft, in_progress, completed, cancelled
    warehouse_id = Column(Integer, ForeignKey("warehouses.id"))
    location_ids = Column(Text)  # JSON格式存储库位ID列表
    product_ids = Column(Text)   # JSON格式存储产品ID列表
    planned_start_date = Column(DateTime)
    planned_end_date = Column(DateTime)
    actual_start_date = Column(DateTime)
    actual_end_date = Column(DateTime)
    total_items = Column(Integer, default=0)
    counted_items = Column(Integer, default=0)
    variance_items = Column(Integer, default=0)
    notes = Column(Text)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    warehouse = relationship("Warehouse")
    creator = relationship("User")
    items = relationship("StocktakeItem", back_populates="stocktake_order")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "order_number": self.order_number,
            "title": self.title,
            "type": self.type,
            "status": self.status,
            "warehouse_id": self.warehouse_id,
            "total_items": self.total_items,
            "counted_items": self.counted_items,
            "variance_items": self.variance_items,
            "planned_start_date": self.planned_start_date.isoformat() if self.planned_start_date else None,
            "planned_end_date": self.planned_end_date.isoformat() if self.planned_end_date else None,
            "actual_start_date": self.actual_start_date.isoformat() if self.actual_start_date else None,
            "actual_end_date": self.actual_end_date.isoformat() if self.actual_end_date else None,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class StocktakeItem(Base):
    """盘点明细表"""
    __tablename__ = "stocktake_items"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    stocktake_order_id = Column(Integer, ForeignKey("stocktake_orders.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    location_id = Column(Integer, ForeignKey("locations.id"), nullable=False)
    batch_id = Column(Integer, ForeignKey("product_batches.id"))
    book_quantity = Column(Integer, default=0)    # 账面数量
    counted_quantity = Column(Integer)            # 盘点数量
    variance_quantity = Column(Integer, default=0) # 差异数量
    variance_reason = Column(String(200))         # 差异原因
    is_counted = Column(Boolean, default=False)
    counted_by = Column(Integer, ForeignKey("users.id"))
    counted_at = Column(DateTime)
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    stocktake_order = relationship("StocktakeOrder", back_populates="items")
    product = relationship("Product")
    location = relationship("Location")
    batch = relationship("ProductBatch")
    counter = relationship("User")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "stocktake_order_id": self.stocktake_order_id,
            "product_id": self.product_id,
            "location_id": self.location_id,
            "batch_id": self.batch_id,
            "book_quantity": self.book_quantity,
            "counted_quantity": self.counted_quantity,
            "variance_quantity": self.variance_quantity,
            "variance_reason": self.variance_reason,
            "is_counted": self.is_counted,
            "counted_by": self.counted_by,
            "counted_at": self.counted_at.isoformat() if self.counted_at else None,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
