"""
WMS客户端系统 - 主窗口
企业风格：正式、简约、大气
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QStackedWidget, QSplitter, QMenuBar, QStatusBar, QToolBar,
    QApplication, QFrame, QScrollArea, QPushButton, QDialog, QMessageBox,
    QLineEdit, QSizePolicy, QLabel, QTableWidgetItem
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal
from PyQt6.QtGui import QIcon, QAction, QFont, QPalette

from ..components.base_components import (
    BaseCard, TitleLabel, BodyLabel, PrimaryButton, SecondaryButton,
    BaseTable, ActionBar, StatusBar
)
from ..styles.theme import theme


class AdminPasswordDialog(QDialog):
    """管理员密码验证对话框 - 触摸屏优化"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("管理员验证")

        # 设置对话框大小 - 黄金比例优化
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()

        # 使用黄金比例 (1:1.618) 计算对话框尺寸
        base_width = max(480, min(580, int(screen_geometry.width() * 0.35)))
        self.dialog_width = base_width
        self.dialog_height = int(base_width / 1.618)  # 黄金比例高度

        # 确保最小尺寸
        self.dialog_height = max(300, self.dialog_height)

        self.setFixedSize(self.dialog_width, self.dialog_height)

        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowStaysOnTopHint)
        self.setModal(True)

        # 管理员密码: admin123
        self.admin_password = "admin123"

        # 退出确认计数器
        self.exit_attempts = 0
        self.max_attempts = 2  # 最多2次验证

        self._setup_ui()

    def _setup_ui(self):
        """设置UI - 触摸屏优化，确保内容完整显示"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建滚动区域以防内容过多
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # 创建内容容器 - 黄金比例间距
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)

        # 使用黄金比例计算间距
        golden_ratio = 1.618
        base_spacing = int(self.width() / (golden_ratio * 20))  # 基础间距
        layout.setSpacing(base_spacing)

        # 边距使用黄金比例
        margin = int(self.width() / (golden_ratio * 12))
        layout.setContentsMargins(margin, margin, margin, margin)

        # 标题容器 - 黄金比例间距
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_spacing = int(base_spacing / golden_ratio)  # 更小的间距
        title_layout.setSpacing(title_spacing)
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 图标 - 黄金比例字体大小
        icon_size = int(self.width() / (golden_ratio * 8))  # 基于对话框宽度计算
        icon_label = BodyLabel("🔐")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: {icon_size}px;
                background-color: transparent;
                padding: {int(icon_size/8)}px;
            }}
        """)
        title_layout.addWidget(icon_label)

        # 标题 - 黄金比例字体大小
        title_size = int(icon_size / golden_ratio)  # 标题字体比图标小
        title_label = BodyLabel("管理员权限验证")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: #1e40af;
                font-size: {title_size}px;
                font-weight: bold;
                background-color: transparent;
                margin-bottom: {int(title_size/4)}px;
            }}
        """)
        title_layout.addWidget(title_label)

        # 说明文字 - 黄金比例字体大小
        info_size = int(title_size / golden_ratio)  # 比标题更小
        info_label = BodyLabel("退出系统需要管理员权限验证\n默认密码: admin123")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet(f"""
            QLabel {{
                color: #64748b;
                font-size: {info_size}px;
                background-color: transparent;
                margin-bottom: {int(info_size * 0.8)}px;
                line-height: 1.5;
            }}
        """)
        title_layout.addWidget(info_label)

        # 尝试次数提示 - 更小的字体
        attempts_size = int(info_size * 0.9)
        self.attempts_label = BodyLabel(f"剩余验证次数: {self.max_attempts - self.exit_attempts}")
        self.attempts_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.attempts_label.setStyleSheet(f"""
            QLabel {{
                color: #f59e0b;
                font-size: {attempts_size}px;
                font-weight: 500;
                background-color: transparent;
            }}
        """)
        title_layout.addWidget(self.attempts_label)

        layout.addWidget(title_container)

        # 密码输入 - 黄金比例优化
        input_height = int(self.dialog_height / (golden_ratio * 6))  # 基于对话框高度计算
        input_font_size = int(input_height / 3)  # 字体大小与输入框高度成比例
        input_padding = int(input_height / 4)  # 内边距
        input_radius = int(input_height / 4)  # 圆角半径

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入管理员密码")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setFixedHeight(input_height)
        self.password_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: #f8fafc;
                border: 2px solid #e2e8f0;
                border-radius: {input_radius}px;
                padding: {input_padding}px {int(input_padding * 1.2)}px;
                font-size: {input_font_size}px;
                color: #1e293b;
                font-weight: 500;
            }}
            QLineEdit:focus {{
                border-color: #1e40af;
                background-color: white;
                border-width: 3px;
            }}
        """)
        self.password_input.returnPressed.connect(self._verify_password)
        layout.addWidget(self.password_input)

        # 按钮 - 黄金比例优化
        button_layout = QHBoxLayout()
        button_spacing = int(self.width() / (golden_ratio * 15))  # 按钮间距
        button_layout.setSpacing(button_spacing)

        # 按钮尺寸计算
        button_height = int(input_height * 0.9)  # 比输入框稍小
        button_width = int(self.width() / (golden_ratio * 3))  # 按钮宽度
        button_font_size = int(button_height / 3)  # 按钮字体大小
        button_radius = int(button_height / 4)  # 按钮圆角
        button_padding = int(button_height / 5)  # 按钮内边距

        cancel_btn = QPushButton("取消")
        cancel_btn.setFixedSize(button_width, button_height)
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #6b7280;
                color: white;
                border: none;
                border-radius: {button_radius}px;
                padding: {button_padding}px;
                font-size: {button_font_size}px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: #4b5563;
            }}
            QPushButton:pressed {{
                background-color: #374151;
            }}
        """)
        cancel_btn.clicked.connect(self.reject)

        verify_btn = QPushButton("验证")
        verify_btn.setFixedSize(button_width, button_height)
        verify_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #1e40af;
                color: white;
                border: none;
                border-radius: {button_radius}px;
                padding: {button_padding}px;
                font-size: {button_font_size}px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: #1d4ed8;
            }}
            QPushButton:pressed {{
                background-color: #1e3a8a;
            }}
        """)
        verify_btn.clicked.connect(self._verify_password)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(verify_btn)
        layout.addLayout(button_layout)

        # 设置滚动区域
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)

        # 对话框样式 - 黄金比例优化
        dialog_radius = int(self.width() / (golden_ratio * 20))  # 对话框圆角
        border_width = max(2, int(self.width() / 200))  # 边框宽度

        self.setStyleSheet(f"""
            AdminPasswordDialog {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #eff6ff, stop:0.3 #dbeafe, stop:0.7 #bfdbfe, stop:1 #93c5fd);
                border-radius: {dialog_radius}px;
                border: {border_width}px solid #1e40af;
            }}
            QScrollArea {{
                background: transparent;
                border: none;
            }}
        """)

        # 设置焦点
        self.password_input.setFocus()

    def _verify_password(self):
        """验证密码 - 限制尝试次数"""
        password = self.password_input.text().strip()

        if password == self.admin_password:
            self.accept()
        else:
            self.exit_attempts += 1
            remaining = self.max_attempts - self.exit_attempts

            if remaining > 0:
                self._show_error(f"密码错误！剩余验证次数: {remaining}")
                self.attempts_label.setText(f"剩余验证次数: {remaining}")
                self.password_input.clear()
                self.password_input.setFocus()
            else:
                # 达到最大尝试次数，强制退出
                self._show_error("验证失败次数过多，系统将强制退出！")
                self.accept()  # 强制通过验证

    def _show_error(self, message):
        """显示错误信息 - 黄金比例优化"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("验证失败")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Warning)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

        # 获取当前对话框的尺寸用于计算
        golden_ratio = 1.618
        msg_font_size = int(self.width() / (golden_ratio * 25))  # 消息框字体大小
        btn_height = int(self.height() / (golden_ratio * 8))  # 按钮高度
        btn_width = int(self.width() / (golden_ratio * 5))  # 按钮宽度
        btn_radius = int(btn_height / 4)  # 按钮圆角

        # 优化消息框样式 - 黄金比例
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: white;
                border-radius: {int(self.width() / (golden_ratio * 30))}px;
                font-size: {msg_font_size}px;
            }}
            QMessageBox QPushButton {{
                background-color: #1e40af;
                color: white;
                border: none;
                border-radius: {btn_radius}px;
                padding: {int(btn_height/6)}px {int(btn_width/8)}px;
                font-size: {int(msg_font_size * 0.9)}px;
                font-weight: 600;
                min-width: {btn_width}px;
                min-height: {btn_height}px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: #1d4ed8;
            }}
            QMessageBox QPushButton:pressed {{
                background-color: #1e3a8a;
            }}
        """)

        msg_box.exec()


class SidebarWidget(QWidget):
    """侧边栏组件"""
    
    # 信号定义
    menu_clicked = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        self._setup_style()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo区域 - 使用响应式高度，更美观的设计
        logo_frame = QFrame()
        # 获取父窗口的工具栏高度，如果没有则使用默认值
        toolbar_height = getattr(self.parent(), 'toolbar_height', theme.spacing.HEADER_HEIGHT) if self.parent() else theme.spacing.HEADER_HEIGHT
        logo_frame.setFixedHeight(toolbar_height)
        logo_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(255, 255, 255, 0.05));
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)

        logo_layout = QVBoxLayout(logo_frame)
        # 使用响应式边距
        margin = max(10, min(20, int(toolbar_height * 0.25)))
        logo_layout.setContentsMargins(margin, margin//2, margin, margin//2)
        logo_layout.setSpacing(2)

        # 主标题
        logo_label = QLabel("WMS系统")
        logo_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 22px;
                font-weight: 700;
                font-family: "Microsoft YaHei UI", sans-serif;
                background-color: transparent;
            }
        """)
        logo_layout.addWidget(logo_label)

        # 副标题
        subtitle_label = QLabel("库房管理系统")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 12px;
                font-weight: 400;
                font-family: "Microsoft YaHei UI", sans-serif;
                background-color: transparent;
            }
        """)
        logo_layout.addWidget(subtitle_label)
        
        layout.addWidget(logo_frame)
        
        # 菜单区域
        menu_scroll = QScrollArea()
        menu_scroll.setWidgetResizable(True)
        menu_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        menu_widget = QWidget()
        menu_layout = QVBoxLayout(menu_widget)
        menu_layout.setContentsMargins(0, theme.spacing.MD, 0, 0)
        menu_layout.setSpacing(theme.spacing.SM)
        
        # 菜单项
        menu_items = [
            ("dashboard", "📊 控制台", True),
            ("inbound", "📦 入库管理", False),
            ("outbound", "📤 出库管理", False),
            ("inventory", "📋 库存查询", False),
            ("reports", "📈 报表统计", False),
            ("users", "👥 用户管理", False),
            ("settings", "⚙️ 系统设置", False),
            ("about", "ℹ️ 关于系统", False),
        ]
        
        self.menu_buttons = {}
        for menu_id, menu_text, is_active in menu_items:
            button = self._create_menu_button(menu_id, menu_text, is_active)
            self.menu_buttons[menu_id] = button
            menu_layout.addWidget(button)
        
        menu_layout.addStretch()
        menu_scroll.setWidget(menu_widget)
        layout.addWidget(menu_scroll)
    
    def _create_menu_button(self, menu_id: str, text: str, is_active: bool = False):
        """创建菜单按钮 - 现代化美观设计"""
        button = QPushButton(text)

        # 使用父窗口计算的触摸屏优化尺寸
        if self.parent() and hasattr(self.parent(), 'menu_button_height'):
            button_height = self.parent().menu_button_height
            font_size = self.parent().menu_button_font_size
        else:
            # 默认触摸屏友好尺寸
            button_height = 60
            font_size = 18

        button.setFixedHeight(button_height)

        # 现代化美观样式 - 与快速入库字体大小一致
        base_style = f"""
            QPushButton {{
                font-size: {font_size}px;
                font-weight: 600;
                font-family: "Microsoft YaHei UI", sans-serif;
                padding: 12px 20px;
                text-align: left;
                border: none;
                border-radius: 8px;
                margin: 2px 8px;
                background-color: transparent;
                color: rgba(255, 255, 255, 0.9);
            }}
            QPushButton:hover {{
                background-color: rgba(255, 255, 255, 0.1);
                color: white;
            }}
            QPushButton:pressed {{
                background-color: rgba(255, 255, 255, 0.15);
            }}
        """

        button.setStyleSheet(base_style)
        button.clicked.connect(lambda: self._on_menu_click(menu_id))

        if is_active:
            self._set_active_button(button)
        else:
            self._set_inactive_button(button)

        return button
    
    def _set_active_button(self, button):
        """设置活动按钮样式 - 现代化美观设计"""
        # 获取字体大小
        font_size = getattr(self.parent(), 'menu_button_font_size', 18) if self.parent() else 18

        button.setStyleSheet(f"""
            QPushButton {{
                font-size: {font_size}px;
                font-weight: 700;
                font-family: "Microsoft YaHei UI", sans-serif;
                padding: 12px 20px;
                text-align: left;
                border: none;
                border-radius: 8px;
                margin: 2px 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.25),
                    stop:1 rgba(255, 255, 255, 0.15));
                color: white;
                border-left: 4px solid #fbbf24;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.3),
                    stop:1 rgba(255, 255, 255, 0.2));
            }}
        """)

    def _set_inactive_button(self, button):
        """设置非活动按钮样式 - 现代化美观设计"""
        # 获取字体大小
        font_size = getattr(self.parent(), 'menu_button_font_size', 18) if self.parent() else 18

        button.setStyleSheet(f"""
            QPushButton {{
                font-size: {font_size}px;
                font-weight: 600;
                font-family: "Microsoft YaHei UI", sans-serif;
                padding: 12px 20px;
                text-align: left;
                border: none;
                border-radius: 8px;
                margin: 2px 8px;
                background-color: transparent;
                color: rgba(255, 255, 255, 0.9);
                border-left: 4px solid transparent;
            }}
            QPushButton:hover {{
                background-color: rgba(255, 255, 255, 0.1);
                color: white;
                border-left: 4px solid rgba(251, 191, 36, 0.5);
            }}
            QPushButton:pressed {{
                background-color: rgba(255, 255, 255, 0.15);
            }}
        """)
    
    def _setup_style(self):
        """设置样式 - 现代化美观设计"""
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e3a8a,
                    stop:0.3 #1e40af,
                    stop:0.7 #1d4ed8,
                    stop:1 #1e3a8a);
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: rgba(255, 255, 255, 0.1);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: rgba(255, 255, 255, 0.5);
            }
        """)

        # 使用响应式宽度，如果没有设置则使用默认值
        sidebar_width = getattr(self.parent(), 'sidebar_width', theme.spacing.SIDEBAR_WIDTH) if self.parent() else theme.spacing.SIDEBAR_WIDTH
        self.setFixedWidth(sidebar_width)
    
    def _on_menu_click(self, menu_id: str):
        """菜单点击事件"""
        # 重置所有按钮样式
        for btn_id, button in self.menu_buttons.items():
            if btn_id == menu_id:
                self._set_active_button(button)
            else:
                self._set_inactive_button(button)

        # 发送信号
        self.menu_clicked.emit(menu_id)


class DashboardWidget(QWidget):
    """控制台组件 - 重新设计的美观界面"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        self._setup_timer()

    def _setup_ui(self):
        """设置UI - 现代化美观设计"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # 创建滚动内容容器
        scroll_content = QWidget()
        layout = QVBoxLayout(scroll_content)

        # 计算触摸屏优化的边距和间距
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        margin = max(20, min(30, int(screen_geometry.width() * 0.02)))
        spacing = max(15, min(25, int(screen_geometry.height() * 0.02)))

        layout.setContentsMargins(margin, margin, margin, margin)
        layout.setSpacing(spacing)

        # 欢迎区域
        welcome_card = self._create_welcome_section()
        layout.addWidget(welcome_card)

        # 统计卡片区域 - 重新设计
        stats_section = self._create_stats_section()
        layout.addWidget(stats_section)

        # 功能模块区域
        modules_section = self._create_modules_section()
        layout.addWidget(modules_section)

        # 系统状态和最近操作区域
        bottom_section = self._create_bottom_section()
        layout.addWidget(bottom_section)

        # 添加弹性空间，确保内容顶部对齐
        layout.addStretch()

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

    def _create_welcome_section(self):
        """创建欢迎区域"""
        card = BaseCard()
        layout = QHBoxLayout()

        # 左侧欢迎信息
        welcome_layout = QVBoxLayout()

        # 获取当前时间和问候语
        import datetime
        current_time = datetime.datetime.now()
        hour = current_time.hour
        if 5 <= hour < 12:
            greeting = "早上好"
            icon = "🌅"
        elif 12 <= hour < 18:
            greeting = "下午好"
            icon = "☀️"
        else:
            greeting = "晚上好"
            icon = "🌙"

        # 问候标题
        greeting_label = TitleLabel(f"{icon} {greeting}，欢迎使用WMS系统", level=2)
        greeting_label.setStyleSheet(f"""
            color: {theme.colors.PRIMARY};
            font-weight: 600;
            margin-bottom: 8px;
        """)
        welcome_layout.addWidget(greeting_label)

        # 时间显示
        self.time_label = BodyLabel(current_time.strftime("今天是 %Y年%m月%d日 %A %H:%M:%S"))
        self.time_label.setStyleSheet(f"""
            color: {theme.colors.TEXT_SECONDARY};
            font-size: 16px;
        """)
        welcome_layout.addWidget(self.time_label)

        layout.addLayout(welcome_layout)
        layout.addStretch()

        # 右侧系统logo或图标
        logo_label = QLabel("🏢")
        logo_label.setStyleSheet("""
            font-size: 48px;
            color: #3b82f6;
        """)
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(logo_label)

        card.add_layout(layout)
        return card

    def _create_stats_section(self):
        """创建统计区域"""
        card = BaseCard()
        layout = QVBoxLayout()

        # 区域标题
        title = TitleLabel("📊 今日数据概览", level=3)
        title.setStyleSheet(f"""
            color: {theme.colors.PRIMARY};
            font-weight: 600;
            margin-bottom: 15px;
        """)
        layout.addWidget(title)

        # 统计卡片网格 - 响应式布局
        stats_layout = QGridLayout()
        stats_layout.setSpacing(15)

        # 模拟数据加载状态 - 可以根据实际数据源调整
        has_data = True  # 设置为 False 可以测试空数据状态

        if has_data:
            # 统计数据
            stats_data = [
                ("📦 今日入库", "156", "件", "#10b981", "比昨日 +12%"),
                ("� 今日出库", "89", "件", "#3b82f6", "比昨日 -5%"),
                ("📋 库存总量", "12,456", "件", "#8b5cf6", "正常水位"),
                ("⚠️ 待处理", "23", "单", "#f59e0b", "需要关注"),
            ]
        else:
            # 空数据状态
            stats_data = [
                ("📦 今日入库", "--", "件", "#e5e7eb", "暂无数据"),
                ("📤 今日出库", "--", "件", "#e5e7eb", "暂无数据"),
                ("📋 库存总量", "--", "件", "#e5e7eb", "暂无数据"),
                ("⚠️ 待处理", "--", "单", "#e5e7eb", "暂无数据"),
            ]

        # 根据屏幕宽度决定列数
        screen = QApplication.primaryScreen()
        screen_width = screen.geometry().width()
        cols = 4 if screen_width > 1400 else (2 if screen_width > 800 else 1)

        for i, (title, value, unit, color, trend) in enumerate(stats_data):
            stat_card = self._create_enhanced_stat_card(title, value, unit, color, trend, not has_data)
            row = i // cols
            col = i % cols
            stats_layout.addWidget(stat_card, row, col)

        # 设置列的拉伸因子，确保均匀分布
        for col in range(cols):
            stats_layout.setColumnStretch(col, 1)

        layout.addLayout(stats_layout)
        card.add_layout(layout)
        return card

    def _create_modules_section(self):
        """创建功能模块区域"""
        card = BaseCard()
        layout = QVBoxLayout()

        # 区域标题
        title = TitleLabel("� 快速操作", level=3)
        title.setStyleSheet(f"""
            color: {theme.colors.PRIMARY};
            font-weight: 600;
            margin-bottom: 15px;
        """)
        layout.addWidget(title)

        # 功能按钮网格 - 响应式布局
        modules_layout = QGridLayout()
        modules_layout.setSpacing(15)

        # 模拟功能模块加载状态 - 可以根据实际需求调整
        has_modules = True  # 设置为 False 可以测试空数据状态

        if has_modules:
            # 功能模块数据
            modules_data = [
                ("📦 快速入库", "扫码入库商品", "#10b981", "inbound"),
                ("📤 快速出库", "扫码出库商品", "#3b82f6", "outbound"),
                ("🔍 库存查询", "查询商品信息", "#8b5cf6", "query"),
                ("👥 用户管理", "管理系统用户", "#f59e0b", "users"),
                ("📊 报表统计", "查看数据报表", "#ef4444", "reports"),
                ("⚙️ 系统设置", "系统配置管理", "#6b7280", "settings"),
            ]
        else:
            # 空数据状态 - 显示占位按钮
            modules_data = [
                ("功能模块", "正在加载中...", "#e5e7eb", "loading"),
                ("功能模块", "正在加载中...", "#e5e7eb", "loading"),
                ("功能模块", "正在加载中...", "#e5e7eb", "loading"),
                ("功能模块", "正在加载中...", "#e5e7eb", "loading"),
                ("功能模块", "正在加载中...", "#e5e7eb", "loading"),
                ("功能模块", "正在加载中...", "#e5e7eb", "loading"),
            ]

        # 根据屏幕宽度决定列数
        screen = QApplication.primaryScreen()
        screen_width = screen.geometry().width()
        cols = 3 if screen_width > 1200 else (2 if screen_width > 800 else 1)

        for i, (title, desc, color, action) in enumerate(modules_data):
            module_btn = self._create_module_button(title, desc, color, action, not has_modules)
            row = i // cols
            col = i % cols
            modules_layout.addWidget(module_btn, row, col)

        # 设置列的拉伸因子，确保均匀分布
        for col in range(cols):
            modules_layout.setColumnStretch(col, 1)

        layout.addLayout(modules_layout)
        card.add_layout(layout)
        return card

    def _create_bottom_section(self):
        """创建底部区域（系统状态和最近操作）"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setSpacing(20)

        # 左侧：系统状态
        status_card = BaseCard()
        status_layout = QVBoxLayout()

        status_title = TitleLabel("🔧 系统状态", level=3)
        status_title.setStyleSheet(f"""
            color: {theme.colors.PRIMARY};
            font-weight: 600;
            margin-bottom: 15px;
        """)
        status_layout.addWidget(status_title)

        # 状态指示器
        status_items = [
            ("🟢 数据库连接", "正常", "#10b981"),
            ("🟢 网络连接", "正常", "#10b981"),
            ("🟡 存储空间", "78% 使用", "#f59e0b"),
            ("🟢 系统运行", "稳定", "#10b981"),
        ]

        for icon_text, status, color in status_items:
            status_item = QHBoxLayout()

            status_label = BodyLabel(icon_text)
            status_label.setStyleSheet("font-size: 14px; font-weight: 500;")
            status_item.addWidget(status_label)

            status_value = BodyLabel(status)
            status_value.setStyleSheet(f"color: {color}; font-size: 14px; font-weight: 600;")
            status_item.addWidget(status_value)
            status_item.addStretch()

            status_layout.addLayout(status_item)

        status_card.add_layout(status_layout)
        layout.addWidget(status_card)

        # 右侧：最近操作
        recent_card = BaseCard()
        recent_layout = QVBoxLayout()

        recent_title = TitleLabel("📋 最近操作", level=3)
        recent_title.setStyleSheet(f"""
            color: {theme.colors.PRIMARY};
            font-weight: 600;
            margin-bottom: 15px;
        """)
        recent_layout.addWidget(recent_title)

        # 最近操作列表
        recent_operations = [
            ("14:30", "入库", "笔记本电脑 × 5", "#10b981"),
            ("14:15", "出库", "无线鼠标 × 10", "#3b82f6"),
            ("13:45", "入库", "打印纸 × 50", "#10b981"),
            ("13:20", "出库", "键盘 × 3", "#3b82f6"),
            ("12:55", "查询", "显示器库存", "#8b5cf6"),
        ]

        for time, op_type, item, color in recent_operations:
            op_item = QHBoxLayout()

            time_label = BodyLabel(time)
            time_label.setStyleSheet("color: #6b7280; font-size: 12px; min-width: 40px;")
            op_item.addWidget(time_label)

            type_label = BodyLabel(op_type)
            type_label.setStyleSheet(f"color: {color}; font-size: 12px; font-weight: 600; min-width: 40px;")
            op_item.addWidget(type_label)

            item_label = BodyLabel(item)
            item_label.setStyleSheet("font-size: 12px;")
            op_item.addWidget(item_label)
            op_item.addStretch()

            recent_layout.addLayout(op_item)

        recent_card.add_layout(recent_layout)
        layout.addWidget(recent_card)

        return container

    def _create_enhanced_stat_card(self, title: str, value: str, unit: str, color: str, trend: str, is_empty: bool = False):
        """创建增强的统计卡片"""
        card = BaseCard()
        # 设置最小尺寸，确保在小窗口中也能正常显示
        card.setMinimumSize(200, 120)
        card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()

        # 标题
        title_label = BodyLabel(title)
        title_label.setStyleSheet(f"""
            font-size: 14px;
            color: {theme.colors.TEXT_SECONDARY};
            font-weight: 500;
            margin-bottom: 8px;
        """)
        layout.addWidget(title_label)

        # 数值和单位
        value_layout = QHBoxLayout()

        if is_empty:
            # 空数据状态：显示占位图标
            empty_icon = QLabel("📊")
            empty_icon.setStyleSheet(f"""
                font-size: 32px;
                color: {color};
                margin: 10px 0;
            """)
            empty_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
            value_layout.addWidget(empty_icon)
            value_layout.addStretch()
        else:
            # 正常数据状态
            value_label = TitleLabel(value, level=2)
            value_label.setStyleSheet(f"""
                color: {color};
                font-size: 28px;
                font-weight: bold;
            """)
            unit_label = BodyLabel(unit)
            unit_label.setStyleSheet(f"""
                font-size: 16px;
                color: {theme.colors.TEXT_SECONDARY};
                margin-left: 5px;
            """)

            value_layout.addWidget(value_label)
            value_layout.addWidget(unit_label)
            value_layout.addStretch()

        layout.addLayout(value_layout)

        # 趋势信息
        trend_label = BodyLabel(trend)
        trend_label.setStyleSheet(f"""
            font-size: 12px;
            color: {theme.colors.TEXT_SECONDARY};
            margin-top: 5px;
        """)
        layout.addWidget(trend_label)

        # 设置卡片样式 - 根据是否为空数据调整样式
        if is_empty:
            card.setStyleSheet(f"""
                BaseCard {{
                    border-left: 4px solid {color};
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(248, 250, 252, 0.95),
                        stop:1 rgba(241, 245, 249, 0.9));
                    opacity: 0.7;
                }}
            """)
        else:
            card.setStyleSheet(f"""
                BaseCard {{
                    border-left: 4px solid {color};
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.9));
                }}
            """)

        card.add_layout(layout)
        return card

    def _create_module_button(self, title: str, desc: str, color: str, action: str, is_empty: bool = False):
        """创建功能模块按钮 - 图标在上，文字在下"""
        button = QPushButton()
        # 设置最小尺寸和响应式高度，增加高度以容纳图标
        button.setMinimumSize(180, 120)
        button.setMaximumHeight(140)
        button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        button.setCursor(Qt.CursorShape.PointingHandCursor)

        # 按钮布局 - 垂直布局，图标在上
        layout = QVBoxLayout(button)
        layout.setSpacing(8)
        layout.setContentsMargins(10, 15, 10, 15)

        # 图标映射
        icon_map = {
            "inbound": "📦",
            "outbound": "📤",
            "query": "🔍",
            "users": "👥",
            "reports": "📊",
            "settings": "⚙️",
            "loading": "⏳"  # 加载状态图标
        }

        # 图标
        if is_empty:
            # 空数据状态：显示加载图标
            icon_label = QLabel("⏳")
            icon_label.setStyleSheet(f"""
                color: {color};
                font-size: 28px;
                border: none;
                background: transparent;
                margin-bottom: 5px;
                opacity: 0.6;
            """)
        else:
            # 正常状态
            icon_label = QLabel(icon_map.get(action, "📋"))
            icon_label.setStyleSheet(f"""
                color: {color};
                font-size: 32px;
                border: none;
                background: transparent;
                margin-bottom: 5px;
            """)

        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(icon_label)

        # 标题 - 去掉emoji，只保留文字
        title_text = title.split(" ", 1)[-1] if " " in title else title
        title_label = QLabel(title_text)
        title_label.setStyleSheet(f"""
            color: {color};
            font-size: 14px;
            font-weight: 600;
            border: none;
            background: transparent;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 描述 - 移动到图标下方
        desc_label = QLabel(desc)
        desc_label.setStyleSheet(f"""
            color: {theme.colors.TEXT_SECONDARY};
            font-size: 11px;
            border: none;
            background: transparent;
            margin-top: 2px;
        """)
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setWordWrap(True)  # 允许文字换行
        layout.addWidget(desc_label)

        # 按钮样式 - 根据是否为空数据调整样式
        if is_empty:
            # 空数据状态样式
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(248, 250, 252, 0.9),
                        stop:1 rgba(241, 245, 249, 0.8));
                    border: 2px dashed {color};
                    border-radius: 16px;
                    padding: 0px;
                    opacity: 0.7;
                }}
                QPushButton:hover {{
                    opacity: 0.8;
                    border-style: solid;
                }}
            """)
            # 禁用空数据按钮的点击
            button.setEnabled(False)
        else:
            # 正常状态样式
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.85));
                    border: 2px solid {color};
                    border-radius: 16px;
                    padding: 0px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color},
                        stop:1 rgba({int(color[1:3], 16)}, {int(color[3:5], 16)}, {int(color[5:7], 16)}, 0.8));
                    border-color: {color};
                }}
                QPushButton:hover QLabel {{
                    color: white !important;
                }}
                QPushButton:pressed {{
                    background: {color};
                }}
                QPushButton:pressed QLabel {{
                    color: white !important;
                }}
            """)

        # 连接点击事件 - 只有非空数据状态才连接
        if not is_empty:
            button.clicked.connect(lambda: self._handle_module_click(action))

        return button

    def _handle_module_click(self, action: str):
        """处理模块按钮点击"""
        # 发送信号给主窗口切换页面
        parent = self.parent()
        while parent and not hasattr(parent, '_on_menu_changed'):
            parent = parent.parent()

        if parent and hasattr(parent, '_on_menu_changed'):
            parent._on_menu_changed(action)

    def _setup_timer(self):
        """设置定时器更新时间"""
        from PyQt6.QtCore import QTimer
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_time)
        self.timer.start(1000)  # 每秒更新一次

    def _update_time(self):
        """更新时间显示"""
        import datetime
        current_time = datetime.datetime.now()
        if hasattr(self, 'time_label'):
            self.time_label.setText(current_time.strftime("今天是 %Y年%m月%d日 %A %H:%M:%S"))


class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self, user_data=None):
        super().__init__()
        self.user_data = user_data or {}
        self._setup_ui()
        self._setup_style()
        self._setup_connections()
        self._update_user_info()

    def _calculate_responsive_sizes(self):
        """计算响应式尺寸 - 专为触摸屏一体机优化"""
        width = self.screen_geometry.width()
        height = self.screen_geometry.height()

        # 触摸屏优化：侧边栏宽度增加，提供更好的视觉效果 (14-18% of screen width)
        self.sidebar_width = max(200, min(280, int(width * 0.16)))

        # 触摸屏优化：工具栏高度适中 (6-8% of screen height)
        self.toolbar_height = max(60, min(80, int(height * 0.07)))

        # 状态栏高度
        self.statusbar_height = max(30, min(40, int(height * 0.035)))

        # 计算内容区域尺寸
        self.content_width = width - self.sidebar_width
        self.content_height = height - self.toolbar_height - self.statusbar_height

        # 触摸屏优化：计算菜单按钮尺寸 (更大的触摸目标，与快速入库标题字体一致)
        self.menu_button_height = max(60, min(75, int(height * 0.07)))
        self.menu_button_font_size = max(16, min(20, int(height * 0.02)))  # 与快速入库24px相近

        # 触摸屏优化：计算快速操作按钮尺寸
        self.quick_button_height = max(65, min(85, int(height * 0.08)))

        print(f"📐 触摸屏响应式尺寸计算:")
        print(f"   侧边栏宽度: {self.sidebar_width}px ({width * 0.16:.1f}% 屏宽)")
        print(f"   工具栏高度: {self.toolbar_height}px")
        print(f"   状态栏高度: {self.statusbar_height}px")
        print(f"   内容区域: {self.content_width}x{self.content_height}px")
        print(f"   菜单按钮高度: {self.menu_button_height}px")
        print(f"   菜单按钮字体: {self.menu_button_font_size}px")
        print(f"   快速按钮高度: {self.quick_button_height}px")

    def _setup_ui(self):
        """设置UI - 自动适配屏幕分辨率的满屏效果"""
        self.setWindowTitle("WMS库房自助出入库客户端系统")

        # 获取屏幕信息并设置全屏 - 自动适配分辨率
        screen = QApplication.primaryScreen()
        self.screen_geometry = screen.geometry()

        print(f"📺 屏幕分辨率: {self.screen_geometry.width()} x {self.screen_geometry.height()}")

        # 设置窗口为全屏 - 触摸屏优化
        self.setGeometry(self.screen_geometry)
        self.showFullScreen()

        # 设置窗口标志 - 无边框，置顶
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)

        # 确保窗口能够正确处理不同分辨率
        self.setMinimumSize(800, 600)  # 设置最小尺寸
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose, True)

        # 计算响应式尺寸
        self._calculate_responsive_sizes()

        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 设置与login_window.py一致的背景样式
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:0.5 #2563eb, stop:1 #3b82f6);
            }
        """)

        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 侧边栏 - 使用响应式宽度
        self.sidebar = SidebarWidget(self)
        self.sidebar.setFixedWidth(self.sidebar_width)
        main_layout.addWidget(self.sidebar)
        
        # 内容区域
        content_frame = QFrame()
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # 顶部工具栏
        self.toolbar = self._create_toolbar()
        content_layout.addWidget(self.toolbar)
        
        # 页面内容
        self.content_stack = QStackedWidget()
        
        # 添加页面
        self.dashboard = DashboardWidget()
        self.content_stack.addWidget(self.dashboard)

        # 添加其他功能页面
        from .inbound_window import InboundWindow
        from .outbound_window import OutboundWindow
        from .query_window import QueryWindow
        from .user_management_window import UserManagementWindow
        from .reports_window import ReportsWindow
        from .settings_window import SettingsWindow

        # 创建功能窗口（嵌入式）
        self.inbound_widget, self.inbound_window = self._create_embedded_widget(InboundWindow)
        self.outbound_widget, self.outbound_window = self._create_embedded_widget(OutboundWindow)
        self.query_widget, self.query_window = self._create_embedded_widget(QueryWindow)
        self.users_widget, self.users_window = self._create_embedded_widget(UserManagementWindow)
        self.reports_widget, self.reports_window = self._create_embedded_widget(ReportsWindow)
        self.settings_widget, self.settings_window = self._create_embedded_widget(SettingsWindow)

        # 连接返回主界面信号 - 修复版本
        if hasattr(self.inbound_window, 'back_to_dashboard'):
            self.inbound_window.back_to_dashboard.connect(self._return_to_dashboard)
        if hasattr(self.outbound_window, 'back_to_dashboard'):
            self.outbound_window.back_to_dashboard.connect(self._return_to_dashboard)
        if hasattr(self.query_window, 'back_to_dashboard'):
            self.query_window.back_to_dashboard.connect(self._return_to_dashboard)
        if hasattr(self.users_window, 'back_to_dashboard'):
            self.users_window.back_to_dashboard.connect(self._return_to_dashboard)
        if hasattr(self.reports_window, 'back_to_dashboard'):
            self.reports_window.back_to_dashboard.connect(self._return_to_dashboard)
        if hasattr(self.settings_window, 'back_to_dashboard'):
            self.settings_window.back_to_dashboard.connect(self._return_to_dashboard)

        self.content_stack.addWidget(self.inbound_widget)
        self.content_stack.addWidget(self.outbound_widget)
        self.content_stack.addWidget(self.query_widget)
        self.content_stack.addWidget(self.users_widget)
        self.content_stack.addWidget(self.reports_widget)
        self.content_stack.addWidget(self.settings_widget)
        
        content_layout.addWidget(self.content_stack)
        
        # 状态栏
        self.status_bar = StatusBar()
        # 设置版权信息到状态栏
        self.status_bar.set_status("© 2024 贵州睿云慧通科技有限公司 - WMS库房自助出入库客户端系统")
        content_layout.addWidget(self.status_bar)
        
        main_layout.addWidget(content_frame)
    
    def _create_toolbar(self):
        """创建工具栏 - 使用响应式高度"""
        toolbar = QFrame()
        toolbar.setFixedHeight(self.toolbar_height)
        
        layout = QHBoxLayout(toolbar)
        # 使用响应式边距
        margin = max(15, min(30, int(self.screen_geometry.width() * 0.02)))
        layout.setContentsMargins(margin, 0, margin, 0)
        
        # 页面标题
        self.page_title = TitleLabel("控制台", level=2)
        layout.addWidget(self.page_title)
        
        layout.addStretch()
        
        # 用户信息
        user_label = BodyLabel("当前用户：管理员")
        layout.addWidget(user_label)

        # 退出按钮 - 使用响应式尺寸
        exit_btn = QPushButton("退出系统")
        btn_width = max(100, min(150, int(self.screen_geometry.width() * 0.08)))
        btn_height = max(35, min(50, int(self.toolbar_height * 0.7)))
        exit_btn.setFixedSize(btn_width, btn_height)
        exit_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc2626;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                margin-left: 10px;
            }
            QPushButton:hover {
                background-color: #b91c1c;
            }
        """)
        exit_btn.clicked.connect(self._handle_exit)
        layout.addWidget(exit_btn)

        # 设置样式
        toolbar.setStyleSheet(f"""
        QFrame {{
            background-color: {theme.colors.WHITE};
            border-bottom: 1px solid {theme.colors.BORDER};
        }}
        """)

        return toolbar
    
    def _setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
        QMainWindow {{
            background-color: {theme.colors.BACKGROUND};
        }}
        """)
    
    def _create_embedded_widget(self, window_class):
        """创建嵌入式窗口组件 - 触摸屏优化，支持滚动"""
        # 创建滚动区域容器
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:0.5 #2563eb, stop:1 #3b82f6);
                border: none;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(255, 255, 255, 0.5);
            }
        """)

        # 创建内容容器
        container = QWidget()
        container.setStyleSheet("""
            QWidget {
                background: transparent;
            }
        """)

        # 设置容器最小尺寸，但允许内容超出时滚动
        container.setMinimumSize(self.content_width - 20, self.content_height - 20)

        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建窗口实例并获取其中央组件
        window = window_class()
        central_widget = window.centralWidget()

        # 将中央组件添加到容器中
        if central_widget:
            central_widget.setParent(container)
            # 设置自适应大小策略
            central_widget.setSizePolicy(
                QSizePolicy.Policy.Expanding,
                QSizePolicy.Policy.Expanding
            )
            layout.addWidget(central_widget)

        # 将容器添加到滚动区域
        scroll_area.setWidget(container)

        return scroll_area, window

    def _setup_connections(self):
        """设置信号连接"""
        self.sidebar.menu_clicked.connect(self._on_menu_changed)

    def _return_to_dashboard(self):
        """返回主界面 - 统一处理方法"""
        # 切换到控制台页面
        self.content_stack.setCurrentWidget(self.dashboard)

        # 更新页面标题
        self.page_title.setText("控制台")

        # 更新状态栏
        self.status_bar.set_status("已返回控制台")

        # 更新侧边栏按钮状态
        if hasattr(self.sidebar, 'menu_buttons'):
            for btn_id, button in self.sidebar.menu_buttons.items():
                if btn_id == "dashboard":
                    self.sidebar._set_active_button(button)
                else:
                    self.sidebar._set_inactive_button(button)

        print("🏠 已返回控制台主界面")
    
    def _on_menu_changed(self, menu_id: str):
        """菜单切换事件"""
        if menu_id == "about":
            self._show_about_dialog()
            return

        menu_titles = {
            "dashboard": "控制台",
            "inbound": "入库管理",
            "outbound": "出库管理",
            "inventory": "库存查询",
            "reports": "报表统计",
            "users": "用户管理",
            "settings": "系统设置",
        }

        title = menu_titles.get(menu_id, "未知页面")
        self.page_title.setText(title)
        self.status_bar.set_status(f"切换到{title}")

        # 切换到对应的页面
        if menu_id == "dashboard":
            self.content_stack.setCurrentWidget(self.dashboard)
        elif menu_id == "inbound":
            self.content_stack.setCurrentWidget(self.inbound_widget)
        elif menu_id == "outbound":
            self.content_stack.setCurrentWidget(self.outbound_widget)
        elif menu_id == "inventory":
            self.content_stack.setCurrentWidget(self.query_widget)
        elif menu_id == "reports":
            self.content_stack.setCurrentWidget(self.reports_widget)
        elif menu_id == "users":
            self.content_stack.setCurrentWidget(self.users_widget)
        elif menu_id == "settings":
            self.content_stack.setCurrentWidget(self.settings_widget)
        else:
            # 默认显示控制台
            self.content_stack.setCurrentWidget(self.dashboard)


    def _show_about_dialog(self):
        """显示关于对话框"""
        try:
            from ..dialogs.about_dialog import show_about_dialog
            show_about_dialog(self)
        except ImportError:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "关于",
                                  "WMS库房自助出入库客户端系统 v1.0.0\n\n"
                                  "© 2024 贵州睿云慧通科技有限公司\n"
                                  "版权所有 · 保留所有权利")

    def _update_user_info(self):
        """更新用户信息显示"""
        if self.user_data:
            user_name = self.user_data.get('full_name', self.user_data.get('username', '未知用户'))
            user_role = self.user_data.get('role', '未知角色')

            # 更新窗口标题
            self.setWindowTitle(f"WMS库房自助出入库客户端系统 - 欢迎 {user_name} ({user_role})")

            # 可以在这里添加更多用户信息的显示逻辑

    def _handle_exit(self):
        """处理退出系统 - 限制验证次数"""
        # 显示管理员验证对话框
        dialog = AdminPasswordDialog(self)
        result = dialog.exec()

        if result == QDialog.DialogCode.Accepted:
            # 检查是否是强制退出（达到最大尝试次数）
            if dialog.exit_attempts >= dialog.max_attempts:
                print("⚠️ 达到最大验证次数，强制退出系统...")
            else:
                print("🔐 管理员验证成功，正在退出系统...")

            # 退出应用程序
            QApplication.quit()
        else:
            print("❌ 管理员验证取消，继续运行系统")

    def resizeEvent(self, event):
        """窗口大小变化事件 - 重新计算响应式尺寸"""
        super().resizeEvent(event)

        # 如果窗口大小发生变化，重新计算响应式尺寸
        if hasattr(self, 'screen_geometry'):
            new_size = event.size()
            if (new_size.width() != self.screen_geometry.width() or
                new_size.height() != self.screen_geometry.height()):

                # 更新屏幕几何信息
                self.screen_geometry = self.geometry()

                # 重新计算响应式尺寸
                self._calculate_responsive_sizes()

                # 更新组件尺寸
                if hasattr(self, 'sidebar'):
                    self.sidebar.setFixedWidth(self.sidebar_width)

                if hasattr(self, 'toolbar'):
                    self.toolbar.setFixedHeight(self.toolbar_height)

                print(f"📐 窗口大小变化，重新计算响应式尺寸")

    def closeEvent(self, event):
        """重写关闭事件"""
        # 阻止直接关闭，需要管理员验证
        event.ignore()
        self._handle_exit()


if __name__ == "__main__":
    app = QApplication([])
    
    # 设置应用程序样式
    app.setStyle("Fusion")
    
    window = MainWindow()
    window.show()
    
    app.exec()
