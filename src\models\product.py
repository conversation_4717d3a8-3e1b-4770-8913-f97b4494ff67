"""
产品相关数据模型
© 2024 贵州睿云慧通科技有限公司
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Numeric, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
import enum

class ProductStatus(enum.Enum):
    """产品状态枚举"""
    ACTIVE = "active"        # 启用
    INACTIVE = "inactive"    # 停用
    DISCONTINUED = "discontinued"  # 停产

class Category(Base):
    """产品类别表"""
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, unique=True)
    code = Column(String(20), unique=True)
    description = Column(Text)
    parent_id = Column(Integer, ForeignKey("categories.id"))
    sort_order = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 自关联关系
    parent = relationship("Category", remote_side=[id], back_populates="children")
    children = relationship("Category", back_populates="parent")
    
    # 产品关系
    products = relationship("Product", back_populates="category")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "parent_id": self.parent_id,
            "sort_order": self.sort_order,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class Product(Base):
    """产品表"""
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    sku = Column(String(50), unique=True, nullable=False, index=True)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    category_id = Column(Integer, ForeignKey("categories.id"))
    unit = Column(String(20), default="件")  # 计量单位
    weight = Column(Numeric(10, 3))  # 重量(kg)
    dimensions = Column(String(50))  # 尺寸(长x宽x高)
    barcode = Column(String(100), unique=True, index=True)
    qr_code = Column(Text)  # 二维码数据
    min_stock_level = Column(Integer, default=0)  # 最小库存
    max_stock_level = Column(Integer, default=1000)  # 最大库存
    reorder_point = Column(Integer, default=10)  # 补货点
    cost_price = Column(Numeric(10, 2))  # 成本价
    sale_price = Column(Numeric(10, 2))  # 销售价
    supplier_id = Column(Integer, ForeignKey("suppliers.id"))
    brand = Column(String(100))  # 品牌
    model = Column(String(100))  # 型号
    color = Column(String(50))   # 颜色
    size = Column(String(50))    # 尺码
    material = Column(String(100))  # 材质
    origin = Column(String(100))    # 产地
    shelf_life = Column(Integer)    # 保质期(天)
    storage_conditions = Column(Text)  # 存储条件
    image_path = Column(String(255))   # 产品图片路径
    status = Column(String(20), default=ProductStatus.ACTIVE.value)
    is_serialized = Column(Boolean, default=False)  # 是否需要序列号管理
    is_batch_managed = Column(Boolean, default=False)  # 是否需要批次管理
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    category = relationship("Category", back_populates="products")
    supplier = relationship("Supplier", back_populates="products")
    inventory_items = relationship("Inventory", back_populates="product")
    inbound_items = relationship("InboundOrderItem", back_populates="product")
    outbound_items = relationship("OutboundOrderItem", back_populates="product")
    serial_numbers = relationship("ProductSerial", back_populates="product")
    batches = relationship("ProductBatch", back_populates="product")
    
    # 索引
    __table_args__ = (
        Index('idx_product_category', 'category_id'),
        Index('idx_product_supplier', 'supplier_id'),
        Index('idx_product_status', 'status'),
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "sku": self.sku,
            "name": self.name,
            "description": self.description,
            "category_id": self.category_id,
            "unit": self.unit,
            "weight": float(self.weight) if self.weight else None,
            "dimensions": self.dimensions,
            "barcode": self.barcode,
            "min_stock_level": self.min_stock_level,
            "max_stock_level": self.max_stock_level,
            "reorder_point": self.reorder_point,
            "cost_price": float(self.cost_price) if self.cost_price else None,
            "sale_price": float(self.sale_price) if self.sale_price else None,
            "supplier_id": self.supplier_id,
            "brand": self.brand,
            "model": self.model,
            "color": self.color,
            "size": self.size,
            "status": self.status,
            "is_serialized": self.is_serialized,
            "is_batch_managed": self.is_batch_managed,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class ProductSerial(Base):
    """产品序列号表"""
    __tablename__ = "product_serials"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    serial_number = Column(String(100), unique=True, nullable=False, index=True)
    status = Column(String(20), default="available")  # available, sold, damaged, returned
    location_id = Column(Integer, ForeignKey("locations.id"))
    inbound_order_id = Column(Integer, ForeignKey("inbound_orders.id"))
    outbound_order_id = Column(Integer, ForeignKey("outbound_orders.id"))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    product = relationship("Product", back_populates="serial_numbers")
    location = relationship("Location")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "product_id": self.product_id,
            "serial_number": self.serial_number,
            "status": self.status,
            "location_id": self.location_id,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class ProductBatch(Base):
    """产品批次表"""
    __tablename__ = "product_batches"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    batch_number = Column(String(100), nullable=False, index=True)
    production_date = Column(DateTime)
    expiry_date = Column(DateTime)
    quantity = Column(Integer, default=0)
    remaining_quantity = Column(Integer, default=0)
    supplier_batch = Column(String(100))  # 供应商批次号
    quality_status = Column(String(20), default="qualified")  # qualified, unqualified, pending
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    product = relationship("Product", back_populates="batches")
    
    # 复合唯一约束
    __table_args__ = (
        Index('idx_product_batch', 'product_id', 'batch_number', unique=True),
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "product_id": self.product_id,
            "batch_number": self.batch_number,
            "production_date": self.production_date.isoformat() if self.production_date else None,
            "expiry_date": self.expiry_date.isoformat() if self.expiry_date else None,
            "quantity": self.quantity,
            "remaining_quantity": self.remaining_quantity,
            "supplier_batch": self.supplier_batch,
            "quality_status": self.quality_status,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class Supplier(Base):
    """供应商表"""
    __tablename__ = "suppliers"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(200), nullable=False)
    contact_person = Column(String(100))
    phone = Column(String(20))
    email = Column(String(100))
    address = Column(Text)
    website = Column(String(200))
    tax_number = Column(String(50))  # 税号
    bank_account = Column(String(100))  # 银行账号
    payment_terms = Column(String(100))  # 付款条件
    credit_rating = Column(String(20))   # 信用等级
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    products = relationship("Product", back_populates="supplier")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "code": self.code,
            "name": self.name,
            "contact_person": self.contact_person,
            "phone": self.phone,
            "email": self.email,
            "address": self.address,
            "website": self.website,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
