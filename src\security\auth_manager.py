"""
WMS认证管理器 - 用户认证和会话管理
© 2024 贵州睿云慧通科技有限公司
"""

import json
from datetime import datetime, timedelta
from typing import Dict, Optional, Any, List, Tuple
from pathlib import Path
from .password_manager import PasswordManager
from .license_manager import LicenseManager


class AuthManager:
    """认证管理器 - 处理用户认证和会话管理"""
    
    def __init__(self):
        self.password_manager = PasswordManager()
        self.license_manager = LicenseManager()
        self.users_file = Path("data/users.json")
        self.sessions_file = Path("data/sessions.json")
        self.current_session = None
        
        # 默认用户角色权限
        self.ROLE_PERMISSIONS = {
            'admin': {
                'system_management': True,
                'user_management': True,
                'inventory_management': True,
                'report_access': True,
                'settings_access': True,
                'backup_restore': True,
                'license_management': True
            },
            'manager': {
                'system_management': False,
                'user_management': True,
                'inventory_management': True,
                'report_access': True,
                'settings_access': True,
                'backup_restore': False,
                'license_management': False
            },
            'operator': {
                'system_management': False,
                'user_management': False,
                'inventory_management': True,
                'report_access': False,
                'settings_access': False,
                'backup_restore': False,
                'license_management': False
            },
            'viewer': {
                'system_management': False,
                'user_management': False,
                'inventory_management': False,
                'report_access': True,
                'settings_access': False,
                'backup_restore': False,
                'license_management': False
            }
        }
        
        # 初始化默认用户
        self._init_default_users()
    
    def _init_default_users(self):
        """初始化默认用户"""
        if not self.users_file.exists():
            # 创建默认管理员用户
            admin_password, admin_salt = self.password_manager.create_admin_password("admin123")
            
            default_users = {
                'admin': {
                    'username': 'admin',
                    'password_hash': admin_password,
                    'salt': admin_salt,
                    'role': 'admin',
                    'full_name': '系统管理员',
                    'email': '<EMAIL>',
                    'department': '管理部',
                    'created_at': datetime.now().isoformat(),
                    'last_login': None,
                    'is_active': True,
                    'login_attempts': 0,
                    'locked_until': None
                }
            }
            
            self._save_users(default_users)
    
    def authenticate_user(self, username: str, password: str) -> Tuple[bool, Dict[str, Any]]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Tuple[bool, Dict]: (认证结果, 用户信息或错误信息)
        """
        try:
            # 检查授权
            is_licensed, license_info = self.license_manager.validate_license()
            if not is_licensed:
                return False, {
                    'error': '系统授权无效',
                    'details': license_info.get('error', '未知错误')
                }
            
            # 加载用户数据
            users = self._load_users()
            
            if username not in users:
                return False, {'error': '用户不存在'}
            
            user = users[username]
            
            # 检查用户是否被锁定
            if user.get('locked_until'):
                locked_until = datetime.fromisoformat(user['locked_until'])
                if datetime.now() < locked_until:
                    remaining_minutes = int((locked_until - datetime.now()).total_seconds() / 60)
                    return False, {
                        'error': f'账户已被锁定，请{remaining_minutes}分钟后重试'
                    }
                else:
                    # 解锁账户
                    user['locked_until'] = None
                    user['login_attempts'] = 0
            
            # 检查用户是否激活
            if not user.get('is_active', True):
                return False, {'error': '账户已被禁用'}
            
            # 验证密码
            is_valid = self.password_manager.verify_password(
                password, 
                user['password_hash'], 
                user['salt']
            )
            
            if is_valid:
                # 登录成功，重置登录尝试次数
                user['login_attempts'] = 0
                user['last_login'] = datetime.now().isoformat()
                user['locked_until'] = None
                
                # 创建会话
                session_token = self._create_session(user)
                
                # 保存用户数据
                users[username] = user
                self._save_users(users)
                
                return True, {
                    'user': {
                        'username': user['username'],
                        'full_name': user['full_name'],
                        'role': user['role'],
                        'department': user.get('department', ''),
                        'permissions': self.ROLE_PERMISSIONS.get(user['role'], {})
                    },
                    'session_token': session_token,
                    'license_info': license_info
                }
            else:
                # 登录失败，增加尝试次数
                user['login_attempts'] = user.get('login_attempts', 0) + 1
                
                # 如果尝试次数过多，锁定账户
                if user['login_attempts'] >= 5:
                    user['locked_until'] = (datetime.now() + timedelta(minutes=30)).isoformat()
                    users[username] = user
                    self._save_users(users)
                    return False, {'error': '登录失败次数过多，账户已被锁定30分钟'}
                
                users[username] = user
                self._save_users(users)
                
                remaining_attempts = 5 - user['login_attempts']
                return False, {
                    'error': f'密码错误，还有{remaining_attempts}次尝试机会'
                }
                
        except Exception as e:
            return False, {'error': f'认证失败: {str(e)}'}
    
    def create_user(self, username: str, password: str, role: str, 
                   full_name: str, email: str = '', department: str = '') -> Tuple[bool, str]:
        """
        创建用户
        
        Args:
            username: 用户名
            password: 密码
            role: 角色
            full_name: 全名
            email: 邮箱
            department: 部门
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 检查权限
            if not self._check_permission('user_management'):
                return False, '没有用户管理权限'
            
            # 验证密码强度
            is_strong, message = self.password_manager.validate_password_strength(password)
            if not is_strong:
                return False, f'密码不符合要求: {message}'
            
            # 检查角色是否有效
            if role not in self.ROLE_PERMISSIONS:
                return False, f'无效的角色: {role}'
            
            # 加载用户数据
            users = self._load_users()
            
            # 检查用户是否已存在
            if username in users:
                return False, '用户名已存在'
            
            # 检查用户数量限制
            license_info = self.license_manager.get_license_status()
            if license_info['status'] == 'valid':
                max_users = license_info['info']['features'].get('max_users', 0)
                if max_users > 0 and len(users) >= max_users:
                    return False, f'用户数量已达到授权限制({max_users}个)'
            
            # 创建用户
            password_hash, salt = self.password_manager.hash_password(password)
            
            users[username] = {
                'username': username,
                'password_hash': password_hash,
                'salt': salt,
                'role': role,
                'full_name': full_name,
                'email': email,
                'department': department,
                'created_at': datetime.now().isoformat(),
                'last_login': None,
                'is_active': True,
                'login_attempts': 0,
                'locked_until': None
            }
            
            # 保存用户数据
            self._save_users(users)
            
            return True, '用户创建成功'
            
        except Exception as e:
            return False, f'创建用户失败: {str(e)}'
    
    def change_password(self, username: str, old_password: str, new_password: str) -> Tuple[bool, str]:
        """
        修改密码
        
        Args:
            username: 用户名
            old_password: 旧密码
            new_password: 新密码
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 验证新密码强度
            is_strong, message = self.password_manager.validate_password_strength(new_password)
            if not is_strong:
                return False, f'新密码不符合要求: {message}'
            
            # 加载用户数据
            users = self._load_users()
            
            if username not in users:
                return False, '用户不存在'
            
            user = users[username]
            
            # 验证旧密码
            if not self.password_manager.verify_password(old_password, user['password_hash'], user['salt']):
                return False, '旧密码错误'
            
            # 设置新密码
            new_hash, new_salt = self.password_manager.hash_password(new_password)
            user['password_hash'] = new_hash
            user['salt'] = new_salt
            
            # 保存用户数据
            users[username] = user
            self._save_users(users)
            
            return True, '密码修改成功'
            
        except Exception as e:
            return False, f'修改密码失败: {str(e)}'
    
    def _create_session(self, user: Dict) -> str:
        """创建用户会话"""
        session_token = self.password_manager.generate_session_token()
        
        session_data = {
            'token': session_token,
            'username': user['username'],
            'role': user['role'],
            'created_at': datetime.now().isoformat(),
            'expires_at': (datetime.now() + timedelta(hours=8)).isoformat(),
            'last_activity': datetime.now().isoformat()
        }
        
        # 保存会话
        sessions = self._load_sessions()
        sessions[session_token] = session_data
        self._save_sessions(sessions)
        
        self.current_session = session_data
        return session_token
    
    def _check_permission(self, permission: str) -> bool:
        """检查当前用户权限"""
        if not self.current_session:
            return False
        
        role = self.current_session.get('role', '')
        permissions = self.ROLE_PERMISSIONS.get(role, {})
        return permissions.get(permission, False)
    
    def _load_users(self) -> Dict:
        """加载用户数据"""
        try:
            if self.users_file.exists():
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        return {}
    
    def _save_users(self, users: Dict):
        """保存用户数据"""
        self.users_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.users_file, 'w', encoding='utf-8') as f:
            json.dump(users, f, ensure_ascii=False, indent=2)
    
    def _load_sessions(self) -> Dict:
        """加载会话数据"""
        try:
            if self.sessions_file.exists():
                with open(self.sessions_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        return {}
    
    def _save_sessions(self, sessions: Dict):
        """保存会话数据"""
        self.sessions_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.sessions_file, 'w', encoding='utf-8') as f:
            json.dump(sessions, f, ensure_ascii=False, indent=2)


# 全局认证管理器实例
auth_manager = AuthManager()
