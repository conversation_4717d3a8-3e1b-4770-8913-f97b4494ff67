# WMS库房自助出入库客户端系统 - 打包依赖
# 仅包含运行时必需的核心依赖，用于减少打包体积

# 核心GUI框架
PyQt6==6.6.1

# 数据库相关
SQLAlchemy==2.0.23
sqlite3  # Python内置

# 基础图像处理
opencv-python==4.8.1.78
Pillow==10.1.0
numpy==1.24.3

# 条码和二维码识别
pyzbar==0.1.9
qrcode==7.4.2

# 加密和安全
cryptography>=41.0.8
bcrypt==4.1.2

# 网络请求
requests==2.31.0

# 配置和日志
colorlog==6.8.0

# 数据处理
pandas==2.1.4
openpyxl==3.1.2

# 时间处理
python-dateutil==2.8.2

# 系统工具
psutil==5.9.6

# Windows特定
pywin32==306; sys_platform == "win32"

# 打包工具
PyInstaller==6.2.0

# 可选的OCR功能（如果需要）
# pytesseract==0.3.10

# 可选的人脸识别功能（如果需要，但会显著增加体积）
# face-recognition==1.3.0
# dlib==19.24.2

# 可选的高级OCR功能（如果需要，但会显著增加体积）
# paddleocr==2.7.0.3
# easyocr==1.7.0

# 注意：
# 1. 此文件仅包含核心运行时依赖
# 2. 如需完整功能，请使用 requirements.txt
# 3. 某些功能（如人脸识别、高级OCR）会显著增加打包体积
# 4. 建议根据实际需求选择性安装依赖
