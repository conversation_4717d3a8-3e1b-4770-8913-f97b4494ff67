"""
窗口管理器 - 提供流畅的窗口切换效果
"""

from PyQt6.QtWidgets import QWidget, QGraphicsOpacityEffect
from PyQt6.QtCore import QPropertyAnimation, QEasingCurve, QTimer, pyqtSignal, QObject
from PyQt6.QtGui import QScreen
from typing import Optional, Callable


class WindowTransition(QObject):
    """窗口过渡动画管理器"""
    
    transition_finished = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.current_animation = None
        self.fade_duration = 300  # 动画持续时间（毫秒）
    
    def fade_out(self, widget: QWidget, callback: Optional[Callable] = None):
        """淡出动画"""
        if not widget or not widget.isVisible():
            if callback:
                callback()
            return
        
        # 创建透明度效果
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        # 创建动画
        self.current_animation = QPropertyAnimation(effect, b"opacity")
        self.current_animation.setDuration(self.fade_duration)
        self.current_animation.setStartValue(1.0)
        self.current_animation.setEndValue(0.0)
        self.current_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 动画完成后的处理
        def on_fade_out_finished():
            widget.hide()
            widget.setGraphicsEffect(None)  # 移除效果
            if callback:
                callback()
            self.transition_finished.emit()
        
        self.current_animation.finished.connect(on_fade_out_finished)
        self.current_animation.start()
    
    def fade_in(self, widget: QWidget, callback: Optional[Callable] = None):
        """淡入动画"""
        if not widget:
            if callback:
                callback()
            return
        
        # 显示窗口
        widget.show()
        
        # 创建透明度效果
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        effect.setOpacity(0.0)
        
        # 创建动画
        self.current_animation = QPropertyAnimation(effect, b"opacity")
        self.current_animation.setDuration(self.fade_duration)
        self.current_animation.setStartValue(0.0)
        self.current_animation.setEndValue(1.0)
        self.current_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 动画完成后的处理
        def on_fade_in_finished():
            widget.setGraphicsEffect(None)  # 移除效果
            if callback:
                callback()
            self.transition_finished.emit()
        
        self.current_animation.finished.connect(on_fade_in_finished)
        self.current_animation.start()
    
    def cross_fade(self, from_widget: QWidget, to_widget: QWidget, callback: Optional[Callable] = None):
        """交叉淡化：同时淡出旧窗口和淡入新窗口"""
        if not from_widget and not to_widget:
            if callback:
                callback()
            return
        
        # 如果只有一个窗口，使用简单的淡入或淡出
        if not from_widget:
            self.fade_in(to_widget, callback)
            return
        
        if not to_widget:
            self.fade_out(from_widget, callback)
            return
        
        # 准备新窗口的位置和大小
        if from_widget.isVisible():
            to_widget.setGeometry(from_widget.geometry())
        
        # 同时执行淡出和淡入
        completed_count = 0
        
        def on_animation_finished():
            nonlocal completed_count
            completed_count += 1
            if completed_count >= 2:  # 两个动画都完成
                if callback:
                    callback()
                self.transition_finished.emit()
        
        # 淡出旧窗口
        self.fade_out(from_widget, on_animation_finished)
        
        # 延迟一点时间后淡入新窗口，创造更自然的过渡
        QTimer.singleShot(50, lambda: self.fade_in(to_widget, on_animation_finished))


class WindowManager(QObject):
    """窗口管理器 - 管理应用程序中的所有窗口切换"""
    
    def __init__(self):
        super().__init__()
        self.transition = WindowTransition()
        self.current_window = None
        self.window_cache = {}  # 窗口缓存
    
    def register_window(self, window_id: str, window: QWidget):
        """注册窗口到缓存"""
        self.window_cache[window_id] = window
    
    def get_window(self, window_id: str) -> Optional[QWidget]:
        """获取缓存的窗口"""
        return self.window_cache.get(window_id)
    
    def switch_to_window(self, window_id: str, window: Optional[QWidget] = None, 
                        use_animation: bool = True, callback: Optional[Callable] = None):
        """切换到指定窗口"""
        # 获取目标窗口
        target_window = window or self.get_window(window_id)
        
        if not target_window:
            print(f"警告：窗口 {window_id} 不存在")
            if callback:
                callback()
            return
        
        # 如果是同一个窗口，不需要切换
        if target_window == self.current_window:
            if callback:
                callback()
            return
        
        # 缓存窗口
        if window_id not in self.window_cache:
            self.register_window(window_id, target_window)
        
        # 执行切换
        if use_animation and self.current_window:
            # 使用动画切换
            self.transition.cross_fade(
                self.current_window, 
                target_window,
                callback
            )
        else:
            # 直接切换
            if self.current_window:
                self.current_window.hide()
            target_window.show()
            if callback:
                callback()
        
        # 更新当前窗口
        self.current_window = target_window
    
    def hide_current_window(self, use_animation: bool = True, callback: Optional[Callable] = None):
        """隐藏当前窗口"""
        if not self.current_window:
            if callback:
                callback()
            return
        
        if use_animation:
            self.transition.fade_out(self.current_window, callback)
        else:
            self.current_window.hide()
            if callback:
                callback()
        
        self.current_window = None
    
    def show_window(self, window_id: str, window: Optional[QWidget] = None,
                   use_animation: bool = True, callback: Optional[Callable] = None):
        """显示窗口"""
        target_window = window or self.get_window(window_id)
        
        if not target_window:
            print(f"警告：窗口 {window_id} 不存在")
            if callback:
                callback()
            return
        
        # 缓存窗口
        if window_id not in self.window_cache:
            self.register_window(window_id, target_window)
        
        if use_animation:
            self.transition.fade_in(target_window, callback)
        else:
            target_window.show()
            if callback:
                callback()
        
        self.current_window = target_window
    
    def clear_cache(self):
        """清空窗口缓存"""
        self.window_cache.clear()
        self.current_window = None


# 全局窗口管理器实例
window_manager = WindowManager()
