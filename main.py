"""
WMS库房自助出入库客户端系统 - 主程序入口
© 2024 贵州睿云慧通科技有限公司
"""

import sys
import os
import traceback
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt, QTimer, QObject
from PyQt6.QtGui import QIcon

from src.models.database import init_database
from src.ui.dialogs.splash_screen import SplashScreen
from src.ui.windows.login_window import LoginWindow
from src.ui.utils.window_manager import window_manager
from src.utils.logger import setup_logging, get_logger
from src.config.copyright import print_copyright

# 设置日志
setup_logging()
logger = get_logger(__name__)

class WMSApplication(QObject):
    """WMS应用程序主类"""
    
    def __init__(self):
        super().__init__()
        self.app = None
        self.splash = None
        self.login_window = None
        self.dashboard_window = None
        self.main_window = None
        
    def initialize(self):
        """初始化应用程序"""
        try:
            # 创建QApplication
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("WMS库房自助出入库客户端系统")
            self.app.setApplicationVersion("1.0.0")
            self.app.setOrganizationName("贵州睿云慧通科技有限公司")

            # 设置应用程序图标
            icon_path = Path("resources/icons/app_icon.ico")
            if icon_path.exists():
                self.app.setWindowIcon(QIcon(str(icon_path)))

            # 设置全局样式
            self._setup_global_style()

            # 安装事件过滤器，防止启动画面被其他窗口打断
            self.app.installEventFilter(self)

            # 显示版权信息
            print_copyright()

            logger.info("应用程序初始化完成")
            return True

        except Exception as e:
            logger.error(f"应用程序初始化失败: {e}")
            self._show_error("初始化失败", f"应用程序初始化失败：{str(e)}")
            return False
    
    def _setup_global_style(self):
        """设置全局样式"""
        style = """
        QApplication {
            font-family: "Microsoft YaHei", "SimHei", sans-serif;
            font-size: 9pt;
        }
        
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QWidget {
            background-color: white;
            color: #333333;
        }
        
        QPushButton {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #005a9e;
        }
        
        QPushButton:pressed {
            background-color: #004578;
        }
        
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        
        QLineEdit {
            border: 2px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            font-size: 10pt;
        }
        
        QLineEdit:focus {
            border-color: #007acc;
        }
        
        QTableWidget {
            gridline-color: #e0e0e0;
            background-color: white;
            alternate-background-color: #f9f9f9;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        QTableWidget::item:selected {
            background-color: #007acc;
            color: white;
        }
        
        QHeaderView::section {
            background-color: #f0f0f0;
            padding: 8px;
            border: none;
            border-bottom: 2px solid #007acc;
            font-weight: bold;
        }
        """
        self.app.setStyleSheet(style)

    def eventFilter(self, obj, event):
        """事件过滤器，保护启动画面不被打断"""
        from PyQt6.QtCore import QEvent

        # 如果启动画面正在显示，拦截所有可能影响它的事件
        if self.splash and self.splash.isVisible():
            # 更全面的事件拦截列表
            event_types_to_block = [
                QEvent.Type.WindowActivate,
                QEvent.Type.WindowDeactivate,
                QEvent.Type.FocusIn,
                QEvent.Type.FocusOut,
                QEvent.Type.Show,
                QEvent.Type.Hide,
                QEvent.Type.WindowStateChange,
                QEvent.Type.ActivationChange,
                QEvent.Type.ApplicationDeactivate,
                QEvent.Type.ApplicationActivate,
                QEvent.Type.MouseButtonPress,      # 鼠标点击
                QEvent.Type.MouseButtonRelease,    # 鼠标释放
                QEvent.Type.KeyPress,              # 键盘按键
                QEvent.Type.KeyRelease,            # 键盘释放
            ]

            if event.type() in event_types_to_block and obj != self.splash:
                # 强制将焦点返回给启动画面
                QTimer.singleShot(0, lambda: self._force_splash_focus())
                # 阻止所有这些事件的传播
                return True

        return super().eventFilter(obj, event) if hasattr(super(), 'eventFilter') else False

    def _force_splash_focus(self):
        """强制启动画面获得焦点"""
        if self.splash and self.splash.isVisible():
            self.splash.raise_()
            self.splash.activateWindow()
            self.splash.setFocus()
    
    def show_splash(self):
        """显示启动画面"""
        try:
            self.splash = SplashScreen()

            # 确保启动画面置顶显示且不被打断
            self.splash.show()
            self.splash.raise_()
            self.splash.activateWindow()

            # 创建强制置顶定时器，确保启动画面始终在最前面
            self.splash_keep_top_timer = QTimer()
            self.splash_keep_top_timer.timeout.connect(self._keep_splash_on_top)
            self.splash_keep_top_timer.start(50)  # 每50ms检查一次，更频繁

            # 创建全局焦点监控定时器
            self.focus_monitor_timer = QTimer()
            self.focus_monitor_timer.timeout.connect(self._monitor_focus)
            self.focus_monitor_timer.start(20)  # 每20ms监控焦点，更频繁

            # 创建鼠标位置监控定时器
            self.mouse_monitor_timer = QTimer()
            self.mouse_monitor_timer.timeout.connect(self._monitor_mouse)
            self.mouse_monitor_timer.start(50)  # 每50ms监控鼠标

            # 模拟初始化过程
            QTimer.singleShot(1000, lambda: self._update_splash_progress(20, "正在初始化数据库..."))
            QTimer.singleShot(2000, lambda: self._update_splash_progress(40, "正在加载配置..."))
            QTimer.singleShot(3000, lambda: self._update_splash_progress(60, "正在初始化设备..."))
            QTimer.singleShot(4000, lambda: self._update_splash_progress(80, "正在加载界面..."))
            QTimer.singleShot(5000, lambda: self._update_splash_progress(100, "初始化完成"))
            QTimer.singleShot(5500, self._finish_splash)

            logger.info("启动画面显示完成")

        except Exception as e:
            logger.error(f"显示启动画面失败: {e}")
            self._finish_splash()

    def _keep_splash_on_top(self):
        """保持启动画面置顶"""
        if self.splash and self.splash.isVisible():
            # 多重强制置顶策略
            self.splash.raise_()
            self.splash.activateWindow()
            self.splash.setFocus()

            # 确保应用程序获得焦点
            self.app.setActiveWindow(self.splash)

            # 强制处理所有待处理的事件
            self.app.processEvents()

            # 如果窗口被最小化，恢复它
            if self.splash.windowState() & Qt.WindowState.WindowMinimized:
                self.splash.setWindowState(Qt.WindowState.WindowActive)

    def _monitor_focus(self):
        """监控焦点变化，强制保持启动画面焦点"""
        if self.splash and self.splash.isVisible():
            active_window = self.app.activeWindow()
            if active_window != self.splash:
                # 如果焦点不在启动画面上，强制夺回
                self.splash.raise_()
                self.splash.activateWindow()
                self.splash.setFocus()
                self.app.setActiveWindow(self.splash)
                # 强制处理事件
                self.app.processEvents()

    def _monitor_mouse(self):
        """监控鼠标活动，确保点击不会影响启动画面"""
        if self.splash and self.splash.isVisible():
            # 获取鼠标位置
            from PyQt6.QtGui import QCursor
            mouse_pos = QCursor.pos()

            # 如果鼠标在启动画面区域外，也要确保启动画面保持焦点
            splash_geometry = self.splash.geometry()
            if not splash_geometry.contains(mouse_pos):
                # 强制保持启动画面在最前面
                self.splash.raise_()
                self.splash.activateWindow()

    def _update_splash_progress(self, progress, message):
        """更新启动画面进度，确保窗口保持置顶"""
        if self.splash:
            self.splash.update_progress(progress, message)
            # 确保启动画面始终置顶
            self.splash.raise_()
            self.splash.activateWindow()
            # 强制刷新显示
            self.splash.repaint()
            # 处理事件队列，确保界面更新
            self.app.processEvents()
    
    def _finish_splash(self):
        """完成启动画面"""
        try:
            # 停止所有定时器
            if hasattr(self, 'splash_keep_top_timer'):
                self.splash_keep_top_timer.stop()
                self.splash_keep_top_timer = None

            if hasattr(self, 'focus_monitor_timer'):
                self.focus_monitor_timer.stop()
                self.focus_monitor_timer = None

            if hasattr(self, 'mouse_monitor_timer'):
                self.mouse_monitor_timer.stop()
                self.mouse_monitor_timer = None

            if self.splash:
                # 停止启动画面内部的置顶定时器
                if hasattr(self.splash, 'stay_on_top_timer'):
                    self.splash.stay_on_top_timer.stop()
                self.splash.close()
                self.splash = None

            # 移除事件过滤器
            self.app.removeEventFilter(self)

            # 初始化数据库
            self._initialize_database()

            # 显示登录窗口
            self.show_login()

        except Exception as e:
            logger.error(f"完成启动失败: {e}")
            self._show_error("启动失败", f"系统启动失败：{str(e)}")
            sys.exit(1)
    
    def _initialize_database(self):
        """初始化数据库"""
        try:
            logger.info("正在初始化数据库...")
            init_database()
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def show_login(self):
        """显示登录窗口"""
        try:
            from src.ui.windows.login_window import LoginWindow
            self.login_window = LoginWindow()
            self.login_window.login_success.connect(self._on_login_success)
            self.login_window.show()

            logger.info("登录窗口显示完成")

        except Exception as e:
            logger.error(f"显示登录窗口失败: {e}")
            self._show_error("登录失败", f"无法显示登录窗口：{str(e)}")
    
    def _on_login_success(self, user_data):
        """登录成功处理"""
        try:
            logger.info(f"用户登录成功: {user_data.get('username')}")

            # 隐藏登录窗口
            if self.login_window:
                self.login_window.hide()

            # 显示4卡片界面
            self.show_dashboard_window(user_data)

            # 清空登录窗口引用
            self.login_window = None

        except Exception as e:
            logger.error(f"登录成功处理失败: {e}")
            self._show_error("登录失败", f"登录处理失败：{str(e)}")

    def show_dashboard_window(self, user_data):
        """显示4卡片界面"""
        try:
            from src.ui.windows.dashboard_window import DashboardWindow

            self.dashboard_window = DashboardWindow()
            self.dashboard_window.admin_login_success.connect(lambda admin_info: self._on_admin_login_success(admin_info, user_data))
            self.dashboard_window.showFullScreen()

            logger.info("4卡片界面显示完成")

        except Exception as e:
            logger.error(f"显示4卡片界面失败: {e}")
            self._show_error("系统错误", f"无法显示4卡片界面：{str(e)}")

    def _on_admin_login_success(self, admin_info, original_user_data):
        """管理员登录成功处理"""
        try:
            logger.info(f"管理员登录成功: {admin_info.get('username')}")

            # 隐藏4卡片窗口
            if self.dashboard_window:
                self.dashboard_window.hide()

            # 显示主窗口
            self.show_main_window(admin_info)

            # 清空4卡片窗口引用
            self.dashboard_window = None

        except Exception as e:
            logger.error(f"管理员登录处理失败: {e}")
            self._show_error("系统错误", f"管理员登录处理失败：{str(e)}")

    def _on_admin_login_failed(self):
        """管理员登录失败处理"""
        # 验证失败时保持在dashboard_window，不做任何操作
        logger.info("管理员验证失败，返回4卡片界面")

    def show_main_window(self, user_data):
        """显示主窗口"""
        try:
            from src.ui.windows.main_window import MainWindow

            self.main_window = MainWindow(user_data)
            self.main_window.showMaximized()

            logger.info("主窗口显示完成")

        except Exception as e:
            logger.error(f"显示主窗口失败: {e}")
            self._show_error("系统错误", f"无法显示主窗口：{str(e)}")
    
    def _show_error(self, title, message):
        """显示错误消息"""
        try:
            if self.app:
                QMessageBox.critical(None, title, message)
            else:
                print(f"错误: {title} - {message}")
        except:
            print(f"错误: {title} - {message}")
    
    def run(self):
        """运行应用程序"""
        try:
            if not self.initialize():
                return 1
            
            # 显示启动画面
            self.show_splash()
            
            # 运行事件循环
            return self.app.exec()
            
        except Exception as e:
            logger.error(f"应用程序运行失败: {e}")
            logger.error(traceback.format_exc())
            self._show_error("系统错误", f"应用程序运行失败：{str(e)}")
            return 1
        finally:
            self._cleanup()
    
    def _cleanup(self):
        """清理资源"""
        try:
            logger.info("正在清理资源...")
            
            if self.main_window:
                self.main_window.close()

            if self.dashboard_window:
                self.dashboard_window.close()

            if self.login_window:
                self.login_window.close()

            if self.splash:
                self.splash.close()
            
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"资源清理失败: {e}")

def main():
    """主函数"""
    try:
        # 设置高DPI支持
        if hasattr(Qt.ApplicationAttribute, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        if hasattr(Qt.ApplicationAttribute, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        
        # 创建并运行应用程序
        app = WMSApplication()
        return app.run()
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        return 0
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
