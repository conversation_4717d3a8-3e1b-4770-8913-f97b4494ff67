"""
WMS授权管理器 - 软件授权控制
© 2024 贵州睿云慧通科技有限公司
"""

import json
import hashlib
import base64
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, Any
from pathlib import Path
import secrets
from .password_manager import PasswordManager


class LicenseManager:
    """授权管理器 - 控制软件使用授权"""
    
    def __init__(self):
        self.password_manager = PasswordManager()
        self.license_file = Path("data/license.dat")
        self.machine_id = self.password_manager.hash_machine_id()
        
        # 授权类型
        self.LICENSE_TYPES = {
            'trial': '试用版',
            'standard': '标准版', 
            'professional': '专业版',
            'enterprise': '企业版'
        }
        
        # 功能权限映射
        self.FEATURE_PERMISSIONS = {
            'trial': {
                'max_users': 2,
                'max_products': 100,
                'advanced_reports': False,
                'api_access': False,
                'multi_warehouse': False,
                'face_recognition': False,
                'barcode_scanning': True,
                'basic_inventory': True
            },
            'standard': {
                'max_users': 10,
                'max_products': 1000,
                'advanced_reports': True,
                'api_access': False,
                'multi_warehouse': False,
                'face_recognition': True,
                'barcode_scanning': True,
                'basic_inventory': True
            },
            'professional': {
                'max_users': 50,
                'max_products': 10000,
                'advanced_reports': True,
                'api_access': True,
                'multi_warehouse': True,
                'face_recognition': True,
                'barcode_scanning': True,
                'basic_inventory': True
            },
            'enterprise': {
                'max_users': -1,  # 无限制
                'max_products': -1,  # 无限制
                'advanced_reports': True,
                'api_access': True,
                'multi_warehouse': True,
                'face_recognition': True,
                'barcode_scanning': True,
                'basic_inventory': True
            }
        }
    
    def generate_license_key(self, license_type: str, days: int = 365, 
                           customer_info: Optional[Dict] = None) -> str:
        """
        生成授权密钥
        
        Args:
            license_type: 授权类型
            days: 有效天数
            customer_info: 客户信息
            
        Returns:
            str: 授权密钥
        """
        if license_type not in self.LICENSE_TYPES:
            raise ValueError(f"无效的授权类型: {license_type}")
        
        # 创建授权数据
        license_data = {
            'type': license_type,
            'machine_id': self.machine_id,
            'issue_date': datetime.now().isoformat(),
            'expire_date': (datetime.now() + timedelta(days=days)).isoformat(),
            'customer': customer_info or {},
            'features': self.FEATURE_PERMISSIONS[license_type],
            'version': '1.0.0',
            'issuer': '贵州睿云慧通科技有限公司'
        }
        
        # 序列化并加密
        license_json = json.dumps(license_data, ensure_ascii=False)
        
        # 生成签名
        signature = self._generate_signature(license_json)
        
        # 组合授权数据和签名
        full_license = {
            'data': license_data,
            'signature': signature
        }
        
        # 加密整个授权信息
        encryption_key = self._generate_license_key()
        encrypted_license = self.password_manager.encrypt_data(
            json.dumps(full_license, ensure_ascii=False), 
            encryption_key
        )
        
        # 生成最终的授权密钥
        license_key = base64.b64encode(
            f"{encryption_key}:{encrypted_license}".encode('utf-8')
        ).decode('utf-8')
        
        return license_key
    
    def validate_license(self, license_key: Optional[str] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        验证授权
        
        Args:
            license_key: 授权密钥（可选，不提供则从文件读取）
            
        Returns:
            Tuple[bool, Dict]: (是否有效, 授权信息)
        """
        try:
            if license_key is None:
                license_key = self._load_license_from_file()
            
            if not license_key:
                return False, {'error': '未找到授权信息'}
            
            # 解析授权密钥
            try:
                decoded = base64.b64decode(license_key.encode('utf-8')).decode('utf-8')
                encryption_key, encrypted_license = decoded.split(':', 1)
            except Exception:
                return False, {'error': '授权密钥格式错误'}
            
            # 解密授权信息
            try:
                license_json = self.password_manager.decrypt_data(encrypted_license, encryption_key)
                license_info = json.loads(license_json)
            except Exception:
                return False, {'error': '授权密钥解密失败'}
            
            # 验证签名
            if not self._verify_signature(
                json.dumps(license_info['data'], ensure_ascii=False),
                license_info['signature']
            ):
                return False, {'error': '授权签名验证失败'}
            
            license_data = license_info['data']
            
            # 验证机器ID
            if license_data.get('machine_id') != self.machine_id:
                return False, {'error': '授权与当前机器不匹配'}
            
            # 验证有效期
            expire_date = datetime.fromisoformat(license_data['expire_date'])
            if datetime.now() > expire_date:
                return False, {'error': '授权已过期', 'expire_date': expire_date.isoformat()}
            
            # 返回授权信息
            return True, {
                'valid': True,
                'type': license_data['type'],
                'type_name': self.LICENSE_TYPES.get(license_data['type'], '未知'),
                'expire_date': expire_date.isoformat(),
                'days_remaining': (expire_date - datetime.now()).days,
                'features': license_data['features'],
                'customer': license_data.get('customer', {}),
                'version': license_data.get('version', '1.0.0')
            }
            
        except Exception as e:
            return False, {'error': f'授权验证失败: {str(e)}'}
    
    def save_license(self, license_key: str) -> bool:
        """
        保存授权到文件
        
        Args:
            license_key: 授权密钥
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 先验证授权
            is_valid, info = self.validate_license(license_key)
            if not is_valid:
                return False
            
            # 确保目录存在
            self.license_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存到文件
            with open(self.license_file, 'w', encoding='utf-8') as f:
                f.write(license_key)
            
            return True
        except Exception:
            return False
    
    def _load_license_from_file(self) -> Optional[str]:
        """从文件加载授权"""
        try:
            if self.license_file.exists():
                with open(self.license_file, 'r', encoding='utf-8') as f:
                    return f.read().strip()
        except Exception:
            pass
        return None
    
    def _generate_license_key(self) -> str:
        """生成授权加密密钥"""
        return self.password_manager.generate_secure_key()
    
    def _generate_signature(self, data: str) -> str:
        """生成数据签名"""
        # 使用机器ID和固定密钥生成签名
        secret_key = f"WMS_LICENSE_2024_{self.machine_id}"
        signature_data = f"{data}:{secret_key}"
        return hashlib.sha256(signature_data.encode('utf-8')).hexdigest()
    
    def _verify_signature(self, data: str, signature: str) -> bool:
        """验证数据签名"""
        expected_signature = self._generate_signature(data)
        return secrets.compare_digest(signature, expected_signature)
    
    def get_trial_license(self, days: int = 30) -> str:
        """
        生成试用授权
        
        Args:
            days: 试用天数
            
        Returns:
            str: 试用授权密钥
        """
        customer_info = {
            'name': '试用用户',
            'company': '试用公司',
            'email': '<EMAIL>'
        }
        
        return self.generate_license_key('trial', days, customer_info)
    
    def check_feature_permission(self, feature: str) -> bool:
        """
        检查功能权限
        
        Args:
            feature: 功能名称
            
        Returns:
            bool: 是否有权限
        """
        is_valid, license_info = self.validate_license()
        
        if not is_valid:
            return False
        
        features = license_info.get('features', {})
        return features.get(feature, False)
    
    def get_license_status(self) -> Dict[str, Any]:
        """获取授权状态"""
        is_valid, license_info = self.validate_license()
        
        if is_valid:
            return {
                'status': 'valid',
                'message': '授权有效',
                'info': license_info
            }
        else:
            return {
                'status': 'invalid',
                'message': license_info.get('error', '授权无效'),
                'info': {}
            }


# 全局授权管理器实例
license_manager = LicenseManager()
