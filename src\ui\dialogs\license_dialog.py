"""
WMS授权对话框 - 授权管理界面
© 2024 贵州睿云慧通科技有限公司
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QTextEdit, QTabWidget, QWidget, QFormLayout,
    QMessageBox, QProgressBar, QGroupBox, QApplication
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont
from datetime import datetime
from ...security.license_manager import license_manager


class LicenseDialog(QDialog):
    """授权管理对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("WMS系统授权管理")
        
        # 设置对话框大小 - 黄金比例优化
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        
        self.golden_ratio = 1.618
        base_width = max(600, min(800, int(screen_geometry.width() * 0.5)))
        self.dialog_width = base_width
        self.dialog_height = int(base_width / self.golden_ratio * 1.2)
        
        self.setFixedSize(self.dialog_width, self.dialog_height)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowStaysOnTopHint)
        self.setModal(True)
        
        self._setup_ui()
        self._load_license_status()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("🔐 WMS系统授权管理")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #1e40af;
                font-size: 24px;
                font-weight: bold;
                font-family: "Microsoft YaHei UI", sans-serif;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f1f5f9;
                color: #64748b;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: 500;
            }
            QTabBar::tab:selected {
                background-color: #1e40af;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #e2e8f0;
            }
        """)
        
        # 授权状态选项卡
        self._create_status_tab()
        
        # 激活授权选项卡
        self._create_activation_tab()
        
        # 试用授权选项卡
        self._create_trial_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 刷新状态")
        self.refresh_btn.setFixedHeight(40)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6, stop:1 #1d4ed8);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1d4ed8, stop:1 #1e3a8a);
            }
        """)
        self.refresh_btn.clicked.connect(self._load_license_status)
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.setFixedHeight(40)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6b7280, stop:1 #4b5563);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4b5563, stop:1 #374151);
            }
        """)
        self.close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        # 设置对话框样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(248, 250, 252, 0.98),
                    stop:1 rgba(241, 245, 249, 0.95));
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
    
    def _create_status_tab(self):
        """创建授权状态选项卡"""
        status_widget = QWidget()
        layout = QVBoxLayout(status_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 状态显示区域
        self.status_group = QGroupBox("📊 当前授权状态")
        self.status_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: 600;
                color: #1e293b;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        status_layout = QFormLayout(self.status_group)
        
        # 状态标签
        self.status_label = QLabel("检查中...")
        self.type_label = QLabel("--")
        self.expire_label = QLabel("--")
        self.remaining_label = QLabel("--")
        
        status_layout.addRow("授权状态:", self.status_label)
        status_layout.addRow("授权类型:", self.type_label)
        status_layout.addRow("到期时间:", self.expire_label)
        status_layout.addRow("剩余天数:", self.remaining_label)
        
        layout.addWidget(self.status_group)
        
        # 功能权限显示
        self.features_group = QGroupBox("🔧 功能权限")
        self.features_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: 600;
                color: #1e293b;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        self.features_text = QTextEdit()
        self.features_text.setReadOnly(True)
        self.features_text.setMaximumHeight(150)
        self.features_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 10px;
                font-family: "Consolas", monospace;
                font-size: 12px;
            }
        """)
        
        features_layout = QVBoxLayout(self.features_group)
        features_layout.addWidget(self.features_text)
        
        layout.addWidget(self.features_group)
        
        self.tab_widget.addTab(status_widget, "📊 授权状态")
    
    def _create_activation_tab(self):
        """创建激活授权选项卡"""
        activation_widget = QWidget()
        layout = QVBoxLayout(activation_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 说明文字
        info_label = QLabel("请输入您的授权密钥来激活WMS系统:")
        info_label.setStyleSheet("""
            QLabel {
                color: #64748b;
                font-size: 14px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(info_label)
        
        # 授权密钥输入
        self.license_key_input = QTextEdit()
        self.license_key_input.setPlaceholderText("请粘贴您的授权密钥...")
        self.license_key_input.setMaximumHeight(120)
        self.license_key_input.setStyleSheet("""
            QTextEdit {
                background-color: #f8fafc;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 10px;
                font-family: "Consolas", monospace;
                font-size: 12px;
            }
            QTextEdit:focus {
                border-color: #3b82f6;
                background-color: white;
            }
        """)
        layout.addWidget(self.license_key_input)
        
        # 激活按钮
        self.activate_btn = QPushButton("🔑 激活授权")
        self.activate_btn.setFixedHeight(45)
        self.activate_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #10b981, stop:1 #059669);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #059669, stop:1 #047857);
            }
        """)
        self.activate_btn.clicked.connect(self._activate_license)
        layout.addWidget(self.activate_btn)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setMaximumHeight(100)
        self.result_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 10px;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.result_text)
        
        layout.addStretch()
        
        self.tab_widget.addTab(activation_widget, "🔑 激活授权")
    
    def _create_trial_tab(self):
        """创建试用授权选项卡"""
        trial_widget = QWidget()
        layout = QVBoxLayout(trial_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 说明文字
        info_label = QLabel("如果您还没有正式授权，可以申请30天试用授权:")
        info_label.setStyleSheet("""
            QLabel {
                color: #64748b;
                font-size: 14px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(info_label)
        
        # 试用信息
        trial_info = QLabel("""
        试用版功能限制:
        • 最多2个用户
        • 最多100个商品
        • 基础库存管理
        • 条码扫描功能
        • 30天使用期限
        """)
        trial_info.setStyleSheet("""
            QLabel {
                background-color: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 8px;
                padding: 15px;
                color: #92400e;
                font-size: 13px;
                line-height: 1.5;
            }
        """)
        layout.addWidget(trial_info)
        
        # 申请试用按钮
        self.trial_btn = QPushButton("🎯 申请30天试用")
        self.trial_btn.setFixedHeight(45)
        self.trial_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b, stop:1 #d97706);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d97706, stop:1 #b45309);
            }
        """)
        self.trial_btn.clicked.connect(self._apply_trial)
        layout.addWidget(self.trial_btn)
        
        layout.addStretch()
        
        self.tab_widget.addTab(trial_widget, "🎯 试用授权")
    
    def _load_license_status(self):
        """加载授权状态"""
        status = license_manager.get_license_status()
        
        if status['status'] == 'valid':
            info = status['info']
            self.status_label.setText("✅ 授权有效")
            self.status_label.setStyleSheet("color: #10b981; font-weight: 600;")
            
            self.type_label.setText(info.get('type_name', '未知'))
            
            expire_date = info.get('expire_date', '')
            if expire_date:
                try:
                    expire_dt = datetime.fromisoformat(expire_date.replace('Z', '+00:00'))
                    self.expire_label.setText(expire_dt.strftime('%Y-%m-%d %H:%M:%S'))
                except:
                    self.expire_label.setText(expire_date)
            
            days_remaining = info.get('days_remaining', 0)
            if days_remaining > 30:
                color = "#10b981"  # 绿色
            elif days_remaining > 7:
                color = "#f59e0b"  # 黄色
            else:
                color = "#ef4444"  # 红色
            
            self.remaining_label.setText(f"{days_remaining} 天")
            self.remaining_label.setStyleSheet(f"color: {color}; font-weight: 600;")
            
            # 显示功能权限
            features = info.get('features', {})
            features_text = ""
            for feature, enabled in features.items():
                status_icon = "✅" if enabled else "❌"
                feature_name = self._get_feature_name(feature)
                features_text += f"{status_icon} {feature_name}\n"
            
            self.features_text.setText(features_text)
            
        else:
            self.status_label.setText("❌ 授权无效")
            self.status_label.setStyleSheet("color: #ef4444; font-weight: 600;")
            
            self.type_label.setText("--")
            self.expire_label.setText("--")
            self.remaining_label.setText("--")
            
            error_msg = status.get('message', '未知错误')
            self.features_text.setText(f"授权错误: {error_msg}")
    
    def _get_feature_name(self, feature: str) -> str:
        """获取功能中文名称"""
        feature_names = {
            'max_users': '最大用户数',
            'max_products': '最大商品数',
            'advanced_reports': '高级报表',
            'api_access': 'API访问',
            'multi_warehouse': '多仓库管理',
            'face_recognition': '人脸识别',
            'barcode_scanning': '条码扫描',
            'basic_inventory': '基础库存管理'
        }
        return feature_names.get(feature, feature)
    
    def _activate_license(self):
        """激活授权"""
        license_key = self.license_key_input.toPlainText().strip()
        
        if not license_key:
            self.result_text.setText("❌ 请输入授权密钥")
            return
        
        # 验证并保存授权
        if license_manager.save_license(license_key):
            self.result_text.setText("✅ 授权激活成功！")
            self.result_text.setStyleSheet("""
                QTextEdit {
                    background-color: #f0fdf4;
                    border: 1px solid #10b981;
                    border-radius: 6px;
                    padding: 10px;
                    color: #065f46;
                    font-size: 12px;
                }
            """)
            
            # 刷新状态
            QTimer.singleShot(1000, self._load_license_status)
        else:
            self.result_text.setText("❌ 授权密钥无效或已过期")
            self.result_text.setStyleSheet("""
                QTextEdit {
                    background-color: #fef2f2;
                    border: 1px solid #ef4444;
                    border-radius: 6px;
                    padding: 10px;
                    color: #991b1b;
                    font-size: 12px;
                }
            """)
    
    def _apply_trial(self):
        """申请试用授权"""
        try:
            trial_license = license_manager.get_trial_license(30)
            
            if license_manager.save_license(trial_license):
                QMessageBox.information(
                    self,
                    "试用授权成功",
                    "30天试用授权已激活！\n\n请注意试用版功能限制，如需完整功能请联系购买正式授权。"
                )
                
                # 刷新状态
                self._load_license_status()
            else:
                QMessageBox.warning(
                    self,
                    "试用授权失败",
                    "无法激活试用授权，请联系技术支持。"
                )
                
        except Exception as e:
            QMessageBox.critical(
                self,
                "试用授权错误",
                f"申请试用授权时发生错误:\n{str(e)}"
            )
