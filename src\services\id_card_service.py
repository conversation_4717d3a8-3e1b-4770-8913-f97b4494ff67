"""
身份证识别服务
© 2024 贵州睿云慧通科技有限公司
"""

import cv2
import numpy as np
from datetime import datetime
from typing import Optional, Dict, Any, Tuple
import re
import base64
from io import BytesIO
from PIL import Image

from ..utils.logger import get_logger
from ..utils.exceptions import ValidationError, DeviceError

logger = get_logger(__name__)

class IDCardService:
    """身份证识别服务"""
    
    def __init__(self):
        self.ocr_engine = None
        self._init_ocr()
    
    def _init_ocr(self):
        """初始化OCR引擎"""
        try:
            # 使用PaddleOCR进行文字识别
            from paddleocr import PaddleOCR
            self.ocr_engine = PaddleOCR(use_angle_cls=True, lang='ch')
            logger.info("OCR引擎初始化成功")
        except ImportError:
            logger.warning("PaddleOCR未安装，将使用备用OCR方案")
            self.ocr_engine = None
        except Exception as e:
            logger.error(f"OCR引擎初始化失败: {e}")
            self.ocr_engine = None
    
    def recognize_id_card(self, image_data: bytes) -> Dict[str, Any]:
        """
        识别身份证信息
        
        Args:
            image_data: 图像数据
            
        Returns:
            识别结果字典
        """
        try:
            # 预处理图像
            processed_image = self._preprocess_image(image_data)
            
            # 检测身份证区域
            id_card_region = self._detect_id_card_region(processed_image)
            
            if id_card_region is None:
                return {
                    "success": False,
                    "message": "未检测到身份证",
                    "confidence": 0
                }
            
            # 提取文字信息
            ocr_result = self._extract_text(id_card_region)
            
            # 解析身份证信息
            id_info = self._parse_id_card_info(ocr_result)
            
            # 验证身份证号
            if id_info.get("id_number"):
                is_valid = self._validate_id_number(id_info["id_number"])
                id_info["id_number_valid"] = is_valid
            
            return {
                "success": True,
                "data": id_info,
                "confidence": id_info.get("confidence", 0),
                "message": "识别成功"
            }
            
        except Exception as e:
            logger.error(f"身份证识别失败: {e}")
            return {
                "success": False,
                "message": f"识别失败: {str(e)}",
                "confidence": 0
            }
    
    def _preprocess_image(self, image_data: bytes) -> np.ndarray:
        """
        预处理图像
        
        Args:
            image_data: 图像数据
            
        Returns:
            处理后的图像
        """
        # 将字节数据转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            raise ValidationError("无效的图像数据")
        
        # 调整图像大小
        height, width = image.shape[:2]
        if width > 1200:
            scale = 1200 / width
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height))
        
        # 图像增强
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 直方图均衡化
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # 高斯滤波去噪
        denoised = cv2.GaussianBlur(enhanced, (3, 3), 0)
        
        return denoised
    
    def _detect_id_card_region(self, image: np.ndarray) -> Optional[np.ndarray]:
        """
        检测身份证区域
        
        Args:
            image: 输入图像
            
        Returns:
            身份证区域图像或None
        """
        try:
            # 边缘检测
            edges = cv2.Canny(image, 50, 150, apertureSize=3)
            
            # 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 筛选可能的身份证轮廓
            for contour in contours:
                # 计算轮廓面积
                area = cv2.contourArea(contour)
                if area < 10000:  # 面积太小
                    continue
                
                # 计算轮廓的边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                
                # 检查长宽比（身份证约为1.6:1）
                aspect_ratio = w / h
                if 1.4 <= aspect_ratio <= 1.8:
                    # 提取身份证区域
                    id_card_region = image[y:y+h, x:x+w]
                    return id_card_region
            
            # 如果没有检测到合适的轮廓，返回原图像
            return image
            
        except Exception as e:
            logger.warning(f"身份证区域检测失败: {e}")
            return image
    
    def _extract_text(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        提取文字信息
        
        Args:
            image: 输入图像
            
        Returns:
            OCR结果列表
        """
        if self.ocr_engine is None:
            return self._fallback_ocr(image)
        
        try:
            # 使用PaddleOCR识别
            result = self.ocr_engine.ocr(image, cls=True)
            
            ocr_results = []
            if result and result[0]:
                for line in result[0]:
                    if len(line) >= 2:
                        bbox = line[0]
                        text_info = line[1]
                        text = text_info[0] if isinstance(text_info, tuple) else text_info
                        confidence = text_info[1] if isinstance(text_info, tuple) and len(text_info) > 1 else 0.9
                        
                        ocr_results.append({
                            "text": text,
                            "confidence": confidence,
                            "bbox": bbox
                        })
            
            return ocr_results
            
        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            return self._fallback_ocr(image)
    
    def _fallback_ocr(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        备用OCR方案
        
        Args:
            image: 输入图像
            
        Returns:
            OCR结果列表
        """
        try:
            import pytesseract
            
            # 配置Tesseract
            config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz年月日族汉回满蒙维藏壮苗彝布朝鲜侗瑶白土哈尼傣黎傈僳佤畲高山拉祜水东乡纳西景颇柯尔克孜达斡尔羌撒拉毛南仡佬锡伯阿昌普米塔吉克怒族乌孜别克俄罗斯鄂温克德昂保安裕固京塔塔尔独龙鄂伦春赫哲门巴珞巴基诺'
            
            text = pytesseract.image_to_string(image, lang='chi_sim', config=config)
            
            return [{
                "text": text.strip(),
                "confidence": 0.8,
                "bbox": [[0, 0], [image.shape[1], 0], [image.shape[1], image.shape[0]], [0, image.shape[0]]]
            }]
            
        except ImportError:
            logger.warning("Tesseract未安装")
            return []
        except Exception as e:
            logger.error(f"备用OCR失败: {e}")
            return []
    
    def _parse_id_card_info(self, ocr_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        解析身份证信息
        
        Args:
            ocr_results: OCR结果列表
            
        Returns:
            解析后的身份证信息
        """
        id_info = {
            "name": None,
            "gender": None,
            "nation": None,
            "birth_date": None,
            "address": None,
            "id_number": None,
            "issuing_authority": None,
            "valid_from": None,
            "valid_to": None,
            "confidence": 0
        }
        
        # 合并所有识别的文字
        all_text = " ".join([result["text"] for result in ocr_results])
        
        # 计算平均置信度
        if ocr_results:
            id_info["confidence"] = sum(result["confidence"] for result in ocr_results) / len(ocr_results)
        
        # 身份证号码识别（18位）
        id_pattern = r'[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]'
        id_match = re.search(id_pattern, all_text)
        if id_match:
            id_info["id_number"] = id_match.group().upper()
            
            # 从身份证号提取信息
            id_number = id_info["id_number"]
            if len(id_number) == 18:
                # 提取出生日期
                birth_str = id_number[6:14]
                try:
                    birth_date = datetime.strptime(birth_str, "%Y%m%d")
                    id_info["birth_date"] = birth_date.strftime("%Y-%m-%d")
                except:
                    pass
                
                # 提取性别（倒数第二位，奇数为男，偶数为女）
                gender_digit = int(id_number[16])
                id_info["gender"] = "男" if gender_digit % 2 == 1 else "女"
        
        # 姓名识别（通常在第一行）
        for result in ocr_results:
            text = result["text"].strip()
            # 姓名通常是2-4个汉字
            if re.match(r'^[\u4e00-\u9fa5]{2,4}$', text):
                if not id_info["name"]:  # 取第一个匹配的
                    id_info["name"] = text
        
        # 民族识别
        nation_pattern = r'(汉|回|满|蒙|维|藏|壮|苗|彝|布|朝鲜|侗|瑶|白|土|哈尼|傣|黎|傈僳|佤|畲|高山|拉祜|水|东乡|纳西|景颇|柯尔克孜|达斡尔|羌|撒拉|毛南|仡佬|锡伯|阿昌|普米|塔吉克|怒|乌孜别克|俄罗斯|鄂温克|德昂|保安|裕固|京|塔塔尔|独龙|鄂伦春|赫哲|门巴|珞巴|基诺)族?'
        nation_match = re.search(nation_pattern, all_text)
        if nation_match:
            id_info["nation"] = nation_match.group()
        
        # 地址识别（包含省市县等关键词的长文本）
        for result in ocr_results:
            text = result["text"].strip()
            if any(keyword in text for keyword in ["省", "市", "县", "区", "镇", "街道", "村", "号"]) and len(text) > 6:
                if not id_info["address"]:
                    id_info["address"] = text
        
        return id_info
    
    def _validate_id_number(self, id_number: str) -> bool:
        """
        验证身份证号码
        
        Args:
            id_number: 身份证号码
            
        Returns:
            是否有效
        """
        if not id_number or len(id_number) != 18:
            return False
        
        # 检查前17位是否为数字
        if not id_number[:17].isdigit():
            return False
        
        # 检查最后一位
        if id_number[17] not in '0123456789Xx':
            return False
        
        # 校验码验证
        weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        sum_value = sum(int(id_number[i]) * weights[i] for i in range(17))
        check_code = check_codes[sum_value % 11]
        
        return id_number[17].upper() == check_code
    
    def save_id_card_image(self, image_data: bytes, filename: str) -> str:
        """
        保存身份证图像
        
        Args:
            image_data: 图像数据
            filename: 文件名
            
        Returns:
            保存路径
        """
        import os
        from pathlib import Path
        
        # 创建保存目录
        save_dir = Path("data/id_cards")
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        file_path = save_dir / filename
        with open(file_path, 'wb') as f:
            f.write(image_data)
        
        return str(file_path)
