"""
WMS客户端系统 - UI主题配置
企业风格：正式、简约、大气
"""

from typing import Dict, Any
from dataclasses import dataclass


@dataclass
class ColorPalette:
    """颜色调色板"""
    # 主色调
    PRIMARY = "#1E3A8A"          # 深蓝色 - 专业、稳重
    SECONDARY = "#3B82F6"        # 蓝色 - 活力、科技感
    SUCCESS = "#10B981"          # 绿色 - 成功、确认
    WARNING = "#F59E0B"          # 橙色 - 警告、注意
    ERROR = "#EF4444"            # 红色 - 错误、危险
    
    # 中性色
    TEXT_PRIMARY = "#1F2937"     # 深灰 - 主要文字
    TEXT_SECONDARY = "#6B7280"   # 中灰 - 次要文字
    TEXT_MUTED = "#9CA3AF"       # 浅灰 - 辅助文字
    BORDER = "#E5E7EB"           # 浅灰 - 边框
    BACKGROUND = "#F9FAFB"       # 浅灰 - 背景
    WHITE = "#FFFFFF"            # 纯白
    
    # 功能色彩
    INBOUND = "#10B981"          # 入库操作 - 绿色
    OUTBOUND = "#3B82F6"         # 出库操作 - 蓝色
    QUERY = "#6B7280"            # 查询功能 - 中性色
    ADMIN = "#1E3A8A"            # 系统管理 - 深蓝色


@dataclass
class Typography:
    """字体系统"""
    # 字体族
    FONT_FAMILY = "Microsoft YaHei UI, Segoe UI, sans-serif"
    FONT_FAMILY_MONO = "Consolas, Monaco, monospace"
    
    # 字体大小
    H1_SIZE = 28
    H2_SIZE = 24
    H3_SIZE = 20
    H4_SIZE = 18
    BODY_LARGE_SIZE = 16
    BODY_SIZE = 14
    BODY_SMALL_SIZE = 12
    CAPTION_SIZE = 11
    
    # 字体权重
    WEIGHT_REGULAR = 400
    WEIGHT_MEDIUM = 500
    WEIGHT_BOLD = 700


@dataclass
class Spacing:
    """间距系统"""
    # 基础单位
    BASE_UNIT = 8
    
    # 组件间距
    XS = 4   # 0.5 * BASE_UNIT
    SM = 8   # 1 * BASE_UNIT
    MD = 16  # 2 * BASE_UNIT
    LG = 24  # 3 * BASE_UNIT
    XL = 32  # 4 * BASE_UNIT
    XXL = 48 # 6 * BASE_UNIT
    
    # 页面布局
    PAGE_MARGIN = 32
    CARD_PADDING = 24
    SIDEBAR_WIDTH = 240
    HEADER_HEIGHT = 64


@dataclass
class BorderRadius:
    """圆角系统"""
    SM = 4
    MD = 6
    LG = 8
    XL = 12
    FULL = 9999


@dataclass
class Shadow:
    """阴影系统"""
    SM = "0 1px 2px rgba(0, 0, 0, 0.05)"
    MD = "0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)"
    LG = "0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06)"
    XL = "0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)"


class Theme:
    """主题配置类"""
    
    def __init__(self):
        self.colors = ColorPalette()
        self.typography = Typography()
        self.spacing = Spacing()
        self.border_radius = BorderRadius()
        self.shadow = Shadow()
    
    def get_button_style(self, variant: str = "primary") -> Dict[str, Any]:
        """获取按钮样式"""
        base_style = {
            "border-radius": f"{self.border_radius.MD}px",
            "padding": f"{self.spacing.MD}px {self.spacing.LG}px",
            "font-size": f"{self.typography.BODY_SIZE}px",
            "font-weight": self.typography.WEIGHT_MEDIUM,
            "border": "none",
        }
        
        if variant == "primary":
            base_style.update({
                "background-color": self.colors.PRIMARY,
                "color": self.colors.WHITE,
            })
        elif variant == "secondary":
            base_style.update({
                "background-color": self.colors.WHITE,
                "color": self.colors.PRIMARY,
                "border": f"1px solid {self.colors.PRIMARY}",
            })
        elif variant == "success":
            base_style.update({
                "background-color": self.colors.SUCCESS,
                "color": self.colors.WHITE,
            })
        elif variant == "warning":
            base_style.update({
                "background-color": self.colors.WARNING,
                "color": self.colors.WHITE,
            })
        elif variant == "error":
            base_style.update({
                "background-color": self.colors.ERROR,
                "color": self.colors.WHITE,
            })
        
        return base_style
    
    def get_input_style(self, state: str = "normal") -> Dict[str, Any]:
        """获取输入框样式"""
        base_style = {
            "border-radius": f"{self.border_radius.MD}px",
            "padding": f"{self.spacing.MD}px",
            "font-size": f"{self.typography.BODY_SIZE}px",
            "background-color": self.colors.WHITE,
        }
        
        if state == "normal":
            base_style.update({
                "border": f"1px solid {self.colors.BORDER}",
            })
        elif state == "focus":
            base_style.update({
                "border": f"2px solid {self.colors.SECONDARY}",
            })
        elif state == "error":
            base_style.update({
                "border": f"2px solid {self.colors.ERROR}",
            })
        
        return base_style
    
    def get_card_style(self) -> Dict[str, Any]:
        """获取卡片样式"""
        return {
            "background-color": self.colors.WHITE,
            "border": f"1px solid {self.colors.BORDER}",
            "border-radius": f"{self.border_radius.LG}px",
            "padding": f"{self.spacing.LG}px",
        }
    
    def get_text_style(self, variant: str = "body") -> Dict[str, Any]:
        """获取文字样式"""
        base_style = {
            "font-family": self.typography.FONT_FAMILY,
            "color": self.colors.TEXT_PRIMARY,
        }
        
        if variant == "h1":
            base_style.update({
                "font-size": f"{self.typography.H1_SIZE}px",
                "font-weight": self.typography.WEIGHT_BOLD,
            })
        elif variant == "h2":
            base_style.update({
                "font-size": f"{self.typography.H2_SIZE}px",
                "font-weight": self.typography.WEIGHT_BOLD,
            })
        elif variant == "h3":
            base_style.update({
                "font-size": f"{self.typography.H3_SIZE}px",
                "font-weight": self.typography.WEIGHT_BOLD,
            })
        elif variant == "h4":
            base_style.update({
                "font-size": f"{self.typography.H4_SIZE}px",
                "font-weight": self.typography.WEIGHT_BOLD,
            })
        elif variant == "body-large":
            base_style.update({
                "font-size": f"{self.typography.BODY_LARGE_SIZE}px",
                "font-weight": self.typography.WEIGHT_REGULAR,
            })
        elif variant == "body":
            base_style.update({
                "font-size": f"{self.typography.BODY_SIZE}px",
                "font-weight": self.typography.WEIGHT_REGULAR,
            })
        elif variant == "body-small":
            base_style.update({
                "font-size": f"{self.typography.BODY_SMALL_SIZE}px",
                "color": self.colors.TEXT_SECONDARY,
            })
        elif variant == "caption":
            base_style.update({
                "font-size": f"{self.typography.CAPTION_SIZE}px",
                "font-weight": self.typography.WEIGHT_REGULAR,
                "color": self.colors.TEXT_MUTED,
            })
        
        return base_style





    def generate_button_stylesheet(self, variant: str = "primary") -> str:
        """生成按钮样式表"""
        style = self.get_button_style(variant)

        return f"""
        QPushButton {{
            background-color: {style['background-color']};
            color: {style['color']};
            border: {style.get('border', 'none')};
            border-radius: {style['border-radius']};
            padding: {style['padding']};
            font-size: {style['font-size']};
            font-weight: {style['font-weight']};
            min-height: 20px;
        }}

        QPushButton:hover {{
            background-color: {_darken_color(style['background-color'], 0.1)};
        }}

        QPushButton:pressed {{
            background-color: {_darken_color(style['background-color'], 0.2)};
        }}

        QPushButton:disabled {{
            background-color: {self.colors.BORDER};
            color: {self.colors.TEXT_MUTED};
        }}
        """
    
    def generate_input_stylesheet(self) -> str:
        """生成输入框样式表"""
        style = self.get_input_style()

        return f"""
        QLineEdit, QTextEdit, QComboBox {{
            background-color: {style['background-color']};
            border: {style['border']};
            border-radius: {style['border-radius']};
            padding: {style['padding']};
            font-size: {style['font-size']};
            color: {self.colors.TEXT_PRIMARY};
        }}

        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
            border: 1px solid {self.colors.SECONDARY};
        }}
        """


def _darken_color(color: str, factor: float) -> str:
    """颜色加深函数"""
    # 简化实现，实际应用中可以使用更复杂的颜色处理
    if color.startswith('#'):
        # 移除#号
        color = color[1:]
        # 转换为RGB
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        # 加深颜色
        r = max(0, int(r * (1 - factor)))
        g = max(0, int(g * (1 - factor)))
        b = max(0, int(b * (1 - factor)))
        # 转换回十六进制
        return f"#{r:02x}{g:02x}{b:02x}"
    return color


# 创建全局主题实例
theme = Theme()
