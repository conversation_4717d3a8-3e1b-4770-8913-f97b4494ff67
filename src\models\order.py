"""
订单相关数据模型
© 2024 贵州睿云慧通科技有限公司
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Numeric, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
import enum

class OrderStatus(enum.Enum):
    """订单状态枚举"""
    DRAFT = "draft"           # 草稿
    PENDING = "pending"       # 待处理
    IN_PROGRESS = "in_progress"  # 处理中
    COMPLETED = "completed"   # 已完成
    CANCELLED = "cancelled"   # 已取消
    CLOSED = "closed"         # 已关闭

class InboundOrder(Base):
    """入库单表"""
    __tablename__ = "inbound_orders"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    order_number = Column(String(50), unique=True, nullable=False, index=True)
    title = Column(String(200))
    type = Column(String(20), default="purchase")  # purchase, return, transfer, adjustment
    status = Column(String(20), default=OrderStatus.DRAFT.value)
    priority = Column(String(20), default="normal")  # low, normal, high, urgent
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    supplier_id = Column(Integer, ForeignKey("suppliers.id"))
    warehouse_id = Column(Integer, ForeignKey("warehouses.id"))
    reference_number = Column(String(100))  # 参考单号（如采购单号）
    expected_date = Column(DateTime)        # 预期到货日期
    actual_date = Column(DateTime)          # 实际到货日期
    total_items = Column(Integer, default=0)
    total_quantity = Column(Integer, default=0)
    total_amount = Column(Numeric(12, 2), default=0)
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    completed_at = Column(DateTime)
    
    # 关联关系
    user = relationship("User", back_populates="inbound_orders")
    supplier = relationship("Supplier")
    warehouse = relationship("Warehouse")
    items = relationship("InboundOrderItem", back_populates="inbound_order", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_inbound_order_user', 'user_id'),
        Index('idx_inbound_order_supplier', 'supplier_id'),
        Index('idx_inbound_order_status', 'status'),
        Index('idx_inbound_order_date', 'created_at'),
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "order_number": self.order_number,
            "title": self.title,
            "type": self.type,
            "status": self.status,
            "priority": self.priority,
            "user_id": self.user_id,
            "supplier_id": self.supplier_id,
            "warehouse_id": self.warehouse_id,
            "reference_number": self.reference_number,
            "expected_date": self.expected_date.isoformat() if self.expected_date else None,
            "actual_date": self.actual_date.isoformat() if self.actual_date else None,
            "total_items": self.total_items,
            "total_quantity": self.total_quantity,
            "total_amount": float(self.total_amount) if self.total_amount else None,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None
        }

class InboundOrderItem(Base):
    """入库单明细表"""
    __tablename__ = "inbound_order_items"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    inbound_order_id = Column(Integer, ForeignKey("inbound_orders.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    location_id = Column(Integer, ForeignKey("locations.id"))
    batch_id = Column(Integer, ForeignKey("product_batches.id"))
    planned_quantity = Column(Integer, nullable=False)  # 计划数量
    received_quantity = Column(Integer, default=0)      # 已收货数量
    remaining_quantity = Column(Integer, default=0)     # 剩余数量
    unit_price = Column(Numeric(10, 2))                # 单价
    total_price = Column(Numeric(12, 2))               # 总价
    quality_status = Column(String(20), default="qualified")  # qualified, unqualified, pending
    serial_numbers = Column(Text)  # JSON格式存储序列号列表
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    inbound_order = relationship("InboundOrder", back_populates="items")
    product = relationship("Product", back_populates="inbound_items")
    location = relationship("Location")
    batch = relationship("ProductBatch")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "inbound_order_id": self.inbound_order_id,
            "product_id": self.product_id,
            "location_id": self.location_id,
            "batch_id": self.batch_id,
            "planned_quantity": self.planned_quantity,
            "received_quantity": self.received_quantity,
            "remaining_quantity": self.remaining_quantity,
            "unit_price": float(self.unit_price) if self.unit_price else None,
            "total_price": float(self.total_price) if self.total_price else None,
            "quality_status": self.quality_status,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class OutboundOrder(Base):
    """出库单表"""
    __tablename__ = "outbound_orders"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    order_number = Column(String(50), unique=True, nullable=False, index=True)
    title = Column(String(200))
    type = Column(String(20), default="sale")  # sale, transfer, return, adjustment
    status = Column(String(20), default=OrderStatus.DRAFT.value)
    priority = Column(String(20), default="normal")  # low, normal, high, urgent
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    customer_id = Column(Integer, ForeignKey("customers.id"))
    warehouse_id = Column(Integer, ForeignKey("warehouses.id"))
    reference_number = Column(String(100))  # 参考单号（如销售单号）
    required_date = Column(DateTime)        # 要求出库日期
    actual_date = Column(DateTime)          # 实际出库日期
    total_items = Column(Integer, default=0)
    total_quantity = Column(Integer, default=0)
    total_amount = Column(Numeric(12, 2), default=0)
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    completed_at = Column(DateTime)
    
    # 关联关系
    user = relationship("User", back_populates="outbound_orders")
    customer = relationship("Customer")
    warehouse = relationship("Warehouse")
    items = relationship("OutboundOrderItem", back_populates="outbound_order", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_outbound_order_user', 'user_id'),
        Index('idx_outbound_order_customer', 'customer_id'),
        Index('idx_outbound_order_status', 'status'),
        Index('idx_outbound_order_date', 'created_at'),
    )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "order_number": self.order_number,
            "title": self.title,
            "type": self.type,
            "status": self.status,
            "priority": self.priority,
            "user_id": self.user_id,
            "customer_id": self.customer_id,
            "warehouse_id": self.warehouse_id,
            "reference_number": self.reference_number,
            "required_date": self.required_date.isoformat() if self.required_date else None,
            "actual_date": self.actual_date.isoformat() if self.actual_date else None,
            "total_items": self.total_items,
            "total_quantity": self.total_quantity,
            "total_amount": float(self.total_amount) if self.total_amount else None,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None
        }

class OutboundOrderItem(Base):
    """出库单明细表"""
    __tablename__ = "outbound_order_items"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    outbound_order_id = Column(Integer, ForeignKey("outbound_orders.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    location_id = Column(Integer, ForeignKey("locations.id"))
    batch_id = Column(Integer, ForeignKey("product_batches.id"))
    planned_quantity = Column(Integer, nullable=False)  # 计划数量
    picked_quantity = Column(Integer, default=0)        # 已拣货数量
    shipped_quantity = Column(Integer, default=0)       # 已出库数量
    remaining_quantity = Column(Integer, default=0)     # 剩余数量
    unit_price = Column(Numeric(10, 2))                # 单价
    total_price = Column(Numeric(12, 2))               # 总价
    serial_numbers = Column(Text)  # JSON格式存储序列号列表
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    outbound_order = relationship("OutboundOrder", back_populates="items")
    product = relationship("Product", back_populates="outbound_items")
    location = relationship("Location")
    batch = relationship("ProductBatch")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "outbound_order_id": self.outbound_order_id,
            "product_id": self.product_id,
            "location_id": self.location_id,
            "batch_id": self.batch_id,
            "planned_quantity": self.planned_quantity,
            "picked_quantity": self.picked_quantity,
            "shipped_quantity": self.shipped_quantity,
            "remaining_quantity": self.remaining_quantity,
            "unit_price": float(self.unit_price) if self.unit_price else None,
            "total_price": float(self.total_price) if self.total_price else None,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class Customer(Base):
    """客户表"""
    __tablename__ = "customers"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(200), nullable=False)
    contact_person = Column(String(100))
    phone = Column(String(20))
    email = Column(String(100))
    address = Column(Text)
    website = Column(String(200))
    tax_number = Column(String(50))  # 税号
    credit_limit = Column(Numeric(12, 2))  # 信用额度
    payment_terms = Column(String(100))    # 付款条件
    credit_rating = Column(String(20))     # 信用等级
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "code": self.code,
            "name": self.name,
            "contact_person": self.contact_person,
            "phone": self.phone,
            "email": self.email,
            "address": self.address,
            "website": self.website,
            "credit_limit": float(self.credit_limit) if self.credit_limit else None,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
