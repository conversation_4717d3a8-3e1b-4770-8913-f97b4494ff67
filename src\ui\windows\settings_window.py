"""
WMS客户端系统 - 系统设置窗口
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QLineEdit, QComboBox, QCheckBox,
    QSpinBox, QTabWidget, QGroupBox, QApplication, QMessageBox,
    QFileDialog, QTextEdit, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ..components.base_components import (
    BaseCard, PrimaryButton, SecondaryButton,
    TitleLabel, BodyLabel, UnifiedToolbar
)
from ..styles.theme import theme


class SettingsWindow(QMainWindow):
    """系统设置窗口"""
    
    # 信号定义
    back_to_dashboard = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        self._load_current_settings()
    
    def _setup_ui(self):
        """设置UI - 现代化企业风格"""
        self.setWindowTitle("系统设置")

        # 获取屏幕信息
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()

        # 设置全屏
        self.setGeometry(screen_geometry)
        self.showFullScreen()
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)

        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 与其他模块保持一致的蓝色渐变背景
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:0.5 #2563eb, stop:1 #3b82f6);
            }
        """)

        # 主布局
        layout = QVBoxLayout(central_widget)
        margin = max(20, min(35, int(screen_geometry.width() * 0.025)))
        spacing = max(15, min(25, int(screen_geometry.height() * 0.02)))
        layout.setContentsMargins(margin, margin, margin, margin)
        layout.setSpacing(spacing)

        # 工具栏 - 与智能查询样式完全一致
        toolbar_widget = QWidget()
        toolbar_widget.setFixedHeight(80)
        toolbar_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:1 rgba(248, 250, 252, 0.95));
                border-radius: 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)

        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(30, 20, 30, 20)
        toolbar_layout.setSpacing(20)

        # 返回按钮
        back_btn = QPushButton("← 返回主界面")
        back_btn.setFixedSize(140, 40)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 600;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        back_btn.clicked.connect(self.back_to_dashboard.emit)
        toolbar_layout.addWidget(back_btn)

        # 标题
        title_label = QLabel("⚙️ 系统设置")
        title_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 24px;
                font-weight: 700;
                background: transparent;
                border: none;
            }
        """)
        toolbar_layout.addWidget(title_label)

        toolbar_layout.addStretch()

        # 保存设置按钮
        save_btn = QPushButton("💾 保存设置")
        save_btn.setFixedSize(120, 40)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 600;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        save_btn.clicked.connect(self._save_settings)
        toolbar_layout.addWidget(save_btn)

        layout.addWidget(toolbar_widget)

        # 内容区域 - 与快速查询样式完全一致
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:1 rgba(248, 250, 252, 0.95));
                border-radius: 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)

        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(30, 30, 30, 30)
        content_layout.setSpacing(20)

        # 设置选项卡 - 与快速查询样式完全一致
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8fafc;
                color: #374151;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: 600;
            }
            QTabBar::tab:selected {
                background-color: #3b82f6;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #e5e7eb;
            }
        """)

        # 添加设置选项卡
        self._create_modern_system_settings_tab()
        self._create_modern_database_settings_tab()
        self._create_modern_security_settings_tab()
        self._create_modern_backup_settings_tab()

        content_layout.addWidget(self.tab_widget)
        layout.addWidget(content_widget)
    


    def _get_group_style(self):
        """获取分组样式 - 与快速查询风格完全一致"""
        return f"""
            QGroupBox {{
                font-size: {theme.typography.BODY_SIZE}px;
                font-weight: {theme.typography.WEIGHT_MEDIUM};
                color: {theme.colors.TEXT_PRIMARY};
                border: 2px solid {theme.colors.BORDER};
                border-radius: {theme.border_radius.LG}px;
                margin-top: {theme.spacing.SM}px;
                padding-top: {theme.spacing.SM}px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {theme.spacing.SM}px;
                padding: 0 {theme.spacing.SM}px 0 {theme.spacing.SM}px;
            }}
        """

    def _style_input(self, widget):
        """设置输入控件样式 - 与快速查询风格完全一致"""
        widget.setStyleSheet(f"""
            QLineEdit, QComboBox, QDateEdit, QSpinBox {{
                background-color: {theme.colors.WHITE};
                border: 2px solid {theme.colors.BORDER};
                border-radius: {theme.border_radius.LG}px;
                padding: {theme.spacing.SM}px {theme.spacing.MD}px;
                font-size: {theme.typography.BODY_SIZE}px;
                color: {theme.colors.TEXT_PRIMARY};
                min-height: 20px;
            }}
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus {{
                border-color: {theme.colors.SECONDARY};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 25px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid {theme.colors.TEXT_SECONDARY};
                margin-right: {theme.spacing.SM}px;
            }}
            QSpinBox::up-button, QSpinBox::down-button {{
                background: {theme.colors.BACKGROUND};
                border: 1px solid {theme.colors.BORDER};
                width: 18px;
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background: {theme.colors.BORDER};
            }}
        """)
    
    def _create_modern_system_settings_tab(self):
        """创建系统设置选项卡 - 与快速查询样式完全一致"""
        system_widget = QWidget()
        layout = QVBoxLayout(system_widget)
        layout.setSpacing(20)

        # 基本设置 - 与快速查询样式完全一致
        basic_group = QGroupBox("基本设置")
        basic_group.setStyleSheet(self._get_group_style())
        basic_layout = QGridLayout(basic_group)
        basic_layout.setSpacing(15)

        # 系统名称
        basic_layout.addWidget(QLabel("系统名称:"), 0, 0)
        self.system_name = QLineEdit("WMS库房管理系统")
        self.system_name.setFixedHeight(35)
        self._style_input(self.system_name)
        basic_layout.addWidget(self.system_name, 0, 1)

        # 公司名称
        basic_layout.addWidget(QLabel("公司名称:"), 1, 0)
        self.company_name = QLineEdit("贵州睿云慧通科技有限公司")
        self.company_name.setFixedHeight(35)
        self._style_input(self.company_name)
        basic_layout.addWidget(self.company_name, 1, 1)

        # 系统版本
        basic_layout.addWidget(QLabel("系统版本:"), 2, 0)
        self.system_version = QLineEdit("v1.0.0")
        self.system_version.setFixedHeight(35)
        self._style_input(self.system_version)
        basic_layout.addWidget(self.system_version, 2, 1)

        layout.addWidget(basic_group)

        # 界面设置 - 与快速查询样式完全一致
        ui_group = QGroupBox("界面设置")
        ui_group.setStyleSheet(self._get_group_style())
        ui_layout = QGridLayout(ui_group)
        ui_layout.setSpacing(15)

        # 主题选择
        ui_layout.addWidget(QLabel("界面主题:"), 0, 0)
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["浅色主题", "深色主题", "自动切换"])
        self.theme_combo.setFixedHeight(35)
        self._style_input(self.theme_combo)
        ui_layout.addWidget(self.theme_combo, 0, 1)

        # 字体大小
        ui_layout.addWidget(QLabel("字体大小:"), 1, 0)
        self.font_size = QSpinBox()
        self.font_size.setRange(12, 28)
        self.font_size.setValue(14)
        self.font_size.setFixedHeight(35)
        self._style_input(self.font_size)
        ui_layout.addWidget(self.font_size, 1, 1)

        layout.addWidget(ui_group)

        self.tab_widget.addTab(system_widget, "⚙️ 系统设置")

        # 基本设置卡片
        basic_card = self._create_modern_settings_card(
            "🏢 基本设置",
            "System Basic Configuration",
            self._create_basic_settings_content()
        )
        layout.addWidget(basic_card)

        # 界面设置卡片


    def _create_modern_settings_card(self, title, subtitle, content_widget):
        """创建现代化设置卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:1 rgba(248, 250, 252, 0.95));
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 16px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # 卡片标题
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(5)

        main_title = TitleLabel(title, level=3)
        main_title.setStyleSheet("""
            color: #1e293b;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 5px;
        """)

        sub_title = BodyLabel(subtitle)
        sub_title.setStyleSheet("""
            color: #6b7280;
            font-size: 12px;
            font-style: italic;
        """)

        title_layout.addWidget(main_title)
        title_layout.addWidget(sub_title)
        layout.addWidget(title_container)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("""
            QFrame {
                background: #e2e8f0;
                border: none;
                height: 1px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(separator)

        # 内容
        layout.addWidget(content_widget)

        return card

    def _create_basic_settings_content(self):
        """创建基本设置内容"""
        content = QWidget()
        layout = QGridLayout(content)
        layout.setSpacing(20)
        layout.setColumnStretch(1, 1)

        # 与其他模块保持一致的标签样式
        label_style = """
            QLabel {
                color: #1e293b;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }
        """

        # 与其他模块保持一致的输入框样式
        input_style = """
            QLineEdit {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #1e293b;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
            }
            QLineEdit:read-only {
                background-color: #f8fafc;
                color: #6b7280;
            }
        """

        # 系统名称
        system_name_label = QLabel("系统名称:")
        system_name_label.setStyleSheet(label_style)
        layout.addWidget(system_name_label, 0, 0)

        self.system_name = QLineEdit("WMS库房自助出入库客户端系统")
        self.system_name.setStyleSheet(input_style)
        layout.addWidget(self.system_name, 0, 1)

        # 公司名称
        company_name_label = QLabel("公司名称:")
        company_name_label.setStyleSheet(label_style)
        layout.addWidget(company_name_label, 1, 0)

        self.company_name = QLineEdit("贵州睿云慧通科技有限公司")
        self.company_name.setStyleSheet(input_style)
        layout.addWidget(self.company_name, 1, 1)

        # 系统版本
        version_label = QLabel("系统版本:")
        version_label.setStyleSheet(label_style)
        layout.addWidget(version_label, 2, 0)

        self.system_version = QLineEdit("v1.0.0")
        self.system_version.setStyleSheet(input_style)
        layout.addWidget(self.system_version, 2, 1)

        # 自动登录选项
        self.auto_login = QCheckBox("启用自动登录")
        self.auto_login.setStyleSheet("""
            QCheckBox {
                color: #1e293b;
                font-size: 14px;
                font-weight: 500;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #e2e8f0;
                border-radius: 4px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #3b82f6;
                border: 2px solid #3b82f6;
            }
            QCheckBox::indicator:hover {
                border-color: #3b82f6;
            }
        """)
        layout.addWidget(self.auto_login, 3, 0, 1, 2)

        return content

    def _create_ui_settings_content(self):
        """创建界面设置内容"""
        content = QWidget()
        layout = QGridLayout(content)
        layout.setSpacing(20)
        layout.setColumnStretch(1, 1)

        # 与其他模块保持一致的标签样式
        label_style = """
            QLabel {
                color: #1e293b;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }
        """

        # 与其他模块保持一致的下拉框样式
        combo_style = """
            QComboBox {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #1e293b;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #3b82f6;
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #6b7280;
                margin-right: 8px;
            }
            QComboBox QAbstractItemView {
                background: white;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                color: #1e293b;
                selection-background-color: #3b82f6;
                selection-color: white;
            }
        """

        # 主题选择
        theme_label = QLabel("界面主题:")
        theme_label.setStyleSheet(label_style)
        layout.addWidget(theme_label, 0, 0)

        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["深蓝主题", "浅色主题", "深色主题", "企业蓝主题"])
        self.theme_combo.setStyleSheet(combo_style)
        layout.addWidget(self.theme_combo, 0, 1)

        # 字体大小
        font_label = QLabel("字体大小:")
        font_label.setStyleSheet(label_style)
        layout.addWidget(font_label, 1, 0)

        self.font_size = QSpinBox()
        self.font_size.setRange(12, 28)
        self.font_size.setValue(16)
        self.font_size.setStyleSheet("""
            QSpinBox {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #1e293b;
                min-height: 20px;
            }
            QSpinBox:focus {
                border-color: #3b82f6;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                width: 18px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background: #e2e8f0;
            }
        """)
        layout.addWidget(self.font_size, 1, 1)

        # 全屏模式
        self.fullscreen_mode = QCheckBox("启用全屏模式")
        self.fullscreen_mode.setChecked(True)
        self.fullscreen_mode.setStyleSheet("""
            QCheckBox {
                color: #1e293b;
                font-size: 14px;
                font-weight: 500;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #e2e8f0;
                border-radius: 4px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #10b981;
                border: 2px solid #10b981;
            }
            QCheckBox::indicator:hover {
                border-color: #10b981;
            }
        """)
        layout.addWidget(self.fullscreen_mode, 2, 0, 1, 2)

        return content

    def _create_modern_database_settings_tab(self):
        """创建数据库设置选项卡 - 与快速查询样式完全一致"""
        database_widget = QWidget()
        layout = QVBoxLayout(database_widget)
        layout.setSpacing(20)

        # 数据库连接设置 - 与快速查询样式完全一致
        db_group = QGroupBox("数据库连接")
        db_group.setStyleSheet(self._get_group_style())
        db_layout = QGridLayout(db_group)
        db_layout.setSpacing(15)

        # 数据库类型
        db_layout.addWidget(QLabel("数据库类型:"), 0, 0)
        self.db_type = QComboBox()
        self.db_type.addItems(["SQLite", "MySQL", "PostgreSQL"])
        self.db_type.setFixedHeight(35)
        self._style_input(self.db_type)
        db_layout.addWidget(self.db_type, 0, 1)

        # 服务器地址
        db_layout.addWidget(QLabel("服务器地址:"), 1, 0)
        self.db_host = QLineEdit("localhost")
        self.db_host.setFixedHeight(35)
        self._style_input(self.db_host)
        db_layout.addWidget(self.db_host, 1, 1)

        # 端口号
        db_layout.addWidget(QLabel("端口号:"), 2, 0)
        self.db_port = QSpinBox()
        self.db_port.setRange(1, 65535)
        self.db_port.setValue(3306)
        self.db_port.setFixedHeight(35)
        self._style_input(self.db_port)
        db_layout.addWidget(self.db_port, 2, 1)

        layout.addWidget(db_group)

        # 连接测试 - 与快速查询样式完全一致
        test_group = QGroupBox("连接测试")
        test_group.setStyleSheet(self._get_group_style())
        test_layout = QHBoxLayout(test_group)
        test_layout.setSpacing(15)

        # 测试按钮
        test_btn = QPushButton("🔧 测试连接")
        test_btn.setFixedSize(120, 35)
        test_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: #2563eb;
            }}
        """)
        test_btn.clicked.connect(self._test_database_connection)
        test_layout.addWidget(test_btn)

        test_layout.addStretch()

        layout.addWidget(test_group)

        self.tab_widget.addTab(database_widget, "🗄️ 数据库设置")

    def _create_database_content(self):
        """创建数据库连接设置内容"""
        content = QWidget()
        layout = QGridLayout(content)
        layout.setSpacing(20)
        layout.setColumnStretch(1, 1)

        # 与其他模块保持一致的标签和输入框样式
        label_style = """
            QLabel {
                color: #1e293b;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }
        """

        input_style = """
            QLineEdit, QSpinBox, QComboBox {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #1e293b;
                min-height: 20px;
            }
            QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
                border-color: #3b82f6;
            }
            QLineEdit:read-only {
                background-color: #f8fafc;
                color: #6b7280;
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #6b7280;
                margin-right: 8px;
            }
            QComboBox QAbstractItemView {
                background: white;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                color: #1e293b;
                selection-background-color: #3b82f6;
                selection-color: white;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                width: 18px;
            }
        """

        # 数据库类型
        db_type_label = QLabel("数据库类型:")
        db_type_label.setStyleSheet(label_style)
        layout.addWidget(db_type_label, 0, 0)

        self.db_type = QComboBox()
        self.db_type.addItems(["SQLite", "MySQL", "PostgreSQL", "SQL Server"])
        self.db_type.setStyleSheet(input_style)
        layout.addWidget(self.db_type, 0, 1)

        # 服务器地址
        host_label = QLabel("服务器地址:")
        host_label.setStyleSheet(label_style)
        layout.addWidget(host_label, 1, 0)

        self.db_host = QLineEdit("localhost")
        self.db_host.setStyleSheet(input_style)
        self.db_host.setPlaceholderText("输入数据库服务器地址")
        layout.addWidget(self.db_host, 1, 1)

        # 端口号
        port_label = QLabel("端口号:")
        port_label.setStyleSheet(label_style)
        layout.addWidget(port_label, 2, 0)

        self.db_port = QSpinBox()
        self.db_port.setRange(1, 65535)
        self.db_port.setValue(3306)
        self.db_port.setStyleSheet(input_style)
        layout.addWidget(self.db_port, 2, 1)

        # 数据库名
        db_name_label = QLabel("数据库名:")
        db_name_label.setStyleSheet(label_style)
        layout.addWidget(db_name_label, 3, 0)

        self.db_name = QLineEdit("wms_database")
        self.db_name.setStyleSheet(input_style)
        self.db_name.setPlaceholderText("输入数据库名称")
        layout.addWidget(self.db_name, 3, 1)

        # 用户名
        user_label = QLabel("用户名:")
        user_label.setStyleSheet(label_style)
        layout.addWidget(user_label, 4, 0)

        self.db_user = QLineEdit("root")
        self.db_user.setStyleSheet(input_style)
        self.db_user.setPlaceholderText("输入数据库用户名")
        layout.addWidget(self.db_user, 4, 1)

        # 密码
        password_label = QLabel("密码:")
        password_label.setStyleSheet(label_style)
        layout.addWidget(password_label, 5, 0)

        self.db_password = QLineEdit()
        self.db_password.setEchoMode(QLineEdit.EchoMode.Password)
        self.db_password.setStyleSheet(input_style)
        self.db_password.setPlaceholderText("输入数据库密码")
        layout.addWidget(self.db_password, 5, 1)

        return content

    def _create_database_test_content(self):
        """创建数据库测试内容"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)

        # 测试连接按钮
        test_btn = QPushButton("🔍 测试数据库连接")
        test_btn.setFixedHeight(50)
        test_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #8b5cf6, stop:1 #8b5cf6dd);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #8b5cf6dd, stop:1 #8b5cf6bb);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7c3aed, stop:1 #6d28d9);
            }
        """)
        test_btn.clicked.connect(self._test_database_connection)
        layout.addWidget(test_btn)

        # 连接状态显示
        self.connection_status = QLabel("🔘 未测试连接")
        self.connection_status.setStyleSheet("""
            QLabel {
                color: #1e293b;
                font-size: 14px;
                font-weight: 500;
                padding: 12px;
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                margin-top: 8px;
            }
        """)
        layout.addWidget(self.connection_status)

        return content

    def _create_modern_security_settings_tab(self):
        """创建安全设置选项卡 - 与快速查询样式完全一致"""
        security_widget = QWidget()
        layout = QVBoxLayout(security_widget)
        layout.setSpacing(20)

        # 用户权限设置 - 与快速查询样式完全一致
        auth_group = QGroupBox("用户权限")
        auth_group.setStyleSheet(self._get_group_style())
        auth_layout = QGridLayout(auth_group)
        auth_layout.setSpacing(15)

        # 登录超时
        auth_layout.addWidget(QLabel("登录超时(分钟):"), 0, 0)
        self.login_timeout = QSpinBox()
        self.login_timeout.setRange(5, 480)
        self.login_timeout.setValue(30)
        self.login_timeout.setFixedHeight(35)
        self._style_input(self.login_timeout)
        auth_layout.addWidget(self.login_timeout, 0, 1)

        # 密码复杂度
        auth_layout.addWidget(QLabel("密码复杂度:"), 1, 0)
        self.password_complexity = QComboBox()
        self.password_complexity.addItems(["简单", "中等", "复杂"])
        self.password_complexity.setFixedHeight(35)
        self._style_input(self.password_complexity)
        auth_layout.addWidget(self.password_complexity, 1, 1)

        layout.addWidget(auth_group)

        self.tab_widget.addTab(security_widget, "🔒 安全设置")


    def _create_security_content(self):
        """创建安全设置内容"""
        content = QWidget()
        layout = QGridLayout(content)
        layout.setSpacing(20)
        layout.setColumnStretch(1, 1)

        # 标签样式 - 黑色字体以便在白色背景上清晰显示
        label_style = """
            QLabel {
                color: #1e293b;
                font-size: 14px;
                font-weight: 600;
                min-width: 120px;
            }
        """

        # 输入框样式 - 与其他模块保持一致
        input_style = """
            QSpinBox {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #1e293b;
                min-height: 20px;
            }
            QSpinBox:focus {
                border-color: #3b82f6;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                width: 18px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background: #e2e8f0;
            }
        """

        # 复选框样式 - 黑色字体，与其他模块保持一致
        checkbox_style = """
            QCheckBox {
                color: #1e293b;
                font-size: 14px;
                font-weight: 500;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #e2e8f0;
                border-radius: 4px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #ef4444;
                border: 2px solid #ef4444;
            }
            QCheckBox::indicator:hover {
                border-color: #ef4444;
            }
        """

        # 最小密码长度
        password_label = QLabel("最小密码长度:")
        password_label.setStyleSheet(label_style)
        layout.addWidget(password_label, 0, 0)

        self.min_password_length = QSpinBox()
        self.min_password_length.setRange(6, 20)
        self.min_password_length.setValue(8)
        self.min_password_length.setStyleSheet(input_style)
        layout.addWidget(self.min_password_length, 0, 1)

        # 会话超时时间
        session_label = QLabel("会话超时(分钟):")
        session_label.setStyleSheet(label_style)
        layout.addWidget(session_label, 1, 0)

        self.session_timeout = QSpinBox()
        self.session_timeout.setRange(5, 480)
        self.session_timeout.setValue(30)
        self.session_timeout.setStyleSheet(input_style)
        layout.addWidget(self.session_timeout, 1, 1)

        # 安全选项
        self.require_uppercase = QCheckBox("要求包含大写字母")
        self.require_uppercase.setStyleSheet(checkbox_style)
        layout.addWidget(self.require_uppercase, 2, 0, 1, 2)

        self.require_numbers = QCheckBox("要求包含数字")
        self.require_numbers.setStyleSheet(checkbox_style)
        layout.addWidget(self.require_numbers, 3, 0, 1, 2)

        self.auto_lock = QCheckBox("启用自动锁屏")
        self.auto_lock.setStyleSheet(checkbox_style)
        layout.addWidget(self.auto_lock, 4, 0, 1, 2)

        return content
    
    def _create_modern_backup_settings_tab(self):
        """创建备份设置选项卡 - 与快速查询样式完全一致"""
        backup_widget = QWidget()
        layout = QVBoxLayout(backup_widget)
        layout.setSpacing(20)

        # 备份设置 - 与快速查询样式完全一致
        backup_group = QGroupBox("备份设置")
        backup_group.setStyleSheet(self._get_group_style())
        backup_layout = QGridLayout(backup_group)
        backup_layout.setSpacing(15)

        # 备份频率
        backup_layout.addWidget(QLabel("备份频率:"), 0, 0)
        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems(["每日", "每周", "每月"])
        self.backup_frequency.setFixedHeight(35)
        self._style_input(self.backup_frequency)
        backup_layout.addWidget(self.backup_frequency, 0, 1)

        # 备份路径
        backup_layout.addWidget(QLabel("备份路径:"), 1, 0)
        self.backup_path = QLineEdit("./backup")
        self.backup_path.setFixedHeight(35)
        self._style_input(self.backup_path)
        backup_layout.addWidget(self.backup_path, 1, 1)

        layout.addWidget(backup_group)

        self.tab_widget.addTab(backup_widget, "💾 备份设置")

    def _test_database_connection(self):
        """测试数据库连接"""
        QMessageBox.information(self, "连接测试", "数据库连接测试功能开发中...")

    def _create_auto_backup_content(self):
        """创建自动备份设置内容"""
        content = QWidget()
        layout = QGridLayout(content)
        layout.setSpacing(20)
        layout.setColumnStretch(1, 1)

        # 与其他模块保持一致的标签样式
        label_style = """
            QLabel {
                color: #1e293b;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }
        """

        # 与其他模块保持一致的输入框样式
        input_style = """
            QLineEdit, QSpinBox, QComboBox {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #1e293b;
                min-height: 20px;
            }
            QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
                border-color: #3b82f6;
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #6b7280;
                margin-right: 8px;
            }
            QComboBox QAbstractItemView {
                background: white;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                color: #1e293b;
                selection-background-color: #3b82f6;
                selection-color: white;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                width: 18px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background: #e2e8f0;
            }
        """

        # 复选框样式
        checkbox_style = """
            QCheckBox {
                color: #1e293b;
                font-size: 14px;
                font-weight: 500;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #e2e8f0;
                border-radius: 4px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #10b981;
                border: 2px solid #10b981;
            }
            QCheckBox::indicator:hover {
                border-color: #10b981;
            }
        """

        # 启用自动备份
        self.enable_backup = QCheckBox("启用自动备份")
        self.enable_backup.setStyleSheet(checkbox_style)
        layout.addWidget(self.enable_backup, 0, 0, 1, 2)

        # 备份频率
        frequency_label = QLabel("备份频率:")
        frequency_label.setStyleSheet(label_style)
        layout.addWidget(frequency_label, 1, 0)

        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems(["每日", "每周", "每月"])
        self.backup_frequency.setStyleSheet(input_style)
        layout.addWidget(self.backup_frequency, 1, 1)

        # 备份路径
        path_label = QLabel("备份路径:")
        path_label.setStyleSheet(label_style)
        layout.addWidget(path_label, 2, 0)

        path_layout = QHBoxLayout()
        self.backup_path = QLineEdit("D:/WMS_Backup")
        self.backup_path.setStyleSheet(input_style)
        path_layout.addWidget(self.backup_path)

        browse_btn = QPushButton("浏览")
        browse_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6b7280, stop:1 #6b7280dd);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                padding: 8px 16px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6b7280dd, stop:1 #6b7280bb);
            }
        """)
        browse_btn.clicked.connect(self._browse_backup_path)
        path_layout.addWidget(browse_btn)

        layout.addLayout(path_layout, 2, 1)

        # 保留备份数量
        count_label = QLabel("保留备份数量:")
        count_label.setStyleSheet(label_style)
        layout.addWidget(count_label, 3, 0)

        self.backup_count = QSpinBox()
        self.backup_count.setRange(1, 30)
        self.backup_count.setValue(7)
        self.backup_count.setStyleSheet(input_style)
        layout.addWidget(self.backup_count, 3, 1)

        return content

    def _create_manual_backup_content(self):
        """创建手动备份内容"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)

        # 说明文字
        info_label = QLabel("点击下方按钮立即创建数据库备份")
        info_label.setStyleSheet("""
            QLabel {
                color: #6b7280;
                font-size: 14px;
                font-weight: 500;
                padding: 12px;
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
            }
        """)
        layout.addWidget(info_label)

        # 立即备份按钮
        manual_backup_btn = QPushButton("💾 立即备份")
        manual_backup_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6, stop:1 #3b82f6dd);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                padding: 12px 24px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6dd, stop:1 #3b82f6bb);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563eb, stop:1 #1d4ed8);
            }
        """)
        manual_backup_btn.clicked.connect(self._manual_backup)
        layout.addWidget(manual_backup_btn)

        return content
    
    def _load_current_settings(self):
        """加载当前设置"""
        # 这里可以从配置文件或数据库加载当前设置
        pass
    
    def _save_settings(self):
        """保存设置"""
        # 创建现代化的保存成功对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("保存成功")
        msg_box.setText("✅ 系统设置已成功保存！")
        msg_box.setInformativeText("所有配置更改已应用到系统中。")
        msg_box.setIcon(QMessageBox.Icon.Information)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

        # 设置现代化样式
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(248, 250, 252, 0.95));
                border: 2px solid rgba(59, 130, 246, 0.3);
                border-radius: 16px;
            }
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(16, 185, 129, 0.9),
                    stop:1 rgba(5, 150, 105, 0.9));
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: 600;
                min-width: 80px;
                min-height: 35px;
            }
            QMessageBox QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.95),
                    stop:1 rgba(16, 185, 129, 0.95));
            }
        """)

        msg_box.exec()

    def _test_database_connection(self):
        """测试数据库连接"""
        # 更新连接状态显示
        if hasattr(self, 'connection_status'):
            self.connection_status.setText("🔄 正在测试连接...")
            self.connection_status.setStyleSheet("""
                QLabel {
                    color: rgba(245, 158, 11, 1);
                    font-size: 16px;
                    font-weight: 500;
                    padding: 15px;
                    background: rgba(245, 158, 11, 0.1);
                    border: 1px solid rgba(245, 158, 11, 0.3);
                    border-radius: 10px;
                    margin-top: 10px;
                }
            """)

            # 模拟连接测试
            from PyQt6.QtCore import QTimer
            timer = QTimer()
            timer.singleShot(1500, self._show_connection_result)

    def _show_connection_result(self):
        """显示连接测试结果"""
        import random
        success = random.choice([True, True, True, False])  # 75%成功率

        if success:
            self.connection_status.setText("✅ 数据库连接成功！")
            self.connection_status.setStyleSheet("""
                QLabel {
                    color: rgba(16, 185, 129, 1);
                    font-size: 16px;
                    font-weight: 500;
                    padding: 15px;
                    background: rgba(16, 185, 129, 0.1);
                    border: 1px solid rgba(16, 185, 129, 0.3);
                    border-radius: 10px;
                    margin-top: 10px;
                }
            """)
        else:
            self.connection_status.setText("❌ 连接失败，请检查配置")
            self.connection_status.setStyleSheet("""
                QLabel {
                    color: rgba(239, 68, 68, 1);
                    font-size: 16px;
                    font-weight: 500;
                    padding: 15px;
                    background: rgba(239, 68, 68, 0.1);
                    border: 1px solid rgba(239, 68, 68, 0.3);
                    border-radius: 10px;
                    margin-top: 10px;
                }
            """)
    
    def _browse_backup_path(self):
        """浏览备份路径"""
        path = QFileDialog.getExistingDirectory(self, "选择备份路径",
                                               self.backup_path.text(),
                                               QFileDialog.Option.ShowDirsOnly)
        if path:
            self.backup_path.setText(path)

    def _manual_backup(self):
        """手动备份"""
        # 创建现代化的备份完成对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("备份完成")
        msg_box.setText("🚀 数据库备份已成功完成！")
        msg_box.setInformativeText(f"备份文件已保存到：{self.backup_path.text()}")
        msg_box.setIcon(QMessageBox.Icon.Information)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

        # 设置现代化样式
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(248, 250, 252, 0.95));
                border: 2px solid rgba(245, 158, 11, 0.3);
                border-radius: 16px;
            }
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(245, 158, 11, 0.9),
                    stop:1 rgba(217, 119, 6, 0.9));
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: 600;
                min-width: 80px;
                min-height: 35px;
            }
            QMessageBox QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(251, 191, 36, 0.95),
                    stop:1 rgba(245, 158, 11, 0.95));
            }
        """)

        msg_box.exec()
