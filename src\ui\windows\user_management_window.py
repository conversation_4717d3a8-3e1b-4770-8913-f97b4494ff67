"""
WMS客户端系统 - 用户管理窗口
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QTableWidget, QTableWidgetItem,
    QLineEdit, QComboBox, QDateEdit, QMessageBox, QHeaderView,
    QApplication, QScrollArea
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont

from ..components.base_components import (
    BaseCard, BaseTable, PrimaryButton, SecondaryButton,
    TitleLabel, BodyLabel, UnifiedToolbar
)
from ..styles import theme


class UserManagementWindow(QMainWindow):
    """用户管理窗口"""
    
    # 信号定义
    back_to_dashboard = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        self._load_sample_data()
    
    def _setup_ui(self):
        """设置UI - 触摸屏优化"""
        self.setWindowTitle("用户管理")
        
        # 获取屏幕信息
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        
        # 设置全屏
        self.setGeometry(screen_geometry)
        self.showFullScreen()
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:0.5 #2563eb, stop:1 #3b82f6);
            }
        """)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        margin = max(15, min(25, int(screen_geometry.width() * 0.018)))
        spacing = max(10, min(18, int(screen_geometry.height() * 0.015)))
        layout.setContentsMargins(margin, margin, margin, margin)
        layout.setSpacing(spacing)
        
        # 工具栏 - 与智能查询样式完全一致
        toolbar_widget = QWidget()
        toolbar_widget.setFixedHeight(80)
        toolbar_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:1 rgba(248, 250, 252, 0.95));
                border-radius: 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)

        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(30, 20, 30, 20)
        toolbar_layout.setSpacing(20)

        # 返回按钮
        back_btn = QPushButton("← 返回主界面")
        back_btn.setFixedSize(140, 40)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 600;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        back_btn.clicked.connect(self.back_to_dashboard.emit)
        toolbar_layout.addWidget(back_btn)

        # 标题
        title_label = QLabel("👥 用户管理")
        title_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 24px;
                font-weight: 700;
                background: transparent;
                border: none;
            }
        """)
        toolbar_layout.addWidget(title_label)

        toolbar_layout.addStretch()

        layout.addWidget(toolbar_widget)
        
        # 用户管理内容
        content_layout = QHBoxLayout()
        
        # 左侧：用户列表
        users_card = BaseCard()
        users_layout = QVBoxLayout()
        
        users_title = TitleLabel("用户列表", level=2)
        users_layout.addWidget(users_title)
        
        # 用户表格
        self.users_table = BaseTable(0, 6)
        self.users_table.setHorizontalHeaderLabels([
            "用户ID", "用户名", "姓名", "角色", "状态", "最后登录"
        ])
        
        # 设置表格高度
        table_height = max(300, min(500, int(screen_geometry.height() * 0.5)))
        self.users_table.setMaximumHeight(table_height)
        
        # 设置列宽自适应
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        users_layout.addWidget(self.users_table)
        users_card.add_layout(users_layout)
        content_layout.addWidget(users_card, 2)
        
        # 右侧：用户操作
        operations_card = BaseCard()
        operations_layout = QVBoxLayout()
        
        operations_title = TitleLabel("用户操作", level=2)
        operations_layout.addWidget(operations_title)
        
        # 添加用户表单
        form_layout = QGridLayout()
        
        # 用户名
        form_layout.addWidget(QLabel("用户名:"), 0, 0)
        self.username_input = QLineEdit()
        self.username_input.setFixedHeight(40)
        form_layout.addWidget(self.username_input, 0, 1)
        
        # 姓名
        form_layout.addWidget(QLabel("姓名:"), 1, 0)
        self.name_input = QLineEdit()
        self.name_input.setFixedHeight(40)
        form_layout.addWidget(self.name_input, 1, 1)
        
        # 角色
        form_layout.addWidget(QLabel("角色:"), 2, 0)
        self.role_combo = QComboBox()
        self.role_combo.addItems(["操作员", "管理员", "超级管理员"])
        self.role_combo.setFixedHeight(40)
        form_layout.addWidget(self.role_combo, 2, 1)
        
        # 密码
        form_layout.addWidget(QLabel("密码:"), 3, 0)
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setFixedHeight(40)
        form_layout.addWidget(self.password_input, 3, 1)
        
        operations_layout.addLayout(form_layout)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        
        add_btn = PrimaryButton("添加用户")
        add_btn.setFixedHeight(45)
        add_btn.clicked.connect(self._add_user)
        buttons_layout.addWidget(add_btn)
        
        edit_btn = SecondaryButton("编辑用户")
        edit_btn.setFixedHeight(45)
        edit_btn.clicked.connect(self._edit_user)
        buttons_layout.addWidget(edit_btn)
        
        delete_btn = SecondaryButton("删除用户")
        delete_btn.setFixedHeight(45)
        delete_btn.clicked.connect(self._delete_user)
        buttons_layout.addWidget(delete_btn)
        
        operations_layout.addLayout(buttons_layout)
        operations_layout.addStretch()
        
        operations_card.add_layout(operations_layout)
        content_layout.addWidget(operations_card, 1)
        
        layout.addLayout(content_layout)
    

    
    def _load_sample_data(self):
        """加载示例数据"""
        sample_users = [
            ["001", "admin", "系统管理员", "超级管理员", "正常", "2024-01-30 14:30"],
            ["002", "operator1", "张三", "操作员", "正常", "2024-01-30 13:45"],
            ["003", "operator2", "李四", "操作员", "正常", "2024-01-30 12:20"],
            ["004", "manager1", "王五", "管理员", "正常", "2024-01-30 11:15"],
            ["005", "operator3", "赵六", "操作员", "禁用", "2024-01-29 16:30"],
        ]
        
        self.users_table.setRowCount(len(sample_users))
        for row, user_data in enumerate(sample_users):
            for col, data in enumerate(user_data):
                item = QTableWidgetItem(str(data))
                # 状态列特殊处理
                if col == 4:  # 状态列
                    if data == "正常":
                        item.setBackground(Qt.GlobalColor.green)
                    else:
                        item.setBackground(Qt.GlobalColor.red)
                self.users_table.setItem(row, col, item)
    
    def _add_user(self):
        """添加用户"""
        username = self.username_input.text().strip()
        name = self.name_input.text().strip()
        role = self.role_combo.currentText()
        password = self.password_input.text().strip()
        
        if not all([username, name, password]):
            QMessageBox.warning(self, "警告", "请填写完整的用户信息！")
            return
        
        # 添加到表格
        row_count = self.users_table.rowCount()
        self.users_table.insertRow(row_count)
        
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
        
        new_user = [
            f"{row_count + 6:03d}",
            username,
            name,
            role,
            "正常",
            current_time
        ]
        
        for col, data in enumerate(new_user):
            item = QTableWidgetItem(str(data))
            if col == 4:  # 状态列
                item.setBackground(Qt.GlobalColor.green)
            self.users_table.setItem(row_count, col, item)
        
        # 清空输入框
        self.username_input.clear()
        self.name_input.clear()
        self.password_input.clear()
        
        QMessageBox.information(self, "成功", f"用户 {username} 添加成功！")
    
    def _edit_user(self):
        """编辑用户"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要编辑的用户！")
            return
        
        QMessageBox.information(self, "编辑用户", "编辑用户功能开发中...")
    
    def _delete_user(self):
        """删除用户"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要删除的用户！")
            return
        
        username = self.users_table.item(current_row, 1).text()
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除用户 {username} 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.users_table.removeRow(current_row)
            QMessageBox.information(self, "成功", f"用户 {username} 删除成功！")
