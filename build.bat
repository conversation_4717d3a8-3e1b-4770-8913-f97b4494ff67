@echo off
chcp 65001 >nul
title WMS客户端系统 - 打包工具

echo ================================================================================
echo 🏢 WMS库房自助出入库客户端系统 - 打包工具
echo © 2024 贵州睿云慧通科技有限公司
echo ================================================================================
echo.

echo 📋 选择打包模式:
echo [1] 快速打包 (仅核心功能，体积较小)
echo [2] 完整打包 (包含所有功能，体积较大)
echo [3] 检查环境
echo [4] 清理构建文件
echo [0] 退出
echo.

set /p choice=请选择 (0-4): 

if "%choice%"=="1" goto quick_build
if "%choice%"=="2" goto full_build
if "%choice%"=="3" goto check_env
if "%choice%"=="4" goto clean_build
if "%choice%"=="0" goto exit
goto invalid_choice

:quick_build
echo.
echo 🚀 开始快速打包...
echo 📦 安装核心依赖...
pip install -r requirements_build.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    goto end
)
echo ✅ 依赖安装完成
echo.
echo 🔨 开始构建...
python build_exe.py
goto end

:full_build
echo.
echo 🚀 开始完整打包...
echo 📦 安装完整依赖...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    goto end
)
echo ✅ 依赖安装完成
echo.
echo 🔨 开始构建...
python build_exe.py
goto end

:check_env
echo.
echo 🔍 检查环境...
python -c "import sys; print(f'Python版本: {sys.version}')"
python -c "try: import PyInstaller; print(f'PyInstaller版本: {PyInstaller.__version__}'); except: print('PyInstaller未安装')"
python -c "try: import PyQt6; print('PyQt6: 已安装'); except: print('PyQt6: 未安装')"
python -c "try: import sqlalchemy; print('SQLAlchemy: 已安装'); except: print('SQLAlchemy: 未安装')"
python -c "try: import cv2; print('OpenCV: 已安装'); except: print('OpenCV: 未安装')"
echo.
pause
goto menu

:clean_build
echo.
echo 🧹 清理构建文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__
for /r %%i in (*.pyc) do del "%%i" 2>nul
echo ✅ 清理完成
echo.
pause
goto menu

:invalid_choice
echo ❌ 无效选择，请重新输入
echo.
pause

:menu
cls
goto :eof

:exit
echo 👋 再见！
timeout /t 2 >nul
goto end

:end
echo.
echo 按任意键退出...
pause >nul
