# 库房自助出入库客户端系统 - Python依赖包

# 核心框架
PyQt6==6.6.1                    # GUI框架
SQLAlchemy==2.0.23               # ORM框架
alembic==1.13.1                  # 数据库迁移工具

# 数据库驱动
psycopg2-binary==2.9.9           # PostgreSQL驱动
sqlite3                          # SQLite驱动（Python内置）

# 图像处理与计算机视觉
opencv-python==4.8.1.78          # 计算机视觉库
Pillow==10.1.0                   # 图像处理库
numpy==1.24.3                    # 数值计算库

# 人脸识别
face-recognition==1.3.0          # 人脸识别库
dlib==19.24.2                    # 机器学习库（人脸识别依赖）

# OCR文字识别
pytesseract==0.3.10              # Tesseract OCR Python接口
paddleocr==2.7.0.3               # 百度PaddleOCR
easyocr==1.7.0                   # EasyOCR（备选方案）

# 条码和二维码识别
pyzbar==0.1.9                    # 条码识别库
qrcode==7.4.2                    # 二维码生成库
opencv-contrib-python==4.8.1.78  # OpenCV扩展模块

# 身份证读取
pyscard==2.0.7                   # 智能卡读取库
pycryptodome==3.19.0             # 加密解密库

# 打印功能
python-escpos==3.0a9             # ESC/POS打印机控制
reportlab==4.0.7                 # PDF生成库
jinja2==3.1.2                    # 模板引擎

# 硬件设备通信
pyserial==3.5                    # 串口通信
pyusb==1.2.1                     # USB设备通信

# 网络和HTTP
requests==2.31.0                 # HTTP请求库
urllib3==2.1.0                   # HTTP客户端

# 配置和日志
configparser                     # 配置文件解析（Python内置）
logging                          # 日志模块（Python内置）
colorlog==6.8.0                  # 彩色日志输出

# 数据处理
pandas==2.1.4                    # 数据分析库
openpyxl==3.1.2                  # Excel文件处理
xlsxwriter==3.1.9                # Excel文件写入

# 加密和安全
cryptography==41.0.8             # 加密库
bcrypt==4.1.2                    # 密码哈希
PyJWT==2.8.0                     # JWT令牌

# 时间和日期
python-dateutil==2.8.2           # 日期时间处理
pytz==2023.3.post1               # 时区处理

# 系统工具
psutil==5.9.6                    # 系统信息获取
pathlib                          # 路径处理（Python内置）
os                                # 操作系统接口（Python内置）
sys                               # 系统参数（Python内置）

# 测试框架
pytest==7.4.3                    # 测试框架
pytest-cov==4.1.0                # 测试覆盖率
pytest-qt==4.2.0                 # PyQt测试支持
pytest-mock==3.12.0              # Mock测试

# 代码质量
black==23.11.0                   # 代码格式化
flake8==6.1.0                    # 代码检查
mypy==1.7.1                      # 类型检查
isort==5.12.0                    # 导入排序

# 打包和分发
PyInstaller==6.2.0               # 打包工具
setuptools==69.0.2               # 安装工具
wheel==0.42.0                    # 打包格式

# 开发工具
ipython==8.18.1                  # 交互式Python
jupyter==1.0.0                   # Jupyter笔记本
matplotlib==3.8.2                # 绘图库（用于数据可视化）

# 多媒体处理
moviepy==1.0.3                   # 视频处理（如需要处理视频）
sounddevice==0.4.6               # 音频设备（如需要音频提示）

# 国际化
babel==2.13.1                    # 国际化支持
gettext                          # 多语言支持（Python内置）

# 性能优化
cython==3.0.6                    # Python C扩展
numba==0.58.1                    # JIT编译器

# 特定平台依赖（Windows）
pywin32==306; sys_platform == "win32"     # Windows API
wmi==1.5.1; sys_platform == "win32"       # Windows管理接口

# 可选依赖（根据具体硬件设备选择）
# 如果使用特定品牌的设备，可能需要额外的驱动库
# 例如：
# zebra-printer==1.0.0            # 斑马打印机
# honeywell-scanner==1.0.0        # 霍尼韦尔扫描枪

# 开发环境依赖（仅开发时需要）
# pre-commit==3.6.0               # Git钩子
# sphinx==7.2.6                   # 文档生成
# sphinx-rtd-theme==1.3.0         # 文档主题

# 注意事项：
# 1. 某些库（如dlib）可能需要额外的系统依赖
# 2. 人脸识别功能需要安装cmake和Visual Studio Build Tools
# 3. OCR功能需要安装Tesseract OCR引擎
# 4. 部分硬件设备可能需要安装对应的驱动程序
# 5. 建议使用虚拟环境安装依赖，避免版本冲突

# 安装命令：
# pip install -r requirements.txt

# 如果遇到安装问题，可以尝试：
# pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
