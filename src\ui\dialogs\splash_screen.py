"""
启动画面 - 显示公司logo和版权信息
"""

import sys
import os
import platform

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from PyQt6.QtWidgets import QSplashScreen, QLabel, QVBoxLayout, QWidget, QProgressBar
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QPixmap, QFont, QPainter, QColor, QLinearGradient

from ..components.base_components import TitleLabel, BodyLabel
from ..styles.theme import theme
from config.copyright import get_copyright_info


class SplashScreen(QSplashScreen):
    """启动画面"""
    
    # 信号定义
    finished = pyqtSignal()
    
    def __init__(self):
        # 创建启动画面背景
        pixmap = self._create_splash_pixmap()
        super().__init__(pixmap)
        
        self.copyright_info = get_copyright_info()
        self.progress = 0
        self._setup_ui()
        self._setup_timer()
    
    def _create_splash_pixmap(self):
        """创建启动画面背景图"""
        width, height = 600, 400
        pixmap = QPixmap(width, height)
        
        # 创建渐变背景
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 设置渐变背景
        gradient = QLinearGradient(0, 0, 0, height)
        gradient.setColorAt(0, QColor("#1e3a8a"))  # 深蓝色
        gradient.setColorAt(1, QColor("#3b82f6"))  # 蓝色
        
        painter.fillRect(0, 0, width, height, gradient)
        painter.end()
        
        return pixmap
    
    def _setup_ui(self):
        """设置UI"""
        # 设置最强的窗口标志组合，确保绝对置顶且不可被遮挡
        self.setWindowFlags(
            Qt.WindowType.Dialog |                      # 对话框类型
            Qt.WindowType.FramelessWindowHint |         # 无边框
            Qt.WindowType.WindowStaysOnTopHint |        # 始终置顶
            Qt.WindowType.WindowSystemMenuHint |        # 系统菜单
            Qt.WindowType.WindowCloseButtonHint |       # 关闭按钮
            Qt.WindowType.MSWindowsFixedSizeDialogHint  # Windows固定大小对话框
        )

        # 设置窗口属性，确保不被打断
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating, False)
        self.setAttribute(Qt.WidgetAttribute.WA_AlwaysShowToolTips, True)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, False)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose, False)
        self.setAttribute(Qt.WidgetAttribute.WA_QuitOnClose, False)  # 防止关闭时退出应用

        # 禁用所有可能导致窗口失去焦点的交互
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)  # 强制获得焦点

        # 设置窗口模态性，阻止其他窗口获得焦点
        self.setWindowModality(Qt.WindowModality.ApplicationModal)

        # 设置窗口为全屏置顶
        self.setWindowState(Qt.WindowState.WindowActive)

        # 安装事件过滤器，拦截所有可能影响窗口的事件
        self.installEventFilter(self)

        # 尝试设置窗口级别为最高（如果支持的话）
        try:
            if hasattr(self, 'setWindowLevel'):
                self.setWindowLevel(1000)  # 设置极高的窗口层级
        except:
            pass

        # 设置文本颜色为白色
        self.setStyleSheet("color: white;")

        # 确保窗口居中显示
        self.center_on_screen()

        # 创建强制置顶定时器
        self._setup_stay_on_top_timer()
    
    def _setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_progress)
        self.timer.start(100)  # 每100ms更新一次
    
    def _update_progress(self):
        """更新进度"""
        self.progress += 2
        
        if self.progress <= 30:
            self.showMessage("正在初始化系统...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter)
        elif self.progress <= 60:
            self.showMessage("正在加载组件...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter)
        elif self.progress <= 90:
            self.showMessage("正在准备界面...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter)
        else:
            self.showMessage("启动完成", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter)
        
        if self.progress >= 100:
            self.timer.stop()
            if hasattr(self, 'stay_on_top_timer'):
                self.stay_on_top_timer.stop()
            self.finished.emit()
            self.close()

    def update_progress(self, value, message):
        """公共方法：更新进度和消息"""
        self.progress = value
        self.showMessage(message, Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter)
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setValue(self.progress)

    def drawContents(self, painter):
        """绘制内容"""
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 设置字体
        title_font = QFont("Microsoft YaHei UI", 24, QFont.Weight.Bold)
        subtitle_font = QFont("Microsoft YaHei UI", 14)
        copyright_font = QFont("Microsoft YaHei UI", 10)
        
        # 绘制公司logo（文字版）
        painter.setFont(QFont("Microsoft YaHei UI", 48))
        painter.setPen(QColor("white"))
        logo_rect = self.rect()
        logo_rect.setHeight(100)
        logo_rect.moveTop(80)
        painter.drawText(logo_rect, Qt.AlignmentFlag.AlignCenter, "🏭")
        
        # 绘制产品名称
        painter.setFont(title_font)
        painter.setPen(QColor("white"))
        title_rect = self.rect()
        title_rect.setHeight(40)
        title_rect.moveTop(180)
        painter.drawText(title_rect, Qt.AlignmentFlag.AlignCenter, 
                        self.copyright_info["product_name"])
        
        # 绘制产品描述
        painter.setFont(subtitle_font)
        painter.setPen(QColor("#e0e7ff"))
        subtitle_rect = self.rect()
        subtitle_rect.setHeight(30)
        subtitle_rect.moveTop(220)
        painter.drawText(subtitle_rect, Qt.AlignmentFlag.AlignCenter, 
                        "智能仓储管理 · 高效 · 智能 · 安全")
        
        # 绘制版权信息
        painter.setFont(copyright_font)
        painter.setPen(QColor("#cbd5e1"))
        copyright_rect = self.rect()
        copyright_rect.setHeight(20)
        copyright_rect.moveTop(280)
        painter.drawText(copyright_rect, Qt.AlignmentFlag.AlignCenter, 
                        self.copyright_info["copyright_text"])
        
        # 绘制著作权声明
        rights_rect = self.rect()
        rights_rect.setHeight(20)
        rights_rect.moveTop(300)
        painter.drawText(rights_rect, Qt.AlignmentFlag.AlignCenter, 
                        "版权所有 · 保留所有权利")
        
        # 绘制进度条
        progress_rect = self.rect()
        progress_rect.setHeight(4)
        progress_rect.setWidth(400)
        progress_rect.moveTop(350)
        progress_rect.moveLeft((self.width() - 400) // 2)
        
        # 进度条背景
        painter.setPen(QColor("#64748b"))
        painter.setBrush(QColor("#64748b"))
        painter.drawRect(progress_rect)
        
        # 进度条前景
        progress_fill = progress_rect
        progress_fill.setWidth(int(400 * self.progress / 100))
        painter.setPen(QColor("#10b981"))
        painter.setBrush(QColor("#10b981"))
        painter.drawRect(progress_fill)
        
        # 调用父类方法绘制消息
        super().drawContents(painter)

    def center_on_screen(self):
        """将窗口居中显示在屏幕上"""
        from PyQt6.QtWidgets import QApplication
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.geometry()
            x = (screen_geometry.width() - self.width()) // 2
            y = (screen_geometry.height() - self.height()) // 2
            self.move(x, y)

    def _setup_stay_on_top_timer(self):
        """设置强制置顶定时器"""
        self.stay_on_top_timer = QTimer()
        self.stay_on_top_timer.timeout.connect(self._force_stay_on_top)
        self.stay_on_top_timer.start(20)  # 每20ms强制置顶一次，更频繁

    def _force_stay_on_top(self):
        """强制保持窗口置顶"""
        if self.isVisible():
            # 多重强制置顶策略
            self.raise_()
            self.activateWindow()
            self.setFocus()

            # 确保窗口在最前面且处于活动状态
            self.setWindowState(Qt.WindowState.WindowActive)

            # 强制重新设置窗口标志
            current_flags = self.windowFlags()
            if not (current_flags & Qt.WindowType.WindowStaysOnTopHint):
                self.setWindowFlags(current_flags | Qt.WindowType.WindowStaysOnTopHint)
                self.show()

            # 强制刷新
            self.repaint()
            self.update()

            # 在Windows系统上使用系统API强制置顶
            self._force_topmost_with_windows_api()

    def show(self):
        """重写show方法，确保窗口置顶显示"""
        super().show()
        self.raise_()
        self.activateWindow()
        # 强制刷新显示
        self.repaint()
        # 确保窗口获得焦点
        self.setFocus()
        # 强制置顶
        self._force_stay_on_top()

    def _force_topmost_with_windows_api(self):
        """使用Windows API强制置顶（如果在Windows系统上）"""
        if platform.system() == "Windows":
            try:
                import ctypes
                from ctypes import wintypes

                # 获取窗口句柄
                hwnd = int(self.winId())

                # Windows API常量
                HWND_TOPMOST = -1
                SWP_NOMOVE = 0x0002
                SWP_NOSIZE = 0x0001
                SWP_SHOWWINDOW = 0x0040

                # 调用SetWindowPos API强制置顶
                ctypes.windll.user32.SetWindowPos(
                    hwnd, HWND_TOPMOST, 0, 0, 0, 0,
                    SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW
                )

                # 强制激活窗口
                ctypes.windll.user32.SetForegroundWindow(hwnd)
                ctypes.windll.user32.BringWindowToTop(hwnd)

            except Exception as e:
                # 如果Windows API调用失败，静默忽略
                pass

    def eventFilter(self, obj, event):
        """事件过滤器，拦截所有可能影响启动画面的事件"""
        from PyQt6.QtCore import QEvent

        # 拦截可能导致窗口失去焦点或被隐藏的事件
        blocked_events = [
            QEvent.Type.WindowDeactivate,      # 窗口失去激活
            QEvent.Type.FocusOut,              # 失去焦点
            QEvent.Type.Hide,                  # 隐藏事件
            QEvent.Type.Close,                 # 关闭事件
            QEvent.Type.WindowStateChange,     # 窗口状态变化
            QEvent.Type.ActivationChange,      # 激活状态变化
            QEvent.Type.ApplicationDeactivate, # 应用程序失去激活
        ]

        if event.type() in blocked_events:
            # 阻止这些事件，并强制保持置顶
            self._force_stay_on_top()
            return True  # 阻止事件传播

        return super().eventFilter(obj, event)

    def closeEvent(self, event):
        """重写关闭事件，防止被意外关闭"""
        # 在启动过程中不允许关闭
        if hasattr(self, 'timer') and self.timer.isActive():
            event.ignore()  # 忽略关闭事件
            self._force_stay_on_top()  # 强制保持置顶
        else:
            event.accept()

    def hideEvent(self, event):
        """重写隐藏事件，防止被隐藏"""
        # 如果在启动过程中被隐藏，立即重新显示
        if hasattr(self, 'timer') and self.timer.isActive():
            event.ignore()
            QTimer.singleShot(0, self.show)  # 立即重新显示
            self._force_stay_on_top()
        else:
            super().hideEvent(event)

    def focusOutEvent(self, event):
        """重写失去焦点事件"""
        # 立即重新获得焦点
        if hasattr(self, 'timer') and self.timer.isActive():
            QTimer.singleShot(0, self.setFocus)
            self._force_stay_on_top()
        super().focusOutEvent(event)

def show_splash_screen():
    """显示启动画面"""
    splash = SplashScreen()
    splash.show()
    return splash


if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication([])
    
    splash = show_splash_screen()
    
    # 模拟启动完成后的操作
    def on_finished():
        print("启动画面完成")
        app.quit()
    
    splash.finished.connect(on_finished)
    
    app.exec()
