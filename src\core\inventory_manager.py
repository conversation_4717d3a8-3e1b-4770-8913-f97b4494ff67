"""
库存管理核心业务模块
© 2024 贵州睿云慧通科技有限公司
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from ..models.database import db_manager
from ..models.inventory import Inventory, InventoryTransaction, Location, Warehouse
from ..models.product import Product, ProductBatch
from ..models.user import User
from ..utils.logger import get_logger
from ..utils.exceptions import ValidationError, BusinessError

logger = get_logger(__name__)

class InventoryManager:
    """库存管理器"""
    
    def get_current_stock(self, product_id: int, location_id: int = None) -> Dict[str, Any]:
        """
        获取当前库存
        
        Args:
            product_id: 产品ID
            location_id: 库位ID，为None时返回所有库位的库存
            
        Returns:
            库存信息字典
        """
        try:
            with db_manager.get_session() as session:
                query = session.query(Inventory).filter(Inventory.product_id == product_id)
                
                if location_id:
                    query = query.filter(Inventory.location_id == location_id)
                
                inventory_items = query.all()
                
                if not inventory_items:
                    return {
                        "product_id": product_id,
                        "location_id": location_id,
                        "total_quantity": 0,
                        "available_quantity": 0,
                        "reserved_quantity": 0,
                        "locations": []
                    }
                
                total_quantity = sum(item.quantity for item in inventory_items)
                available_quantity = sum(item.available_quantity for item in inventory_items)
                reserved_quantity = sum(item.reserved_quantity for item in inventory_items)
                
                locations = []
                for item in inventory_items:
                    location = session.query(Location).filter(Location.id == item.location_id).first()
                    locations.append({
                        "location_id": item.location_id,
                        "location_code": location.code if location else None,
                        "location_name": location.name if location else None,
                        "quantity": item.quantity,
                        "available_quantity": item.available_quantity,
                        "reserved_quantity": item.reserved_quantity
                    })
                
                return {
                    "product_id": product_id,
                    "location_id": location_id,
                    "total_quantity": total_quantity,
                    "available_quantity": available_quantity,
                    "reserved_quantity": reserved_quantity,
                    "locations": locations
                }
                
        except Exception as e:
            logger.error(f"获取库存失败: {e}")
            raise BusinessError("获取库存失败")
    
    def update_stock(self, product_id: int, location_id: int, quantity: int, 
                    operation: str, user_id: int, reference_type: str = None, 
                    reference_id: int = None, batch_id: int = None, 
                    cost_price: float = None, reason: str = None) -> bool:
        """
        更新库存
        
        Args:
            product_id: 产品ID
            location_id: 库位ID
            quantity: 数量变化（正数为增加，负数为减少）
            operation: 操作类型（inbound, outbound, adjustment, transfer）
            user_id: 操作用户ID
            reference_type: 关联类型
            reference_id: 关联ID
            batch_id: 批次ID
            cost_price: 成本价
            reason: 原因
            
        Returns:
            是否成功
        """
        try:
            with db_manager.get_session() as session:
                # 查找或创建库存记录
                inventory = session.query(Inventory).filter(
                    and_(
                        Inventory.product_id == product_id,
                        Inventory.location_id == location_id,
                        Inventory.batch_id == batch_id
                    )
                ).first()
                
                if not inventory:
                    if quantity < 0:
                        raise ValidationError("库存不足")
                    
                    inventory = Inventory(
                        product_id=product_id,
                        location_id=location_id,
                        batch_id=batch_id,
                        quantity=0,
                        available_quantity=0,
                        cost_price=cost_price
                    )
                    session.add(inventory)
                
                # 检查库存是否足够（出库操作）
                if quantity < 0 and inventory.available_quantity < abs(quantity):
                    raise ValidationError("库存不足")
                
                # 更新库存数量
                inventory.quantity += quantity
                inventory.available_quantity += quantity
                
                if cost_price:
                    inventory.cost_price = cost_price
                
                # 更新时间戳
                if operation == "inbound":
                    inventory.last_inbound_date = datetime.now()
                elif operation == "outbound":
                    inventory.last_outbound_date = datetime.now()
                
                inventory.updated_at = datetime.now()
                
                # 记录库存事务
                transaction = InventoryTransaction(
                    product_id=product_id,
                    location_id=location_id,
                    transaction_type=operation,
                    quantity=quantity,
                    reference_type=reference_type,
                    reference_id=reference_id,
                    batch_id=batch_id,
                    cost_price=cost_price,
                    reason=reason,
                    user_id=user_id
                )
                session.add(transaction)
                
                session.commit()
                
                logger.info(f"库存更新成功: 产品{product_id}, 库位{location_id}, 数量{quantity}")
                return True
                
        except Exception as e:
            logger.error(f"库存更新失败: {e}")
            raise
    
    def reserve_inventory(self, product_id: int, location_id: int, quantity: int, 
                         batch_id: int = None) -> bool:
        """
        预留库存
        
        Args:
            product_id: 产品ID
            location_id: 库位ID
            quantity: 预留数量
            batch_id: 批次ID
            
        Returns:
            是否成功
        """
        try:
            with db_manager.get_session() as session:
                inventory = session.query(Inventory).filter(
                    and_(
                        Inventory.product_id == product_id,
                        Inventory.location_id == location_id,
                        Inventory.batch_id == batch_id
                    )
                ).first()
                
                if not inventory or inventory.available_quantity < quantity:
                    raise ValidationError("库存不足")
                
                inventory.reserved_quantity += quantity
                inventory.available_quantity -= quantity
                inventory.updated_at = datetime.now()
                
                session.commit()
                
                logger.info(f"库存预留成功: 产品{product_id}, 库位{location_id}, 数量{quantity}")
                return True
                
        except Exception as e:
            logger.error(f"库存预留失败: {e}")
            raise
    
    def release_reservation(self, product_id: int, location_id: int, quantity: int, 
                           batch_id: int = None) -> bool:
        """
        释放预留库存
        
        Args:
            product_id: 产品ID
            location_id: 库位ID
            quantity: 释放数量
            batch_id: 批次ID
            
        Returns:
            是否成功
        """
        try:
            with db_manager.get_session() as session:
                inventory = session.query(Inventory).filter(
                    and_(
                        Inventory.product_id == product_id,
                        Inventory.location_id == location_id,
                        Inventory.batch_id == batch_id
                    )
                ).first()
                
                if not inventory or inventory.reserved_quantity < quantity:
                    raise ValidationError("预留库存不足")
                
                inventory.reserved_quantity -= quantity
                inventory.available_quantity += quantity
                inventory.updated_at = datetime.now()
                
                session.commit()
                
                logger.info(f"库存预留释放成功: 产品{product_id}, 库位{location_id}, 数量{quantity}")
                return True
                
        except Exception as e:
            logger.error(f"库存预留释放失败: {e}")
            raise
    
    def get_stock_by_location(self, location_id: int) -> List[Dict[str, Any]]:
        """
        按库位获取库存
        
        Args:
            location_id: 库位ID
            
        Returns:
            库存列表
        """
        try:
            with db_manager.get_session() as session:
                inventory_items = session.query(Inventory).filter(
                    Inventory.location_id == location_id
                ).all()
                
                result = []
                for item in inventory_items:
                    product = session.query(Product).filter(Product.id == item.product_id).first()
                    batch = None
                    if item.batch_id:
                        batch = session.query(ProductBatch).filter(ProductBatch.id == item.batch_id).first()
                    
                    result.append({
                        "inventory": item.to_dict(),
                        "product": product.to_dict() if product else None,
                        "batch": batch.to_dict() if batch else None
                    })
                
                return result
                
        except Exception as e:
            logger.error(f"按库位获取库存失败: {e}")
            raise BusinessError("获取库存失败")
    
    def get_low_stock_products(self, warehouse_id: int = None) -> List[Dict[str, Any]]:
        """
        获取低库存产品
        
        Args:
            warehouse_id: 仓库ID
            
        Returns:
            低库存产品列表
        """
        try:
            with db_manager.get_session() as session:
                # 查询库存低于最小库存水平的产品
                query = session.query(
                    Product,
                    func.sum(Inventory.quantity).label('total_quantity')
                ).join(
                    Inventory, Product.id == Inventory.product_id
                ).group_by(Product.id).having(
                    func.sum(Inventory.quantity) < Product.min_stock_level
                )
                
                if warehouse_id:
                    query = query.join(Location, Inventory.location_id == Location.id).filter(
                        Location.warehouse_id == warehouse_id
                    )
                
                results = query.all()
                
                low_stock_products = []
                for product, total_quantity in results:
                    low_stock_products.append({
                        "product": product.to_dict(),
                        "current_stock": total_quantity,
                        "min_stock_level": product.min_stock_level,
                        "shortage": product.min_stock_level - total_quantity
                    })
                
                return low_stock_products
                
        except Exception as e:
            logger.error(f"获取低库存产品失败: {e}")
            raise BusinessError("获取低库存产品失败")
    
    def transfer_inventory(self, product_id: int, from_location_id: int, 
                          to_location_id: int, quantity: int, user_id: int,
                          batch_id: int = None, reason: str = None) -> bool:
        """
        库存转移
        
        Args:
            product_id: 产品ID
            from_location_id: 源库位ID
            to_location_id: 目标库位ID
            quantity: 转移数量
            user_id: 操作用户ID
            batch_id: 批次ID
            reason: 转移原因
            
        Returns:
            是否成功
        """
        try:
            with db_manager.get_session() as session:
                # 检查源库位库存
                from_inventory = session.query(Inventory).filter(
                    and_(
                        Inventory.product_id == product_id,
                        Inventory.location_id == from_location_id,
                        Inventory.batch_id == batch_id
                    )
                ).first()
                
                if not from_inventory or from_inventory.available_quantity < quantity:
                    raise ValidationError("源库位库存不足")
                
                # 减少源库位库存
                self.update_stock(
                    product_id=product_id,
                    location_id=from_location_id,
                    quantity=-quantity,
                    operation="transfer",
                    user_id=user_id,
                    reference_type="transfer",
                    batch_id=batch_id,
                    reason=f"转移到库位{to_location_id}: {reason}"
                )
                
                # 增加目标库位库存
                self.update_stock(
                    product_id=product_id,
                    location_id=to_location_id,
                    quantity=quantity,
                    operation="transfer",
                    user_id=user_id,
                    reference_type="transfer",
                    batch_id=batch_id,
                    cost_price=from_inventory.cost_price,
                    reason=f"从库位{from_location_id}转移: {reason}"
                )
                
                logger.info(f"库存转移成功: 产品{product_id}, {from_location_id}->{to_location_id}, 数量{quantity}")
                return True
                
        except Exception as e:
            logger.error(f"库存转移失败: {e}")
            raise
