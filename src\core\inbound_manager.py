"""
入库管理核心业务模块
© 2024 贵州睿云慧通科技有限公司
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..models.database import db_manager
from ..models.order import InboundOrder, InboundOrderItem, OrderStatus
from ..models.product import Product, ProductBatch
from ..models.inventory import Location
from ..models.user import User
from ..core.inventory_manager import InventoryManager
from ..utils.logger import get_logger
from ..utils.exceptions import ValidationError, BusinessError
from ..utils.number_generator import generate_order_number

logger = get_logger(__name__)

class InboundManager:
    """入库管理器"""
    
    def __init__(self):
        self.inventory_manager = InventoryManager()
    
    def create_inbound_order(self, order_data: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """
        创建入库单
        
        Args:
            order_data: 入库单数据
            user_id: 创建用户ID
            
        Returns:
            创建的入库单信息
        """
        try:
            with db_manager.get_session() as session:
                # 生成入库单号
                order_number = generate_order_number("IN")
                
                # 创建入库单
                inbound_order = InboundOrder(
                    order_number=order_number,
                    title=order_data.get("title", f"入库单-{order_number}"),
                    type=order_data.get("type", "purchase"),
                    priority=order_data.get("priority", "normal"),
                    user_id=user_id,
                    supplier_id=order_data.get("supplier_id"),
                    warehouse_id=order_data.get("warehouse_id"),
                    reference_number=order_data.get("reference_number"),
                    expected_date=order_data.get("expected_date"),
                    notes=order_data.get("notes")
                )
                
                session.add(inbound_order)
                session.flush()  # 获取ID
                
                # 添加入库单明细
                total_items = 0
                total_quantity = 0
                total_amount = 0
                
                for item_data in order_data.get("items", []):
                    item = InboundOrderItem(
                        inbound_order_id=inbound_order.id,
                        product_id=item_data["product_id"],
                        location_id=item_data.get("location_id"),
                        batch_id=item_data.get("batch_id"),
                        planned_quantity=item_data["planned_quantity"],
                        remaining_quantity=item_data["planned_quantity"],
                        unit_price=item_data.get("unit_price"),
                        notes=item_data.get("notes")
                    )
                    
                    if item.unit_price:
                        item.total_price = item.planned_quantity * item.unit_price
                        total_amount += item.total_price
                    
                    session.add(item)
                    total_items += 1
                    total_quantity += item.planned_quantity
                
                # 更新入库单汇总信息
                inbound_order.total_items = total_items
                inbound_order.total_quantity = total_quantity
                inbound_order.total_amount = total_amount
                inbound_order.status = OrderStatus.PENDING.value
                
                session.commit()
                
                logger.info(f"入库单创建成功: {order_number}")
                
                return inbound_order.to_dict()
                
        except Exception as e:
            logger.error(f"入库单创建失败: {e}")
            raise BusinessError("入库单创建失败")
    
    def scan_product(self, barcode: str) -> Optional[Dict[str, Any]]:
        """
        扫描产品条码
        
        Args:
            barcode: 条码
            
        Returns:
            产品信息字典或None
        """
        try:
            with db_manager.get_session() as session:
                product = session.query(Product).filter(
                    and_(
                        Product.barcode == barcode,
                        Product.status == "active"
                    )
                ).first()
                
                if not product:
                    logger.warning(f"未找到产品: {barcode}")
                    return None
                
                # 获取当前库存信息
                stock_info = self.inventory_manager.get_current_stock(product.id)
                
                result = product.to_dict()
                result["current_stock"] = stock_info
                
                return result
                
        except Exception as e:
            logger.error(f"扫描产品失败: {e}")
            raise BusinessError("扫描产品失败")
    
    def validate_product(self, product_id: int, quantity: int, 
                        location_id: int = None) -> Dict[str, Any]:
        """
        验证产品入库
        
        Args:
            product_id: 产品ID
            quantity: 数量
            location_id: 库位ID
            
        Returns:
            验证结果
        """
        try:
            with db_manager.get_session() as session:
                product = session.query(Product).filter(Product.id == product_id).first()
                if not product:
                    return {"valid": False, "message": "产品不存在"}
                
                if product.status != "active":
                    return {"valid": False, "message": "产品已停用"}
                
                if quantity <= 0:
                    return {"valid": False, "message": "数量必须大于0"}
                
                # 检查库位
                if location_id:
                    location = session.query(Location).filter(Location.id == location_id).first()
                    if not location:
                        return {"valid": False, "message": "库位不存在"}
                    
                    if not location.is_active:
                        return {"valid": False, "message": "库位已停用"}
                    
                    # 检查库位容量
                    if location.capacity and location.capacity > 0:
                        current_stock = self.inventory_manager.get_current_stock(
                            product_id, location_id
                        )
                        if current_stock["total_quantity"] + quantity > location.capacity:
                            return {"valid": False, "message": "超出库位容量"}
                
                return {"valid": True, "message": "验证通过"}
                
        except Exception as e:
            logger.error(f"产品验证失败: {e}")
            return {"valid": False, "message": "验证失败"}
    
    def allocate_location(self, product_id: int, quantity: int, 
                         warehouse_id: int = None) -> Optional[Dict[str, Any]]:
        """
        分配库位
        
        Args:
            product_id: 产品ID
            quantity: 数量
            warehouse_id: 仓库ID
            
        Returns:
            分配的库位信息或None
        """
        try:
            with db_manager.get_session() as session:
                # 查找可用库位
                query = session.query(Location).filter(
                    and_(
                        Location.is_active == True,
                        Location.type == "bin"  # 只分配储位
                    )
                )
                
                if warehouse_id:
                    query = query.filter(Location.warehouse_id == warehouse_id)
                
                locations = query.all()
                
                # 优先选择已有该产品库存的库位
                for location in locations:
                    current_stock = self.inventory_manager.get_current_stock(
                        product_id, location.id
                    )
                    
                    if current_stock["total_quantity"] > 0:
                        # 检查容量
                        if (location.capacity and location.capacity > 0 and
                            current_stock["total_quantity"] + quantity <= location.capacity):
                            return location.to_dict()
                        elif not location.capacity:
                            return location.to_dict()
                
                # 如果没有找到已有库存的库位，选择空库位
                for location in locations:
                    current_stock = self.inventory_manager.get_current_stock(
                        product_id, location.id
                    )
                    
                    if current_stock["total_quantity"] == 0:
                        # 检查容量
                        if (location.capacity and location.capacity > 0 and
                            quantity <= location.capacity):
                            return location.to_dict()
                        elif not location.capacity:
                            return location.to_dict()
                
                logger.warning(f"未找到合适的库位: 产品{product_id}, 数量{quantity}")
                return None
                
        except Exception as e:
            logger.error(f"库位分配失败: {e}")
            return None
    
    def confirm_inbound(self, order_id: int, items: List[Dict[str, Any]], 
                       user_id: int) -> bool:
        """
        确认入库
        
        Args:
            order_id: 入库单ID
            items: 入库明细列表
            user_id: 操作用户ID
            
        Returns:
            是否成功
        """
        try:
            with db_manager.get_session() as session:
                # 获取入库单
                inbound_order = session.query(InboundOrder).filter(
                    InboundOrder.id == order_id
                ).first()
                
                if not inbound_order:
                    raise ValidationError("入库单不存在")
                
                if inbound_order.status == OrderStatus.COMPLETED.value:
                    raise ValidationError("入库单已完成")
                
                # 更新入库单状态
                inbound_order.status = OrderStatus.IN_PROGRESS.value
                inbound_order.actual_date = datetime.now()
                
                # 处理入库明细
                for item_data in items:
                    item_id = item_data["item_id"]
                    received_quantity = item_data["received_quantity"]
                    location_id = item_data["location_id"]
                    batch_id = item_data.get("batch_id")
                    quality_status = item_data.get("quality_status", "qualified")
                    
                    # 获取入库单明细
                    order_item = session.query(InboundOrderItem).filter(
                        InboundOrderItem.id == item_id
                    ).first()
                    
                    if not order_item:
                        continue
                    
                    # 验证数量
                    if received_quantity > order_item.remaining_quantity:
                        raise ValidationError(f"收货数量超出剩余数量")
                    
                    # 更新入库单明细
                    order_item.received_quantity += received_quantity
                    order_item.remaining_quantity -= received_quantity
                    order_item.location_id = location_id
                    order_item.batch_id = batch_id
                    order_item.quality_status = quality_status
                    
                    # 只有合格品才入库
                    if quality_status == "qualified":
                        # 更新库存
                        self.inventory_manager.update_stock(
                            product_id=order_item.product_id,
                            location_id=location_id,
                            quantity=received_quantity,
                            operation="inbound",
                            user_id=user_id,
                            reference_type="inbound_order",
                            reference_id=order_id,
                            batch_id=batch_id,
                            cost_price=order_item.unit_price,
                            reason=f"入库单{inbound_order.order_number}入库"
                        )
                
                # 检查是否全部完成
                remaining_items = session.query(InboundOrderItem).filter(
                    and_(
                        InboundOrderItem.inbound_order_id == order_id,
                        InboundOrderItem.remaining_quantity > 0
                    )
                ).count()
                
                if remaining_items == 0:
                    inbound_order.status = OrderStatus.COMPLETED.value
                    inbound_order.completed_at = datetime.now()
                
                session.commit()
                
                logger.info(f"入库确认成功: {inbound_order.order_number}")
                return True
                
        except Exception as e:
            logger.error(f"入库确认失败: {e}")
            raise
    
    def get_inbound_orders(self, user_id: int = None, status: str = None, 
                          start_date: datetime = None, end_date: datetime = None,
                          page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取入库单列表
        
        Args:
            user_id: 用户ID
            status: 状态
            start_date: 开始日期
            end_date: 结束日期
            page: 页码
            page_size: 页大小
            
        Returns:
            入库单列表和分页信息
        """
        try:
            with db_manager.get_session() as session:
                query = session.query(InboundOrder)
                
                # 添加过滤条件
                if user_id:
                    query = query.filter(InboundOrder.user_id == user_id)
                
                if status:
                    query = query.filter(InboundOrder.status == status)
                
                if start_date:
                    query = query.filter(InboundOrder.created_at >= start_date)
                
                if end_date:
                    query = query.filter(InboundOrder.created_at <= end_date)
                
                # 计算总数
                total = query.count()
                
                # 分页查询
                orders = query.order_by(InboundOrder.created_at.desc()).offset(
                    (page - 1) * page_size
                ).limit(page_size).all()
                
                return {
                    "orders": [order.to_dict() for order in orders],
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total + page_size - 1) // page_size
                }
                
        except Exception as e:
            logger.error(f"获取入库单列表失败: {e}")
            raise BusinessError("获取入库单列表失败")
