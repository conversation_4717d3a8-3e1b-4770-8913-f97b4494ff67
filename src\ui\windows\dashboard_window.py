"""
WMS客户端系统 - 4卡片主窗口
企业风格：正式、简约、大气
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QFrame, QApplication, QLabel, QMessageBox, QPushButton, QDialog,
    QLineEdit
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize, QTimer
from PyQt6.QtGui import QFont, QPalette, QIcon

from ..components.base_components import (
    BaseCard, TitleLabel, BodyLabel, PrimaryButton, SecondaryButton
)
from ..styles.theme import theme
from ..utils.window_manager import window_manager

# 导入安全模块
try:
    from ...security.auth_manager import auth_manager
    from ...security.license_manager import license_manager
    from ..dialogs.license_dialog import LicenseDialog
except ImportError:
    # 如果导入失败，使用备用方案
    auth_manager = None
    license_manager = None
    LicenseDialog = None


class AdminPasswordDialog(QDialog):
    """管理员密码验证对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("系统管理 - 管理员验证")

        # 设置对话框大小 - 黄金比例优化
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()

        # 使用黄金比例 (1:1.618) 计算对话框尺寸
        self.golden_ratio = 1.618
        base_width = max(420, min(520, int(screen_geometry.width() * 0.32)))
        self.dialog_width = base_width
        self.dialog_height = int(base_width / self.golden_ratio)  # 黄金比例高度

        # 确保最小尺寸
        self.dialog_height = max(260, self.dialog_height)

        self.setFixedSize(self.dialog_width, self.dialog_height)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowStaysOnTopHint)
        self.setModal(True)

        # 管理员密码: admin123
        self.admin_password = "admin123"

        self._setup_ui()

    def _setup_ui(self):
        """设置UI - 黄金比例优化布局"""
        layout = QVBoxLayout(self)

        # 使用黄金比例计算间距和边距
        base_spacing = int(self.dialog_width / (self.golden_ratio * 18))  # 基础间距
        layout.setSpacing(base_spacing)

        # 边距使用黄金比例
        margin = int(self.dialog_width / (self.golden_ratio * 10))
        layout.setContentsMargins(margin, margin, margin, margin)

        # 标题容器 - 黄金比例间距
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_spacing = int(base_spacing / self.golden_ratio)  # 更小的间距
        title_layout.setSpacing(title_spacing)
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 图标 - 黄金比例字体大小
        icon_size = int(self.dialog_width / (self.golden_ratio * 9))  # 基于对话框宽度计算
        icon_label = QLabel("🔐")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: {icon_size}px;
                background-color: transparent;
                padding: {int(icon_size/10)}px;
                margin-bottom: {int(icon_size/12)}px;
            }}
        """)
        title_layout.addWidget(icon_label)

        # 标题 - 黄金比例字体大小
        title_size = int(icon_size / self.golden_ratio)  # 标题字体比图标小
        title_label = QLabel("管理员权限验证")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: #dc2626;
                font-size: {title_size}px;
                font-weight: 700;
                font-family: "Microsoft YaHei UI", sans-serif;
                background-color: transparent;
                margin-bottom: {int(title_size/4)}px;
                letter-spacing: 1px;
            }}
        """)
        title_layout.addWidget(title_label)

        # 说明文字 - 黄金比例字体大小
        info_size = int(title_size / self.golden_ratio)  # 比标题更小
        info_label = QLabel("请输入管理员密码以继续操作\n默认密码：admin123")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet(f"""
            QLabel {{
                color: #64748b;
                font-size: {info_size}px;
                font-family: "Microsoft YaHei UI", sans-serif;
                background-color: transparent;
                margin-bottom: {int(info_size * 0.8)}px;
                line-height: 1.6;
            }}
        """)
        title_layout.addWidget(info_label)

        layout.addWidget(title_container)

        # 密码输入 - 黄金比例优化
        input_height = int(self.dialog_height / (self.golden_ratio * 5))  # 基于对话框高度计算
        input_font_size = int(input_height / 3.2)  # 字体大小与输入框高度成比例
        input_padding = int(input_height / 4)  # 内边距
        input_radius = int(input_height / 4)  # 圆角半径
        border_width = max(2, int(self.dialog_width / 150))  # 边框宽度

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入管理员密码")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setFixedHeight(input_height)
        self.password_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: #f8fafc;
                border: {border_width}px solid #e2e8f0;
                border-radius: {input_radius}px;
                padding: {input_padding}px {int(input_padding * 1.3)}px;
                font-size: {input_font_size}px;
                font-family: "Microsoft YaHei UI", sans-serif;
                color: #1e293b;
                font-weight: 500;
            }}
            QLineEdit:focus {{
                border-color: #dc2626;
                background-color: white;
                border-width: 3px;
            }}
            QLineEdit::placeholder {{
                color: #94a3b8;
            }}
        """)
        self.password_input.returnPressed.connect(self._verify_password)
        layout.addWidget(self.password_input)

        # 按钮 - 黄金比例优化
        button_layout = QHBoxLayout()
        button_spacing = int(self.dialog_width / (self.golden_ratio * 12))  # 按钮间距
        button_layout.setSpacing(button_spacing)

        # 按钮尺寸计算
        button_height = int(input_height * 0.85)  # 比输入框稍小
        button_width = int(self.dialog_width / (self.golden_ratio * 2.8))  # 按钮宽度
        button_font_size = int(button_height / 3)  # 按钮字体大小
        button_radius = int(button_height / 4.5)  # 按钮圆角
        button_padding = int(button_height / 6)  # 按钮内边距

        cancel_btn = QPushButton("取消")
        cancel_btn.setFixedSize(button_width, button_height)
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6b7280, stop:1 #4b5563);
                color: white;
                border: none;
                border-radius: {button_radius}px;
                padding: {button_padding}px;
                font-size: {button_font_size}px;
                font-weight: 600;
                font-family: "Microsoft YaHei UI", sans-serif;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4b5563, stop:1 #374151);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #374151, stop:1 #1f2937);
            }}
        """)
        cancel_btn.clicked.connect(self.reject)

        verify_btn = QPushButton("验证")
        verify_btn.setFixedSize(button_width, button_height)
        verify_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626, stop:1 #b91c1c);
                color: white;
                border: none;
                border-radius: {button_radius}px;
                padding: {button_padding}px;
                font-size: {button_font_size}px;
                font-weight: 600;
                font-family: "Microsoft YaHei UI", sans-serif;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #b91c1c, stop:1 #991b1b);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #991b1b, stop:1 #7f1d1d);
            }}
        """)
        verify_btn.clicked.connect(self._verify_password)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(verify_btn)
        layout.addLayout(button_layout)

        # 对话框样式 - 黄金比例优化
        dialog_radius = int(self.dialog_width / (self.golden_ratio * 18))  # 对话框圆角
        border_width = max(2, int(self.dialog_width / 140))  # 边框宽度

        self.setStyleSheet(f"""
            AdminPasswordDialog {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(254, 242, 242, 0.98),
                    stop:0.3 rgba(255, 255, 255, 0.95),
                    stop:0.7 rgba(254, 202, 202, 0.92),
                    stop:1 rgba(252, 165, 165, 0.88));
                border-radius: {dialog_radius}px;
                border: {border_width}px solid #dc2626;
            }}
        """)

        # 设置焦点
        self.password_input.setFocus()

    def _verify_password(self):
        """验证密码"""
        password = self.password_input.text().strip()
        if password == self.admin_password:
            self.accept()
        else:
            self._show_error("密码错误，请重试")
            self.password_input.clear()
            self.password_input.setFocus()

    def _show_error(self, message):
        """显示错误信息 - 黄金比例优化"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("验证失败")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Warning)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

        # 使用黄金比例计算消息框样式
        msg_font_size = int(self.dialog_width / (self.golden_ratio * 22))  # 消息框字体大小
        btn_height = int(self.dialog_height / (self.golden_ratio * 6))  # 按钮高度
        btn_width = int(self.dialog_width / (self.golden_ratio * 4))  # 按钮宽度
        btn_radius = int(btn_height / 4)  # 按钮圆角

        # 优化消息框样式
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: white;
                border-radius: {int(self.dialog_width / (self.golden_ratio * 25))}px;
                font-size: {msg_font_size}px;
                font-family: "Microsoft YaHei UI", sans-serif;
            }}
            QMessageBox QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626, stop:1 #b91c1c);
                color: white;
                border: none;
                border-radius: {btn_radius}px;
                padding: {int(btn_height/6)}px {int(btn_width/8)}px;
                font-size: {int(msg_font_size * 0.9)}px;
                font-weight: 600;
                font-family: "Microsoft YaHei UI", sans-serif;
                min-width: {btn_width}px;
                min-height: {btn_height}px;
            }}
            QMessageBox QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #b91c1c, stop:1 #991b1b);
            }}
            QMessageBox QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #991b1b, stop:1 #7f1d1d);
            }}
        """)

        msg_box.exec()


class FunctionCard(BaseCard):
    """功能卡片组件"""
    
    # 信号定义
    card_clicked = pyqtSignal(str)  # 卡片点击信号
    
    def __init__(self, icon: str, title: str, description: str,
                 color: str, card_id: str, parent=None):
        # 先设置属性，再调用父类初始化
        self.card_id = card_id
        self.color = color
        super().__init__(parent)
        self._setup_ui(icon, title, description)
        self._setup_style()
        self._setup_events()
    
    def _setup_ui(self, icon: str, title: str, description: str):
        """设置UI - 标题在上，图标在中，描述在图标垂直下方"""
        # 设置卡片大小 - 适中尺寸，确保文字显示完整
        self.setFixedSize(380, 280)

        # 主布局
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # 改为顶部对齐，便于控制间距
        layout.setSpacing(0)  # 设为0，手动控制间距
        layout.setContentsMargins(25, 20, 25, 20)

        # 标题 - 放在最上方（大文字）
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setWordWrap(True)  # 允许换行
        title_label.setFixedHeight(40)  # 固定高度
        title_label.setStyleSheet("""
        QLabel {
            color: #1e293b;
            font-weight: 800;
            font-size: 20px;
            font-family: "Microsoft YaHei UI", "PingFang SC", sans-serif;
            background-color: transparent;
            border: none;
            letter-spacing: 1px;
        }
        """)
        layout.addWidget(title_label)

        # 标题和图标之间的间距
        layout.addSpacing(15)

        # 图标容器 - 放在中间
        icon_container = QWidget()
        icon_container.setFixedSize(110, 110)
        icon_container.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.15),
                    stop:0.5 rgba(59, 130, 246, 0.1),
                    stop:1 rgba(59, 130, 246, 0.08));
                border-radius: 55px;
                border: 3px solid rgba(59, 130, 246, 0.25);
            }}
        """)

        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)

        # 图标
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet(f"""
        QLabel {{
            font-size: 55px;
            color: {self.color};
            background-color: transparent;
            border: none;
            font-weight: normal;
        }}
        """)
        icon_layout.addWidget(icon_label)
        layout.addWidget(icon_container, 0, Qt.AlignmentFlag.AlignHCenter)

        # 图标和描述之间的间距 - 让描述更靠近图标底部
        layout.addSpacing(25)

        # 描述文字 - 放在图标垂直正下方（绿色框范围内，但不显示框）
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setWordWrap(True)  # 允许换行
        desc_label.setFixedSize(240, 40)  # 固定尺寸，对应绿色框的范围
        desc_label.setStyleSheet("""
        QLabel {
            color: #64748b;
            font-size: 13px;
            font-weight: 500;
            font-family: "Microsoft YaHei UI", "PingFang SC", sans-serif;
            line-height: 1.4;
            background-color: transparent;
            border: none;
            letter-spacing: 0.5px;
            padding: 8px 12px;
        }
        """)
        layout.addWidget(desc_label, 0, Qt.AlignmentFlag.AlignHCenter)

        # 添加弹性空间，确保布局稳定
        layout.addStretch()

        self.add_layout(layout)
    
    def _setup_style(self):
        """设置样式 - 美化卡片，区分功能模块"""
        # 获取颜色，如果没有则使用默认颜色
        color = getattr(self, 'color', '#3b82f6')

        # 根据卡片ID设置不同的背景色和图案
        card_id = getattr(self, 'card_id', 'default')

        # 定义每个功能模块的主题色和背景
        themes = {
            'inbound': {
                'bg_start': 'rgba(16, 185, 129, 0.15)',  # 绿色系 - 入库
                'bg_end': 'rgba(16, 185, 129, 0.08)',
                'border': 'rgba(16, 185, 129, 0.3)',
                'hover_bg': 'rgba(16, 185, 129, 0.25)',
                'pattern': '📦'
            },
            'outbound': {
                'bg_start': 'rgba(245, 158, 11, 0.15)',  # 橙色系 - 出库
                'bg_end': 'rgba(245, 158, 11, 0.08)',
                'border': 'rgba(245, 158, 11, 0.3)',
                'hover_bg': 'rgba(245, 158, 11, 0.25)',
                'pattern': '📤'
            },
            'query': {
                'bg_start': 'rgba(59, 130, 246, 0.15)',  # 蓝色系 - 查询
                'bg_end': 'rgba(59, 130, 246, 0.08)',
                'border': 'rgba(59, 130, 246, 0.3)',
                'hover_bg': 'rgba(59, 130, 246, 0.25)',
                'pattern': '🔍'
            },
            'admin': {
                'bg_start': 'rgba(139, 92, 246, 0.15)',  # 紫色系 - 管理
                'bg_end': 'rgba(139, 92, 246, 0.08)',
                'border': 'rgba(139, 92, 246, 0.3)',
                'hover_bg': 'rgba(139, 92, 246, 0.25)',
                'pattern': '⚙️'
            }
        }

        card_theme = themes.get(card_id, themes['query'])  # 默认使用查询主题

        self.setStyleSheet(f"""
        FunctionCard {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {card_theme['bg_start']},
                stop:0.5 rgba(255, 255, 255, 0.95),
                stop:1 {card_theme['bg_end']});
            border: 3px solid {card_theme['border']};
            border-radius: 24px;
            padding: 25px;
        }}
        FunctionCard:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {card_theme['hover_bg']},
                stop:0.5 rgba(255, 255, 255, 0.98),
                stop:1 {card_theme['bg_start']});
            border: 4px solid {color};
        }}
        FunctionCard:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {card_theme['bg_end']},
                stop:0.5 rgba(248, 250, 252, 0.95),
                stop:1 {card_theme['bg_start']});
        }}
        """)
        
        # 设置鼠标悬停效果
        self.setCursor(Qt.CursorShape.PointingHandCursor)
    
    def _setup_events(self):
        """设置事件"""
        # 使卡片可点击
        self.mousePressEvent = self._on_click
    
    def _on_click(self, event):
        """卡片点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.card_clicked.emit(self.card_id)


# 移除复杂的管理员登录对话框，使用简单的输入对话框


class DashboardWindow(QMainWindow):
    """4卡片主窗口"""
    
    # 信号定义
    admin_login_success = pyqtSignal(dict)  # 管理员登录成功信号
    
    def __init__(self):
        super().__init__()
        self._setup_ui()
        self._setup_style()
        self._setup_connections()
        self._start_hardware_monitoring()  # 启动硬件状态监控
    
    def _setup_ui(self):
        """设置UI"""
        self.setWindowTitle("WMS库房自助出入库客户端系统")

        # 获取屏幕信息并设置全屏
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()

        print(f"📺 屏幕分辨率: {screen_geometry.width()} x {screen_geometry.height()}")

        # 设置窗口几何，但不立即显示
        self.setGeometry(screen_geometry)

        # 设置窗口标志 - 无边框，置顶
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 - 紧凑间距确保底部区域可见
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(30, 20, 30, 20)
        main_layout.setSpacing(20)
        
        # 顶部工具栏 - 固定高度
        self._create_toolbar(main_layout)

        # 功能卡片区域 - 设置合理的拉伸因子
        self._create_function_cards(main_layout)

        # 底部信息区域 - 确保始终可见
        self._create_footer(main_layout)

    def _create_toolbar(self, main_layout):
        """创建顶部工具栏"""
        toolbar = QWidget()
        toolbar.setFixedHeight(50)
        toolbar.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.95);
                border-bottom: 1px solid #e2e8f0;
            }
        """)

        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(20, 10, 20, 10)

        # 左侧标题
        title_label = QLabel("WMS库房自助出入库客户端系统")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {theme.colors.PRIMARY};
                font-size: 20px;
                font-weight: 700;
                font-family: "Microsoft YaHei UI", "PingFang SC", sans-serif;
                background-color: transparent;
                letter-spacing: 1px;
            }}
        """)
        toolbar_layout.addWidget(title_label)

        toolbar_layout.addStretch()

        # 右侧退出按钮
        exit_btn = QPushButton("退出系统")
        exit_btn.setFixedSize(110, 45)
        exit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626, stop:1 #b91c1c);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 15px;
                font-weight: 600;
                font-family: "Microsoft YaHei UI", sans-serif;
                letter-spacing: 0.5px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #b91c1c, stop:1 #991b1b);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #991b1b, stop:1 #7f1d1d);
            }
        """)
        exit_btn.clicked.connect(self.close_with_admin_verification)
        toolbar_layout.addWidget(exit_btn)

        main_layout.addWidget(toolbar)


    
    def _create_function_cards(self, main_layout):
        """创建功能卡片区域"""
        # 卡片容器 - 优化布局间距
        cards_widget = QWidget()
        cards_layout = QGridLayout(cards_widget)
        cards_layout.setSpacing(25)  # 适中间距，确保底部可见
        cards_layout.setContentsMargins(40, 25, 40, 25)  # 紧凑边距
        
        # 四个功能卡片数据 - 恢复原始简洁描述
        cards_data = [
            ("📦", "快速入库", "扫码识别 · 快速录入",
             "#10b981", "inbound", 0, 0),
            ("📤", "快速出库", "智能拣选 · 快速出库",
             "#f59e0b", "outbound", 0, 1),
            ("🔍", "智能查询", "实时查询 · 精准定位",
             "#3b82f6", "query", 1, 0),
            ("⚙️", "系统管理", "权限管理 · 系统配置",
             "#8b5cf6", "admin", 1, 1)
        ]
        
        # 创建卡片
        self.function_cards = {}
        for icon, title, description, color, card_id, row, col in cards_data:
            card = FunctionCard(icon, title, description, color, card_id)
            self.function_cards[card_id] = card
            cards_layout.addWidget(card, row, col, Qt.AlignmentFlag.AlignCenter)
        
        # 设置网格权重
        cards_layout.setRowStretch(0, 1)
        cards_layout.setRowStretch(1, 1)
        cards_layout.setColumnStretch(0, 1)
        cards_layout.setColumnStretch(1, 1)
        
        main_layout.addWidget(cards_widget)
    
    def _create_footer(self, main_layout):
        """创建底部信息区域"""
        footer_widget = QWidget()
        footer_widget.setFixedHeight(80)  # 适中高度，确保显示
        footer_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.98);
                border-top: 2px solid #e2e8f0;
                border-radius: 0px;
            }
        """)
        footer_layout = QHBoxLayout(footer_widget)
        footer_layout.setContentsMargins(30, 15, 30, 15)

        # 左侧版权信息
        copyright_layout = QVBoxLayout()
        copyright_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        copyright_layout.setSpacing(5)

        # 版权信息
        copyright_label = QLabel("© 2024 贵州睿云慧通科技有限公司")
        copyright_label.setStyleSheet("""
            QLabel {
                color: #64748b;
                font-size: 14px;
                font-weight: 500;
                background-color: transparent;
            }
        """)
        copyright_layout.addWidget(copyright_label)

        # 版权声明
        rights_label = QLabel("版权所有 · 保留所有权利")
        rights_label.setStyleSheet("""
            QLabel {
                color: #94a3b8;
                font-size: 12px;
                background-color: transparent;
            }
        """)
        copyright_layout.addWidget(rights_label)

        footer_layout.addLayout(copyright_layout)
        footer_layout.addStretch()

        # 右侧状态监控区域
        self._create_status_area(footer_layout)

        main_layout.addWidget(footer_widget)

    def _create_status_area(self, footer_layout):
        """创建硬件设备状态监控区域"""
        status_widget = QWidget()
        status_widget.setStyleSheet("""
            QWidget {
                background-color: transparent;
            }
        """)
        status_layout = QHBoxLayout(status_widget)
        status_layout.setSpacing(12)
        status_layout.setContentsMargins(0, 0, 0, 0)

        # 硬件设备状态数据 - 完整的设备监控
        self.hardware_status = {
            "network": {"icon": "🌐", "name": "网络", "status": "正常", "online": True},
            "interface": {"icon": "🔌", "name": "接口", "status": "正常", "online": True},
            "camera": {"icon": "📷", "name": "摄像头", "status": "在线", "online": True},
            "printer": {"icon": "🖨️", "name": "小票打印机", "status": "就绪", "online": True},
            "card_machine": {"icon": "💳", "name": "发卡机", "status": "正常", "online": True},
            "barcode_scanner": {"icon": "📊", "name": "条形码扫码枪", "status": "就绪", "online": True},
            "qr_scanner": {"icon": "📱", "name": "二维码扫码枪", "status": "就绪", "online": True}
        }

        self.status_widgets = {}  # 存储状态组件引用

        for device_id, device_info in self.hardware_status.items():
            color = "#10b981" if device_info["online"] else "#9ca3af"  # 绿色在线，灰色故障
            status_item = self._create_status_item(
                device_info["icon"],
                device_info["name"],
                device_info["status"],
                color,
                device_id
            )
            self.status_widgets[device_id] = status_item
            status_layout.addWidget(status_item)

        footer_layout.addWidget(status_widget)

    def _create_status_item(self, icon, name, status, color, device_id):
        """创建单个硬件设备状态项"""
        item_widget = QPushButton()
        item_widget.setFixedSize(85, 50)  # 调整尺寸适应底部栏
        item_widget.setCursor(Qt.CursorShape.PointingHandCursor)
        item_widget.device_id = device_id  # 存储设备ID

        # 根据在线状态设置样式
        is_online = color == "#10b981"  # 绿色表示正常，灰色表示故障

        # 状态指示器颜色
        status_color = "#10b981" if is_online else "#ef4444"  # 绿色正常，红色故障
        bg_color = "rgba(255, 255, 255, 0.8)" if is_online else "rgba(255, 255, 255, 0.6)"

        item_widget.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {status_color};
                border-radius: 8px;
                padding: 4px;
                text-align: center;
                color: {'#1e293b' if is_online else '#6b7280'};
                font-size: 9px;
                font-weight: {'600' if is_online else '500'};
                font-family: "Microsoft YaHei UI", sans-serif;
            }}
            QPushButton:hover {{
                background-color: {'rgba(16, 185, 129, 0.1)' if is_online else 'rgba(239, 68, 68, 0.1)'};
                border-color: {status_color};
            }}
            QPushButton:pressed {{
                background-color: {'rgba(16, 185, 129, 0.2)' if is_online else 'rgba(239, 68, 68, 0.2)'};
            }}
        """)

        # 设置文本内容，包含状态指示器
        status_indicator = "●" if is_online else "●"  # 圆点状态指示器
        # 使用更紧凑的布局
        item_widget.setText(f"{icon} {name}\n{status_indicator} {status}")

        # 连接点击事件
        item_widget.clicked.connect(lambda checked, n=name, s=status, c=color, d=device_id:
                                  self._show_hardware_details(n, s, c, d))

        return item_widget

    def _show_hardware_details(self, name, status, color, device_id):
        """显示硬件设备详情"""
        details_dialog = QDialog(self)
        details_dialog.setWindowTitle(f"{name} - 设备状态详情")
        details_dialog.setFixedSize(450, 350)
        details_dialog.setModal(True)

        layout = QVBoxLayout(details_dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title_label = QLabel(f"🔧 {name}设备状态")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
            }}
        """)
        layout.addWidget(title_label)

        # 硬件设备详细信息
        device_details = {
            "网络": {"IP地址": "*************", "延迟": "12ms", "带宽": "100Mbps", "状态": "连接正常"},
            "接口": {"USB端口": "8个可用", "串口": "COM1-COM4", "网口": "千兆以太网", "状态": "全部正常"},
            "摄像头": {"分辨率": "1920x1080", "帧率": "30fps", "编码": "H.264", "状态": "实时监控中"},
            "小票打印机": {"型号": "Epson TM-T88VI", "纸张": "充足", "墨盒": "85%", "状态": "就绪"},
            "发卡机": {"型号": "ACR122U", "卡片余量": "156张", "固件": "v2.1", "状态": "正常工作"},
            "条形码扫码枪": {"型号": "Honeywell 1900", "扫描速度": "60次/秒", "精度": "99.9%", "状态": "就绪"},
            "二维码扫码枪": {"型号": "Zebra DS2208", "扫描距离": "0-61cm", "解码能力": "全格式", "状态": "就绪"}
        }

        info = device_details.get(name, {})
        for key, value in info.items():
            info_layout = QHBoxLayout()

            key_label = QLabel(f"{key}:")
            key_label.setStyleSheet("color: #64748b; font-weight: 500;")
            key_label.setFixedWidth(80)

            value_label = QLabel(value)
            value_label.setStyleSheet("color: #1e293b; font-weight: 600;")

            info_layout.addWidget(key_label)
            info_layout.addWidget(value_label)
            info_layout.addStretch()

            layout.addLayout(info_layout)

        layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.setFixedHeight(35)
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                opacity: 0.9;
            }}
        """)
        close_btn.clicked.connect(details_dialog.accept)
        layout.addWidget(close_btn)

        details_dialog.exec()

    def _start_hardware_monitoring(self):
        """启动硬件状态监控"""
        # 创建定时器定期检查硬件状态
        self.hardware_timer = QTimer()
        self.hardware_timer.timeout.connect(self._check_hardware_status)
        self.hardware_timer.start(5000)  # 每5秒检查一次

    def _check_hardware_status(self):
        """检查硬件状态（模拟）"""
        import random

        # 模拟硬件状态变化
        devices = list(self.hardware_status.keys())

        # 随机选择一个设备进行状态更新（模拟真实环境）
        if random.random() < 0.1:  # 10%概率发生状态变化
            device = random.choice(devices)
            current_status = self.hardware_status[device]

            # 模拟状态切换
            if current_status["online"]:
                # 小概率变为故障
                if random.random() < 0.05:  # 5%概率故障
                    self.update_hardware_status(device, False, "故障")
            else:
                # 故障设备有较高概率恢复
                if random.random() < 0.3:  # 30%概率恢复
                    self.update_hardware_status(device, True, "正常")

    def _setup_style(self):
        """设置样式 - 与登录窗口呼应的渐变背景"""
        self.setStyleSheet("""
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #1e40af, stop:0.5 #2563eb, stop:1 #3b82f6);
        }
        """)
    
    def _setup_connections(self):
        """设置信号连接"""
        # 连接卡片点击事件
        for card in self.function_cards.values():
            card.card_clicked.connect(self._on_card_clicked)
    
    def _on_card_clicked(self, card_id: str):
        """卡片点击事件处理"""
        print(f"点击了卡片: {card_id}")
        
        if card_id == "admin":
            self._handle_admin_card()
        elif card_id == "inbound":
            self._handle_inbound_card()
        elif card_id == "outbound":
            self._handle_outbound_card()
        elif card_id == "query":
            self._handle_query_card()
    
    def _handle_admin_card(self):
        """处理系统管理卡片点击 - 需要管理员认证"""
        # 显示管理员认证对话框
        dialog = AdminPasswordDialog(self)
        dialog.setWindowTitle("系统管理 - 管理员验证")

        # 修改对话框说明文字
        for child in dialog.findChildren(QLabel):
            if "退出系统需要管理员权限验证" in child.text():
                child.setText("进入系统管理需要管理员权限验证")
                break

        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 管理员验证成功，发送信号
            admin_info = {
                "username": "admin",
                "role": "admin",
                "name": "系统管理员"
            }
            QMessageBox.information(self, "验证成功",
                                  "管理员验证成功！\n正在进入系统管理...")
            self.admin_login_success.emit(admin_info)
        else:
            # 验证失败，保持在当前界面
            print("❌ 管理员验证失败，取消进入系统管理")
    
    def _verify_admin_credentials(self, username: str, password: str) -> bool:
        """验证管理员凭据"""
        valid_admins = {
            "admin": "123456",
            "manager": "admin123",
            "root": "root123"
        }
        return username in valid_admins and valid_admins[username] == password
    
    def _handle_inbound_card(self):
        """处理快速入库卡片点击"""
        from .inbound_window import InboundWindow

        # 创建或获取入库窗口
        if not hasattr(self, 'inbound_window') or not self.inbound_window:
            self.inbound_window = InboundWindow()
            self.inbound_window.back_to_dashboard.connect(self._show_dashboard)

        # 使用窗口管理器进行流畅切换
        window_manager.switch_to_window("inbound", self.inbound_window, use_animation=True)

    def _handle_outbound_card(self):
        """处理快速出库卡片点击"""
        from .outbound_window import OutboundWindow

        # 创建或获取出库窗口
        if not hasattr(self, 'outbound_window') or not self.outbound_window:
            self.outbound_window = OutboundWindow()
            self.outbound_window.back_to_dashboard.connect(self._show_dashboard)

        # 使用窗口管理器进行流畅切换
        window_manager.switch_to_window("outbound", self.outbound_window, use_animation=True)

    def _handle_query_card(self):
        """处理快速查询卡片点击"""
        from .query_window import QueryWindow

        # 创建或获取查询窗口
        if not hasattr(self, 'query_window') or not self.query_window:
            self.query_window = QueryWindow()
            self.query_window.back_to_dashboard.connect(self._show_dashboard)

        # 使用窗口管理器进行流畅切换
        window_manager.switch_to_window("query", self.query_window, use_animation=True)

    def _show_dashboard(self):
        """显示主界面"""
        # 隐藏所有子窗口
        if hasattr(self, 'inbound_window') and self.inbound_window:
            self.inbound_window.hide()
        if hasattr(self, 'outbound_window') and self.outbound_window:
            self.outbound_window.hide()
        if hasattr(self, 'query_window') and self.query_window:
            self.query_window.hide()

        # 显示4卡片主界面
        self.show()
        self.raise_()
        self.activateWindow()

        print("🏠 已返回4卡片主界面")

    def update_hardware_status(self, device_id: str, online: bool, status: str = None):
        """更新硬件状态"""
        if device_id in self.hardware_status:
            self.hardware_status[device_id]["online"] = online
            if status:
                self.hardware_status[device_id]["status"] = status

            # 更新UI
            if device_id in self.status_widgets:
                device_info = self.hardware_status[device_id]
                color = "#10b981" if online else "#9ca3af"
                is_online = online

                # 更新样式
                widget = self.status_widgets[device_id]
                widget.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 rgba(255, 255, 255, 0.9),
                            stop:1 rgba(248, 250, 252, 0.8));
                        border: 2px solid {color};
                        border-radius: 12px;
                        padding: 8px;
                        text-align: center;
                        color: {'#1e293b' if is_online else '#6b7280'};
                        font-size: 11px;
                        font-weight: {'600' if is_online else '400'};
                        line-height: 1.3;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 rgba(255, 255, 255, 1.0),
                            stop:1 rgba(239, 246, 255, 0.9));
                        border-color: {color};
                    }}
                """)

                # 更新文本
                status_text = device_info["status"] if online else f"❌ {device_info['status']}"
                widget.setText(f"{device_info['icon']}\n{device_info['name']}\n{status_text}")



    def closeEvent(self, event):
        """重写关闭事件"""
        # 程序正常退出时不需要验证，只有用户点击关闭按钮时才需要
        event.accept()  # 直接接受关闭事件，不弹出管理员验证

    def close_with_admin_verification(self):
        """需要管理员验证的关闭方法"""
        # 只有用户主动点击退出按钮时才调用此方法
        dialog = AdminPasswordDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            print("🔐 管理员验证成功，正在退出系统...")
            QApplication.quit()
        else:
            print("❌ 管理员验证失败，取消退出操作")


if __name__ == "__main__":
    app = QApplication([])
    
    # 设置应用程序样式
    app.setStyle("Fusion")
    
    window = DashboardWindow()
    window.show()
    
    app.exec()
