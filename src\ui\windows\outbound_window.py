"""
WMS客户端系统 - 快速出库窗口
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QFrame, QApplication, QLabel, QMessageBox, QPushButton, QDialog,
    QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QSpinBox, QTextEdit, QGroupBox, QFormLayout
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize, QTimer
from PyQt6.QtGui import QFont, QPalette, QIcon

from ..components.base_components import (
    BaseCard, TitleLabel, BodyLabel, PrimaryButton, SecondaryButton
)
from ..styles.theme import theme


class OutboundWindow(QMainWindow):
    """快速出库窗口"""
    
    # 信号定义
    back_to_dashboard = pyqtSignal()  # 返回主界面信号
    
    def __init__(self):
        super().__init__()
        self._setup_ui()
        self._setup_style()
        self._setup_connections()
    
    def _setup_ui(self):
        """设置UI - 满屏效果"""
        self.setWindowTitle("WMS库房自助出入库客户端系统 - 快速出库")
        
        # 获取屏幕信息并设置全屏
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        
        # 设置窗口为全屏
        self.setGeometry(screen_geometry)
        self.showFullScreen()
        
        # 设置窗口标志 - 无边框，置顶
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 设置背景样式
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:0.5 #2563eb, stop:1 #3b82f6);
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(30, 20, 30, 20)
        main_layout.setSpacing(20)
        
        # 顶部工具栏
        self._create_toolbar(main_layout)
        
        # 内容区域
        self._create_content_area(main_layout)
        
        # 底部状态栏
        self._create_status_bar(main_layout)
    
    def _create_toolbar(self, main_layout):
        """创建顶部工具栏"""
        toolbar = QWidget()
        toolbar.setFixedHeight(60)
        toolbar.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.95);
                border-bottom: 1px solid #e2e8f0;
                border-radius: 12px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(20, 10, 20, 10)
        
        # 返回按钮
        back_btn = QPushButton("← 返回主界面")
        back_btn.setFixedSize(120, 40)
        back_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6, stop:1 #2563eb);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563eb, stop:1 #1d4ed8);
            }
        """)
        back_btn.clicked.connect(self.back_to_dashboard.emit)
        toolbar_layout.addWidget(back_btn)
        
        # 标题
        title_label = QLabel("📤 快速出库")
        title_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                font-size: 24px;
                font-weight: 700;
                font-family: "Microsoft YaHei UI", sans-serif;
                background-color: transparent;
                margin-left: 20px;
            }
        """)
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # 扫码按钮
        scan_btn = QPushButton("📷 扫码出库")
        scan_btn.setFixedSize(120, 40)
        scan_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b, stop:1 #d97706);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d97706, stop:1 #b45309);
            }
        """)
        scan_btn.clicked.connect(self._start_scanning)
        toolbar_layout.addWidget(scan_btn)
        
        main_layout.addWidget(toolbar)
    
    def _create_content_area(self, main_layout):
        """创建内容区域"""
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:1 rgba(248, 250, 252, 0.95));
                border-radius: 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)
        
        content_layout = QHBoxLayout(content_widget)
        content_layout.setContentsMargins(30, 30, 30, 30)
        content_layout.setSpacing(30)
        
        # 左侧：商品信息输入
        self._create_product_input_area(content_layout)
        
        # 右侧：出库列表
        self._create_outbound_list_area(content_layout)
        
        main_layout.addWidget(content_widget)
    
    def _create_product_input_area(self, content_layout):
        """创建商品信息输入区域"""
        input_group = QGroupBox("商品信息")
        input_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: 600;
                color: #1e293b;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        form_layout = QFormLayout(input_group)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(20, 20, 20, 20)
        
        # 商品编码
        self.product_code_input = QLineEdit()
        self.product_code_input.setPlaceholderText("请输入或扫描商品编码")
        self.product_code_input.setFixedHeight(40)
        self._style_input(self.product_code_input)
        form_layout.addRow("商品编码:", self.product_code_input)
        
        # 商品名称
        self.product_name_input = QLineEdit()
        self.product_name_input.setPlaceholderText("商品名称（自动填充）")
        self.product_name_input.setFixedHeight(40)
        self.product_name_input.setReadOnly(True)
        self._style_input(self.product_name_input)
        form_layout.addRow("商品名称:", self.product_name_input)
        
        # 库存数量显示
        self.stock_label = QLabel("库存数量: --")
        self.stock_label.setStyleSheet("""
            QLabel {
                color: #059669;
                font-size: 14px;
                font-weight: 600;
                background-color: #f0fdf4;
                border: 1px solid #bbf7d0;
                border-radius: 6px;
                padding: 8px 12px;
            }
        """)
        form_layout.addRow("", self.stock_label)
        
        # 出库数量
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(1, 9999)
        self.quantity_input.setValue(1)
        self.quantity_input.setFixedHeight(40)
        self._style_input(self.quantity_input)
        form_layout.addRow("出库数量:", self.quantity_input)
        
        # 出库原因
        self.reason_combo = QComboBox()
        self.reason_combo.addItems(["销售出库", "调拨出库", "退货出库", "损耗出库", "其他"])
        self.reason_combo.setFixedHeight(40)
        self._style_input(self.reason_combo)
        form_layout.addRow("出库原因:", self.reason_combo)
        
        # 备注
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("备注信息（可选）")
        self.notes_input.setFixedHeight(80)
        self._style_input(self.notes_input)
        form_layout.addRow("备注:", self.notes_input)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        add_btn = QPushButton("➕ 添加到列表")
        add_btn.setFixedHeight(45)
        add_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b, stop:1 #d97706);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                padding: 0 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d97706, stop:1 #b45309);
            }
        """)
        add_btn.clicked.connect(self._add_to_list)
        button_layout.addWidget(add_btn)
        
        clear_btn = QPushButton("🗑️ 清空")
        clear_btn.setFixedHeight(45)
        clear_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6b7280, stop:1 #4b5563);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                padding: 0 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4b5563, stop:1 #374151);
            }
        """)
        clear_btn.clicked.connect(self._clear_form)
        button_layout.addWidget(clear_btn)
        
        form_layout.addRow("", button_layout)
        
        input_group.setFixedWidth(400)
        content_layout.addWidget(input_group)
    
    def _create_outbound_list_area(self, content_layout):
        """创建出库列表区域"""
        list_group = QGroupBox("出库列表")
        list_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: 600;
                color: #1e293b;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        list_layout = QVBoxLayout(list_group)
        list_layout.setContentsMargins(20, 20, 20, 20)
        
        # 出库列表表格
        self.outbound_table = QTableWidget()
        self.outbound_table.setColumnCount(6)
        self.outbound_table.setHorizontalHeaderLabels([
            "商品编码", "商品名称", "数量", "原因", "备注", "操作"
        ])
        
        # 设置表格样式
        self.outbound_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                gridline-color: #f1f5f9;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f1f5f9;
            }
            QHeaderView::section {
                background-color: #f8fafc;
                color: #374151;
                font-weight: 600;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #e2e8f0;
            }
        """)
        
        # 设置列宽
        header = self.outbound_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)
        
        self.outbound_table.setColumnWidth(0, 120)
        self.outbound_table.setColumnWidth(2, 80)
        self.outbound_table.setColumnWidth(3, 100)
        self.outbound_table.setColumnWidth(5, 80)
        
        list_layout.addWidget(self.outbound_table)
        
        # 底部按钮
        bottom_layout = QHBoxLayout()
        
        submit_btn = QPushButton("✅ 提交出库")
        submit_btn.setFixedHeight(50)
        submit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626, stop:1 #b91c1c);
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 18px;
                font-weight: 700;
                padding: 0 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #b91c1c, stop:1 #991b1b);
            }
        """)
        submit_btn.clicked.connect(self._submit_outbound)
        bottom_layout.addWidget(submit_btn)
        
        list_layout.addLayout(bottom_layout)
        
        content_layout.addWidget(list_group)
    
    def _create_status_bar(self, main_layout):
        """创建状态栏"""
        status_bar = QWidget()
        status_bar.setFixedHeight(50)
        status_bar.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.95);
                border-top: 1px solid #e2e8f0;
                border-radius: 8px;
            }
        """)
        
        status_layout = QHBoxLayout(status_bar)
        status_layout.setContentsMargins(20, 10, 20, 10)
        
        # 状态信息
        self.status_label = QLabel("就绪 - 请扫描或输入商品编码")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #f59e0b;
                font-size: 14px;
                font-weight: 500;
            }
        """)
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 统计信息
        self.count_label = QLabel("待出库商品: 0 种")
        self.count_label.setStyleSheet("""
            QLabel {
                color: #374151;
                font-size: 14px;
                font-weight: 500;
            }
        """)
        status_layout.addWidget(self.count_label)
        
        main_layout.addWidget(status_bar)
    
    def _style_input(self, widget):
        """设置输入控件样式"""
        widget.setStyleSheet("""
            QLineEdit, QSpinBox, QComboBox, QTextEdit {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #1e293b;
            }
            QLineEdit:focus, QSpinBox:focus, QComboBox:focus, QTextEdit:focus {
                border-color: #f59e0b;
            }
            QLineEdit:read-only {
                background-color: #f8fafc;
                color: #6b7280;
            }
        """)
    
    def _setup_style(self):
        """设置样式"""
        pass
    
    def _setup_connections(self):
        """设置信号连接"""
        self.product_code_input.returnPressed.connect(self._lookup_product)
        self.product_code_input.textChanged.connect(self._on_code_changed)
    
    def _start_scanning(self):
        """开始扫码"""
        self.status_label.setText("扫码中... 请将商品条码对准摄像头")
        self.status_label.setStyleSheet("color: #f59e0b; font-size: 14px; font-weight: 500;")
        
        # 模拟扫码结果
        QTimer.singleShot(2000, self._simulate_scan_result)
    
    def _simulate_scan_result(self):
        """模拟扫码结果"""
        import random
        codes = ["P001", "P002", "P003", "P004", "P005"]
        code = random.choice(codes)
        self.product_code_input.setText(code)
        self._lookup_product()
        self.status_label.setText("扫码成功 - 商品信息已自动填充")
        self.status_label.setStyleSheet("color: #f59e0b; font-size: 14px; font-weight: 500;")
    
    def _lookup_product(self):
        """查找商品信息"""
        code = self.product_code_input.text().strip()
        if not code:
            return
        
        # 模拟商品数据库查询
        products = {
            "P001": {"name": "苹果 iPhone 15", "stock": 50},
            "P002": {"name": "华为 Mate 60", "stock": 30},
            "P003": {"name": "小米 14", "stock": 25},
            "P004": {"name": "OPPO Find X7", "stock": 15},
            "P005": {"name": "vivo X100", "stock": 40}
        }
        
        if code in products:
            product = products[code]
            self.product_name_input.setText(product["name"])
            self.stock_label.setText(f"库存数量: {product['stock']} 件")
            self.quantity_input.setMaximum(product["stock"])
            
            if product["stock"] > 0:
                self.stock_label.setStyleSheet("""
                    QLabel {
                        color: #059669;
                        font-size: 14px;
                        font-weight: 600;
                        background-color: #f0fdf4;
                        border: 1px solid #bbf7d0;
                        border-radius: 6px;
                        padding: 8px 12px;
                    }
                """)
                self.status_label.setText(f"找到商品: {product['name']} (库存: {product['stock']})")
                self.status_label.setStyleSheet("color: #f59e0b; font-size: 14px; font-weight: 500;")
            else:
                self.stock_label.setStyleSheet("""
                    QLabel {
                        color: #dc2626;
                        font-size: 14px;
                        font-weight: 600;
                        background-color: #fef2f2;
                        border: 1px solid #fecaca;
                        border-radius: 6px;
                        padding: 8px 12px;
                    }
                """)
                self.status_label.setText("商品库存不足，无法出库")
                self.status_label.setStyleSheet("color: #dc2626; font-size: 14px; font-weight: 500;")
        else:
            self.product_name_input.setText("")
            self.stock_label.setText("库存数量: --")
            self.stock_label.setStyleSheet("""
                QLabel {
                    color: #6b7280;
                    font-size: 14px;
                    font-weight: 600;
                    background-color: #f9fafb;
                    border: 1px solid #e5e7eb;
                    border-radius: 6px;
                    padding: 8px 12px;
                }
            """)
            self.status_label.setText("未找到商品信息，请检查编码")
            self.status_label.setStyleSheet("color: #dc2626; font-size: 14px; font-weight: 500;")
    
    def _on_code_changed(self):
        """商品编码改变时"""
        if not self.product_code_input.text().strip():
            self.product_name_input.setText("")
            self.stock_label.setText("库存数量: --")
            self.stock_label.setStyleSheet("""
                QLabel {
                    color: #6b7280;
                    font-size: 14px;
                    font-weight: 600;
                    background-color: #f9fafb;
                    border: 1px solid #e5e7eb;
                    border-radius: 6px;
                    padding: 8px 12px;
                }
            """)
            self.status_label.setText("就绪 - 请扫描或输入商品编码")
            self.status_label.setStyleSheet("color: #f59e0b; font-size: 14px; font-weight: 500;")
    
    def _add_to_list(self):
        """添加到出库列表"""
        code = self.product_code_input.text().strip()
        name = self.product_name_input.text().strip()
        quantity = self.quantity_input.value()
        reason = self.reason_combo.currentText()
        notes = self.notes_input.toPlainText().strip()
        
        if not code or not name:
            QMessageBox.warning(self, "提示", "请先输入商品编码并查找商品信息")
            return
        
        # 检查库存
        if "库存不足" in self.status_label.text():
            QMessageBox.warning(self, "提示", "商品库存不足，无法出库")
            return
        
        # 添加到表格
        row = self.outbound_table.rowCount()
        self.outbound_table.insertRow(row)
        
        self.outbound_table.setItem(row, 0, QTableWidgetItem(code))
        self.outbound_table.setItem(row, 1, QTableWidgetItem(name))
        self.outbound_table.setItem(row, 2, QTableWidgetItem(str(quantity)))
        self.outbound_table.setItem(row, 3, QTableWidgetItem(reason))
        self.outbound_table.setItem(row, 4, QTableWidgetItem(notes))
        
        # 删除按钮
        delete_btn = QPushButton("删除")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc2626;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #b91c1c;
            }
        """)
        delete_btn.clicked.connect(lambda: self._delete_row(row))
        self.outbound_table.setCellWidget(row, 5, delete_btn)
        
        # 清空表单
        self._clear_form()
        
        # 更新统计
        self._update_count()
        
        self.status_label.setText(f"已添加商品: {name}")
        self.status_label.setStyleSheet("color: #f59e0b; font-size: 14px; font-weight: 500;")
    
    def _delete_row(self, row):
        """删除行"""
        self.outbound_table.removeRow(row)
        self._update_count()
        self.status_label.setText("已删除商品")
        self.status_label.setStyleSheet("color: #f59e0b; font-size: 14px; font-weight: 500;")
    
    def _clear_form(self):
        """清空表单"""
        self.product_code_input.clear()
        self.product_name_input.clear()
        self.stock_label.setText("库存数量: --")
        self.stock_label.setStyleSheet("""
            QLabel {
                color: #6b7280;
                font-size: 14px;
                font-weight: 600;
                background-color: #f9fafb;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 8px 12px;
            }
        """)
        self.quantity_input.setValue(1)
        self.reason_combo.setCurrentIndex(0)
        self.notes_input.clear()
        self.product_code_input.setFocus()
    
    def _update_count(self):
        """更新统计信息"""
        count = self.outbound_table.rowCount()
        self.count_label.setText(f"待出库商品: {count} 种")
    
    def _submit_outbound(self):
        """提交出库"""
        if self.outbound_table.rowCount() == 0:
            QMessageBox.warning(self, "提示", "请先添加要出库的商品")
            return
        
        reply = QMessageBox.question(
            self, "确认出库", 
            f"确定要提交 {self.outbound_table.rowCount()} 种商品的出库操作吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 模拟出库处理
            QMessageBox.information(self, "出库成功", "所有商品已成功出库！")
            
            # 清空列表
            self.outbound_table.setRowCount(0)
            self._update_count()
            self.status_label.setText("出库完成 - 可以继续添加商品")
            self.status_label.setStyleSheet("color: #f59e0b; font-size: 14px; font-weight: 500;")
