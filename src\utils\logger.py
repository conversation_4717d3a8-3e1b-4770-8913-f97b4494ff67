"""
日志管理工具
© 2024 贵州睿云慧通科技有限公司
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Optional
from pathlib import Path

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class WMSLogger:
    """WMS系统日志管理器"""
    
    def __init__(self, name: str = "wms", log_dir: str = None):
        self.name = name
        self.log_dir = log_dir or os.path.join(os.path.dirname(__file__), '..', '..', 'logs')
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        # 创建日志目录
        Path(self.log_dir).mkdir(parents=True, exist_ok=True)
        
        # 创建日志器
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if self.logger.handlers:
            return
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器 - 所有日志
        all_log_file = os.path.join(self.log_dir, 'wms.log')
        file_handler = logging.handlers.RotatingFileHandler(
            all_log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # 错误日志文件处理器
        error_log_file = os.path.join(self.log_dir, 'error.log')
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        self.logger.addHandler(error_handler)
        
        # 操作日志文件处理器
        operation_log_file = os.path.join(self.log_dir, 'operation.log')
        operation_handler = logging.handlers.RotatingFileHandler(
            operation_log_file, maxBytes=10*1024*1024, backupCount=10, encoding='utf-8'
        )
        operation_handler.setLevel(logging.INFO)
        operation_handler.addFilter(OperationLogFilter())
        operation_handler.setFormatter(file_formatter)
        self.logger.addHandler(operation_handler)
    
    def get_logger(self):
        """获取日志器"""
        return self.logger

class OperationLogFilter(logging.Filter):
    """操作日志过滤器"""
    
    def filter(self, record):
        # 只记录包含操作标识的日志
        return hasattr(record, 'operation') or 'operation' in record.getMessage().lower()

# 全局日志管理器
_logger_manager = WMSLogger()

def get_logger(name: str = None) -> logging.Logger:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    if name:
        return logging.getLogger(f"wms.{name}")
    return _logger_manager.get_logger()

def log_operation(user_id: int, module: str, action: str, description: str, 
                 level: str = "INFO", **kwargs):
    """
    记录操作日志
    
    Args:
        user_id: 用户ID
        module: 模块名称
        action: 操作动作
        description: 操作描述
        level: 日志级别
        **kwargs: 其他参数
    """
    logger = get_logger("operation")
    
    # 构建日志消息
    message = f"[OPERATION] 用户{user_id} 在模块{module} 执行{action}: {description}"
    
    # 添加额外信息
    if kwargs:
        extra_info = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
        message += f" ({extra_info})"
    
    # 记录日志
    log_level = getattr(logging, level.upper(), logging.INFO)
    logger.log(log_level, message, extra={'operation': True})

def setup_logging(log_level: str = "INFO", log_dir: str = None):
    """
    设置全局日志配置
    
    Args:
        log_level: 日志级别
        log_dir: 日志目录
    """
    global _logger_manager
    
    if log_dir:
        _logger_manager = WMSLogger(log_dir=log_dir)
    
    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)
    _logger_manager.logger.setLevel(level)

# 示例使用
if __name__ == "__main__":
    # 测试日志功能
    logger = get_logger("test")
    
    logger.debug("这是调试信息")
    logger.info("这是一般信息")
    logger.warning("这是警告信息")
    logger.error("这是错误信息")
    logger.critical("这是严重错误信息")
    
    # 测试操作日志
    log_operation(
        user_id=1,
        module="inventory",
        action="update_stock",
        description="更新产品库存",
        product_id=123,
        quantity=10
    )
