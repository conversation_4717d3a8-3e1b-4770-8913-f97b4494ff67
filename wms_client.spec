# -*- mode: python ; coding: utf-8 -*-
"""
WMS库房自助出入库客户端系统 - PyInstaller配置文件
© 2024 贵州睿云慧通科技有限公司
"""

import os
import sys
from pathlib import Path

# 获取项目根目录
project_root = Path(os.getcwd()).absolute()

# 数据文件和资源文件
datas = [
    # 配置文件
    ('config.ini', '.'),
    
    # 数据目录（包含数据库文件）
    ('data', 'data'),
    
    # 日志目录
    ('logs', 'logs'),
    
    # 文档目录
    ('docs', 'docs'),
    
    # 资源文件（如果存在）
    # ('resources', 'resources'),
]

# 隐藏导入（PyInstaller可能无法自动检测的模块）
hiddenimports = [
    # PyQt6相关
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'PyQt6.QtSql',
    'PyQt6.QtNetwork',
    'PyQt6.QtPrintSupport',
    
    # SQLAlchemy相关
    'sqlalchemy',
    'sqlalchemy.dialects.sqlite',
    'sqlalchemy.dialects.postgresql',
    'sqlalchemy.pool',
    'sqlalchemy.engine',
    
    # 数据库驱动
    'sqlite3',
    'psycopg2',
    
    # 图像处理
    'cv2',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'numpy',
    
    # 人脸识别
    'face_recognition',
    'dlib',
    
    # OCR相关
    'pytesseract',
    'paddleocr',
    'easyocr',
    
    # 条码识别
    'pyzbar',
    'qrcode',
    
    # 加密相关
    'cryptography',
    'bcrypt',
    'Crypto',
    
    # 网络请求
    'requests',
    'urllib3',
    
    # 数据处理
    'pandas',
    'openpyxl',
    'xlsxwriter',
    
    # 日志和配置
    'colorlog',
    'configparser',
    
    # 系统相关
    'psutil',
    'pathlib',
    
    # Windows特定
    'win32api',
    'win32con',
    'win32gui',
    'win32print',
    'wmi',
    
    # 项目模块
    'src',
    'src.models',
    'src.ui',
    'src.ui.windows',
    'src.ui.dialogs',
    'src.ui.components',
    'src.ui.styles',
    'src.ui.utils',
    'src.core',
    'src.services',
    'src.devices',
    'src.utils',
    'src.config',
]

# 排除的模块（减少打包大小）
excludes = [
    # 测试相关
    'pytest',
    'pytest_cov',
    'pytest_qt',
    'pytest_mock',
    
    # 开发工具
    'black',
    'flake8',
    'mypy',
    'isort',
    'ipython',
    'jupyter',
    
    # 文档工具
    'sphinx',
    'sphinx_rtd_theme',
    
    # 不需要的库
    'matplotlib',
    'moviepy',
    'sounddevice',
    'babel',
    'cython',
    'numba',
    
    # 可选的OCR引擎（如果不使用）
    # 'paddleocr',
    # 'easyocr',
]

# 二进制文件（通常PyInstaller会自动处理）
binaries = []

# 分析主程序
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 处理重复文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='WMS客户端系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 如果有图标文件，在这里指定路径
    version='version_info.txt'  # 版本信息文件
)

# 收集所有文件到一个目录
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='WMS客户端系统'
)
