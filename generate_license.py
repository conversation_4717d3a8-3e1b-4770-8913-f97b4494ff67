#!/usr/bin/env python3
"""
WMS授权密钥生成工具
© 2024 贵州睿云慧通科技有限公司

用于生成不同类型的授权密钥
"""

import sys
import os
import argparse
from datetime import datetime, timedelta

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.security.license_manager import LicenseManager


def print_banner():
    """打印横幅"""
    print("=" * 80)
    print("🔐 WMS授权密钥生成工具")
    print("© 2024 贵州睿云慧通科技有限公司")
    print("=" * 80)


def generate_license(license_type, days, customer_name, customer_company, customer_email):
    """生成授权密钥"""
    try:
        license_manager = LicenseManager()
        
        # 客户信息
        customer_info = {
            'name': customer_name,
            'company': customer_company,
            'email': customer_email,
            'generated_at': datetime.now().isoformat(),
            'generated_by': 'License Generator Tool'
        }
        
        # 生成授权密钥
        license_key = license_manager.generate_license_key(
            license_type, days, customer_info
        )
        
        # 验证生成的授权
        is_valid, info = license_manager.validate_license(license_key)
        
        if is_valid:
            print(f"✅ 授权密钥生成成功！")
            print(f"📋 授权信息:")
            print(f"   类型: {info.get('type_name', '未知')}")
            print(f"   有效期: {days} 天")
            print(f"   到期时间: {info.get('expire_date', '未知')}")
            print(f"   客户: {customer_name} ({customer_company})")
            print(f"   邮箱: {customer_email}")
            print()
            print(f"🔑 授权密钥:")
            print(f"{license_key}")
            print()
            
            # 保存到文件
            filename = f"license_{license_type}_{customer_company.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"WMS系统授权密钥\n")
                f.write(f"© 2024 贵州睿云慧通科技有限公司\n")
                f.write(f"=" * 50 + "\n\n")
                f.write(f"授权类型: {info.get('type_name', '未知')}\n")
                f.write(f"有效期: {days} 天\n")
                f.write(f"到期时间: {info.get('expire_date', '未知')}\n")
                f.write(f"客户姓名: {customer_name}\n")
                f.write(f"客户公司: {customer_company}\n")
                f.write(f"客户邮箱: {customer_email}\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(f"授权密钥:\n")
                f.write(f"{license_key}\n\n")
                f.write(f"使用说明:\n")
                f.write(f"1. 启动WMS系统\n")
                f.write(f"2. 点击登录界面的'授权'按钮\n")
                f.write(f"3. 切换到'激活授权'选项卡\n")
                f.write(f"4. 复制粘贴上述授权密钥\n")
                f.write(f"5. 点击'激活授权'按钮\n\n")
                f.write(f"注意事项:\n")
                f.write(f"- 授权密钥与机器硬件绑定，不可转移\n")
                f.write(f"- 请妥善保管授权密钥，避免泄露\n")
                f.write(f"- 如有问题请联系技术支持\n")
            
            print(f"💾 授权密钥已保存到文件: {filename}")
            
        else:
            print(f"❌ 授权密钥验证失败: {info.get('error', '未知错误')}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 生成授权密钥失败: {str(e)}")
        return False


def interactive_mode():
    """交互模式"""
    print("🎯 交互式授权密钥生成")
    print()
    
    # 选择授权类型
    print("请选择授权类型:")
    print("1. 试用版 (Trial) - 30天，2用户，100商品")
    print("2. 标准版 (Standard) - 1年，10用户，1000商品")
    print("3. 专业版 (Professional) - 1年，50用户，10000商品")
    print("4. 企业版 (Enterprise) - 自定义，无限用户和商品")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            if choice == '1':
                license_type = 'trial'
                default_days = 30
                break
            elif choice == '2':
                license_type = 'standard'
                default_days = 365
                break
            elif choice == '3':
                license_type = 'professional'
                default_days = 365
                break
            elif choice == '4':
                license_type = 'enterprise'
                default_days = 365
                break
            else:
                print("❌ 无效选择，请重新输入")
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            return False
    
    # 输入有效期
    while True:
        try:
            days_input = input(f"\n请输入有效期天数 (默认{default_days}天): ").strip()
            if not days_input:
                days = default_days
            else:
                days = int(days_input)
                if days <= 0:
                    print("❌ 有效期必须大于0天")
                    continue
            break
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            return False
    
    # 输入客户信息
    try:
        print("\n请输入客户信息:")
        customer_name = input("客户姓名: ").strip()
        if not customer_name:
            customer_name = "未知客户"
        
        customer_company = input("客户公司: ").strip()
        if not customer_company:
            customer_company = "未知公司"
        
        customer_email = input("客户邮箱: ").strip()
        if not customer_email:
            customer_email = "<EMAIL>"
        
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return False
    
    # 确认信息
    print(f"\n📋 授权信息确认:")
    print(f"   授权类型: {license_type}")
    print(f"   有效期: {days} 天")
    print(f"   客户姓名: {customer_name}")
    print(f"   客户公司: {customer_company}")
    print(f"   客户邮箱: {customer_email}")
    
    try:
        confirm = input(f"\n确认生成授权密钥? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 用户取消生成")
            return False
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return False
    
    # 生成授权密钥
    return generate_license(license_type, days, customer_name, customer_company, customer_email)


def main():
    """主函数"""
    print_banner()
    
    parser = argparse.ArgumentParser(description='WMS授权密钥生成工具')
    parser.add_argument('--type', choices=['trial', 'standard', 'professional', 'enterprise'],
                       help='授权类型')
    parser.add_argument('--days', type=int, help='有效期天数')
    parser.add_argument('--name', help='客户姓名')
    parser.add_argument('--company', help='客户公司')
    parser.add_argument('--email', help='客户邮箱')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互模式')
    
    args = parser.parse_args()
    
    try:
        if args.interactive or not all([args.type, args.days, args.name, args.company, args.email]):
            # 交互模式
            success = interactive_mode()
        else:
            # 命令行模式
            success = generate_license(
                args.type, args.days, args.name, args.company, args.email
            )
        
        if success:
            print("\n🎉 授权密钥生成完成！")
            return 0
        else:
            print("\n❌ 授权密钥生成失败！")
            return 1
            
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return 1
    except Exception as e:
        print(f"\n❌ 程序错误: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
