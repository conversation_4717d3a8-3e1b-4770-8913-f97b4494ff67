# WMS客户端系统 - 部署指南 (安全增强版)

## 🎉 打包完成

恭喜！WMS库房自助出入库客户端系统已成功打包为Windows 10可执行文件，现已集成完整的安全功能。

## 📦 打包结果

### 生成的文件
```
📁 dist/WMS客户端系统/
├── 🚀 WMS客户端系统.exe          # 主程序（8.6 MB）
├── 📁 _internal/                 # 运行时依赖库
│   ├── cryptography/             # 加密库
│   ├── data/                     # 数据目录
│   ├── logs/                     # 日志目录
│   └── ...其他依赖
├── ⚙️ config.ini                # 配置文件
├── 📄 使用说明.txt               # 使用说明
└── 🔧 启动WMS系统.bat            # 启动脚本

📦 WMS客户端系统_v1.0.0_20250731.zip  # 分发包
```

### 文件大小
- **主程序**: 8.6 MB
- **完整目录**: 约 60-90 MB
- **压缩包**: 约 35-55 MB

## 🔐 新增安全功能

### 密码加盐功能
- ✅ PBKDF2-HMAC-SHA256算法
- ✅ 32字节随机盐值
- ✅ 100,000次迭代防暴力破解
- ✅ 时间常数比较防时序攻击

### 授权控制功能
- ✅ 机器硬件绑定
- ✅ 数字签名防篡改
- ✅ 时间限制控制
- ✅ 功能权限管理

### 用户认证管理
- ✅ 多角色权限系统
- ✅ 会话管理和超时控制
- ✅ 登录保护和账户锁定
- ✅ 用户管理功能

## 🚀 部署方式

### 方式1: 直接运行（推荐）
1. 解压 `WMS客户端系统_v1.0.0_20250731.zip`
2. 双击 `启动WMS系统.bat` 或直接运行 `WMS客户端系统.exe`
3. 首次运行会自动初始化数据库

### 方式2: 复制部署
1. 将整个 `dist/WMS客户端系统/` 目录复制到目标机器
2. 确保目标机器有必要的运行时环境
3. 运行主程序

## 💻 系统要求

### 最低要求
- **操作系统**: Windows 10 (64位) 或更高版本
- **内存**: 4GB RAM
- **磁盘空间**: 200MB 可用空间
- **显示器**: 1024x768 分辨率或更高

### 推荐配置
- **操作系统**: Windows 10/11 (64位)
- **内存**: 8GB RAM 或更高
- **磁盘空间**: 500MB 可用空间
- **显示器**: 1920x1080 分辨率或更高

## 🔧 运行时依赖

### 自动包含的依赖
✅ Python运行时环境
✅ PyQt6 GUI框架
✅ SQLAlchemy 数据库ORM
✅ OpenCV 图像处理
✅ NumPy 数值计算
✅ 其他核心依赖

### 可能需要的系统组件
- **Visual C++ Redistributable** (通常Windows已包含)
- **Windows Defender** 可能需要添加信任

## 🎯 功能特性

### 核心功能
✅ 用户登录认证
✅ 仓库管理界面
✅ 数据库操作
✅ 系统配置
✅ 日志记录

### 界面特性
✅ 现代化UI设计
✅ 响应式布局
✅ 多分辨率适配
✅ 中文界面支持

## 🔐 安全特性

### 用户认证
- 管理员密码验证
- 多种登录方式支持
- 权限管理

### 数据安全
- 本地SQLite数据库
- 配置文件加密
- 操作日志记录

## 📋 使用说明

### 首次启动
1. 运行程序后会显示启动画面
2. 自动初始化数据库和安全模块
3. 创建默认管理员账户（admin/admin123）
4. 进入登录界面

### 授权激活
**首次使用需要激活授权：**
1. 点击登录界面的"🔐 授权"按钮
2. 选择"🎯 试用授权"申请30天免费试用
3. 或选择"🔑 激活授权"输入正式授权密钥

### 登录方式
- **账号登录**: admin / admin123（默认管理员）
- **扫码登录**: 支持二维码扫描
- **人脸识别**: 支持人脸识别登录
- **多用户支持**: 可创建不同角色用户

### 安全特性
- **密码保护**: 所有密码使用加盐哈希存储
- **授权验证**: 系统启动时自动验证授权
- **权限控制**: 基于角色的功能权限管理
- **会话管理**: 8小时会话超时保护

### 退出系统
- 点击"退出系统"按钮
- 需要管理员密码验证（使用加盐验证）

## 🛠️ 故障排除

### 常见问题

#### 1. 程序无法启动
**解决方案**:
- 检查Windows版本是否支持
- 安装Visual C++ Redistributable
- 以管理员身份运行

#### 2. 界面显示异常
**解决方案**:
- 检查显示器分辨率设置
- 调整Windows显示缩放
- 更新显卡驱动

#### 3. 数据库错误
**解决方案**:
- 检查data目录权限
- 删除data/wms.db重新初始化
- 确保磁盘空间充足

#### 4. 杀毒软件误报
**解决方案**:
- 将程序添加到杀毒软件白名单
- 使用Windows Defender排除项
- 从可信来源下载

### 日志文件
程序运行日志保存在 `logs/` 目录：
- `wms.log` - 主程序日志
- `error.log` - 错误日志
- `operation.log` - 操作日志

## 📞 技术支持

### 联系方式
- **公司**: 贵州睿云慧通科技有限公司
- **版本**: v1.0.0
- **构建日期**: 2025-07-31

### 获取帮助
1. 查看日志文件定位问题
2. 参考使用说明文档
3. 联系技术支持团队

## 📝 更新说明

### v1.0.0 (2025-07-31) - 安全增强版
- ✅ 初始版本发布
- ✅ 完整的WMS客户端功能
- ✅ 现代化UI界面
- ✅ 多种登录方式
- ✅ 完善的错误处理
- 🔐 **新增密码加盐功能**
- 🔐 **新增授权控制系统**
- 🔐 **新增用户认证管理**
- 🔐 **新增角色权限控制**
- 🔐 **新增授权管理界面**

## 🔄 升级指南

### 升级步骤
1. 备份当前数据库文件
2. 备份配置文件
3. 安装新版本
4. 恢复数据和配置

### 数据迁移
- 数据库文件: `data/wms.db`
- 配置文件: `config.ini`
- 日志文件: `logs/`

---

## 🎊 部署成功

WMS库房自助出入库客户端系统已成功打包并可在Windows 10系统上运行！

**快速开始**:
1. 解压分发包
2. 运行 `启动WMS系统.bat`
3. 使用 admin/admin123 登录
4. 开始使用系统

**祝您使用愉快！** 🚀

---

© 2024 贵州睿云慧通科技有限公司 版权所有
