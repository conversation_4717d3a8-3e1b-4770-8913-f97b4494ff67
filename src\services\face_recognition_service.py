"""
人脸识别服务
© 2024 贵州睿云慧通科技有限公司
"""

import cv2
import numpy as np
from typing import Optional, List, Dict, Any, Tuple
import pickle
import base64
from io import BytesIO

from ..utils.logger import get_logger
from ..utils.exceptions import ValidationError, DeviceError

logger = get_logger(__name__)

class FaceRecognitionService:
    """人脸识别服务"""
    
    def __init__(self):
        self.face_cascade = None
        self.face_recognizer = None
        self.face_recognition_available = False
        self._init_face_detection()
        self._init_face_recognition()
    
    def _init_face_detection(self):
        """初始化人脸检测"""
        try:
            # 加载OpenCV人脸检测器
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            self.face_cascade = cv2.CascadeClassifier(cascade_path)
            logger.info("人脸检测器初始化成功")
        except Exception as e:
            logger.error(f"人脸检测器初始化失败: {e}")
    
    def _init_face_recognition(self):
        """初始化人脸识别"""
        try:
            import face_recognition
            self.face_recognition_available = True
            logger.info("人脸识别库初始化成功")
        except ImportError:
            logger.warning("face_recognition库未安装，将使用基础人脸检测")
            self.face_recognition_available = False
        except Exception as e:
            logger.error(f"人脸识别库初始化失败: {e}")
            self.face_recognition_available = False
    
    def detect_faces(self, image_data: bytes) -> List[Dict[str, Any]]:
        """
        检测人脸
        
        Args:
            image_data: 图像数据
            
        Returns:
            检测到的人脸列表
        """
        try:
            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                raise ValidationError("无效的图像数据")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 检测人脸
            faces = self.face_cascade.detectMultiScale(
                gray,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(30, 30)
            )
            
            face_list = []
            for i, (x, y, w, h) in enumerate(faces):
                face_info = {
                    "id": i,
                    "bbox": {"x": int(x), "y": int(y), "width": int(w), "height": int(h)},
                    "confidence": 0.8,  # OpenCV检测器没有置信度，使用固定值
                    "landmarks": None
                }
                
                # 如果有face_recognition库，提取更详细信息
                if self.face_recognition_available:
                    face_landmarks = self._get_face_landmarks(image, (x, y, w, h))
                    face_info["landmarks"] = face_landmarks
                
                face_list.append(face_info)
            
            return face_list
            
        except Exception as e:
            logger.error(f"人脸检测失败: {e}")
            raise DeviceError(f"人脸检测失败: {str(e)}")
    
    def extract_face_encoding(self, image_data: bytes, face_bbox: Dict[str, int] = None) -> Optional[str]:
        """
        提取人脸特征编码
        
        Args:
            image_data: 图像数据
            face_bbox: 人脸边界框，如果为None则自动检测
            
        Returns:
            人脸特征编码（base64字符串）或None
        """
        if not self.face_recognition_available:
            logger.warning("face_recognition库不可用，无法提取人脸特征")
            return None
        
        try:
            import face_recognition
            
            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                raise ValidationError("无效的图像数据")
            
            # 转换为RGB格式（face_recognition使用RGB）
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 如果指定了人脸区域，裁剪图像
            if face_bbox:
                x, y, w, h = face_bbox["x"], face_bbox["y"], face_bbox["width"], face_bbox["height"]
                rgb_image = rgb_image[y:y+h, x:x+w]
            
            # 检测人脸位置
            face_locations = face_recognition.face_locations(rgb_image)
            
            if not face_locations:
                logger.warning("未检测到人脸")
                return None
            
            # 提取人脸特征编码
            face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
            
            if not face_encodings:
                logger.warning("无法提取人脸特征")
                return None
            
            # 取第一个人脸的特征编码
            encoding = face_encodings[0]
            
            # 序列化并编码为base64
            encoding_bytes = pickle.dumps(encoding)
            encoding_b64 = base64.b64encode(encoding_bytes).decode('utf-8')
            
            return encoding_b64
            
        except Exception as e:
            logger.error(f"人脸特征提取失败: {e}")
            return None
    
    def compare_faces(self, encoding1: str, encoding2: str, tolerance: float = 0.6) -> Dict[str, Any]:
        """
        比较两个人脸特征
        
        Args:
            encoding1: 第一个人脸特征编码
            encoding2: 第二个人脸特征编码
            tolerance: 相似度阈值
            
        Returns:
            比较结果
        """
        if not self.face_recognition_available:
            return {"match": False, "distance": 1.0, "message": "人脸识别库不可用"}
        
        try:
            import face_recognition
            
            # 解码特征编码
            encoding1_bytes = base64.b64decode(encoding1.encode('utf-8'))
            encoding2_bytes = base64.b64decode(encoding2.encode('utf-8'))
            
            face_encoding1 = pickle.loads(encoding1_bytes)
            face_encoding2 = pickle.loads(encoding2_bytes)
            
            # 计算人脸距离
            distance = face_recognition.face_distance([face_encoding1], face_encoding2)[0]
            
            # 判断是否匹配
            match = distance <= tolerance
            
            # 计算相似度百分比
            similarity = max(0, (1 - distance) * 100)
            
            return {
                "match": match,
                "distance": float(distance),
                "similarity": float(similarity),
                "tolerance": tolerance,
                "message": "匹配成功" if match else "不匹配"
            }
            
        except Exception as e:
            logger.error(f"人脸比较失败: {e}")
            return {"match": False, "distance": 1.0, "message": f"比较失败: {str(e)}"}
    
    def find_best_match(self, target_encoding: str, known_encodings: List[Dict[str, Any]], 
                       tolerance: float = 0.6) -> Optional[Dict[str, Any]]:
        """
        在已知人脸中找到最佳匹配
        
        Args:
            target_encoding: 目标人脸特征编码
            known_encodings: 已知人脸特征列表，每个元素包含encoding和user_id
            tolerance: 相似度阈值
            
        Returns:
            最佳匹配结果或None
        """
        if not self.face_recognition_available or not known_encodings:
            return None
        
        try:
            best_match = None
            best_distance = float('inf')
            
            for known_face in known_encodings:
                result = self.compare_faces(target_encoding, known_face["encoding"], tolerance)
                
                if result["match"] and result["distance"] < best_distance:
                    best_distance = result["distance"]
                    best_match = {
                        "user_id": known_face["user_id"],
                        "distance": result["distance"],
                        "similarity": result["similarity"],
                        "confidence": result["similarity"] / 100
                    }
            
            return best_match
            
        except Exception as e:
            logger.error(f"人脸匹配失败: {e}")
            return None
    
    def _get_face_landmarks(self, image: np.ndarray, face_bbox: Tuple[int, int, int, int]) -> Optional[Dict[str, List]]:
        """
        获取人脸关键点
        
        Args:
            image: 图像
            face_bbox: 人脸边界框
            
        Returns:
            人脸关键点字典或None
        """
        if not self.face_recognition_available:
            return None
        
        try:
            import face_recognition
            
            x, y, w, h = face_bbox
            
            # 转换为RGB格式
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 转换边界框格式（face_recognition使用top, right, bottom, left）
            face_location = (y, x + w, y + h, x)
            
            # 获取人脸关键点
            landmarks_list = face_recognition.face_landmarks(rgb_image, [face_location])
            
            if landmarks_list:
                return landmarks_list[0]
            
            return None
            
        except Exception as e:
            logger.error(f"获取人脸关键点失败: {e}")
            return None
    
    def assess_face_quality(self, image_data: bytes, face_bbox: Dict[str, int] = None) -> Dict[str, Any]:
        """
        评估人脸质量
        
        Args:
            image_data: 图像数据
            face_bbox: 人脸边界框
            
        Returns:
            质量评估结果
        """
        try:
            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                raise ValidationError("无效的图像数据")
            
            # 如果指定了人脸区域，裁剪图像
            if face_bbox:
                x, y, w, h = face_bbox["x"], face_bbox["y"], face_bbox["width"], face_bbox["height"]
                face_image = image[y:y+h, x:x+w]
            else:
                face_image = image
            
            # 转换为灰度图
            gray = cv2.cvtColor(face_image, cv2.COLOR_BGR2GRAY)
            
            # 计算图像清晰度（拉普拉斯方差）
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            # 计算亮度
            brightness = np.mean(gray)
            
            # 计算对比度
            contrast = gray.std()
            
            # 评估人脸大小
            face_size = face_image.shape[0] * face_image.shape[1] if face_bbox else 0
            
            # 综合评分
            quality_score = 0
            
            # 清晰度评分（0-40分）
            if laplacian_var > 500:
                quality_score += 40
            elif laplacian_var > 200:
                quality_score += 30
            elif laplacian_var > 100:
                quality_score += 20
            else:
                quality_score += 10
            
            # 亮度评分（0-30分）
            if 80 <= brightness <= 180:
                quality_score += 30
            elif 60 <= brightness <= 200:
                quality_score += 20
            else:
                quality_score += 10
            
            # 对比度评分（0-20分）
            if contrast > 50:
                quality_score += 20
            elif contrast > 30:
                quality_score += 15
            else:
                quality_score += 10
            
            # 人脸大小评分（0-10分）
            if face_size > 10000:
                quality_score += 10
            elif face_size > 5000:
                quality_score += 8
            else:
                quality_score += 5
            
            return {
                "quality_score": quality_score,
                "sharpness": float(laplacian_var),
                "brightness": float(brightness),
                "contrast": float(contrast),
                "face_size": face_size,
                "is_good_quality": quality_score >= 70
            }
            
        except Exception as e:
            logger.error(f"人脸质量评估失败: {e}")
            return {
                "quality_score": 0,
                "sharpness": 0,
                "brightness": 0,
                "contrast": 0,
                "face_size": 0,
                "is_good_quality": False
            }
