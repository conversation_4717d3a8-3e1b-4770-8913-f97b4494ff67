# WMS客户端系统 - UI设计指南

## 1. 设计理念

### 1.1 核心原则
- **企业风格**：专业、可信赖的视觉形象
- **正式感**：严谨、规范的界面布局
- **简约性**：去除冗余，突出核心功能
- **大气感**：宽敞的布局，合理的留白

### 1.2 设计目标
- 提升用户操作效率
- 降低学习成本
- 增强品牌形象
- 确保视觉一致性

## 2. 色彩系统

### 2.1 主色调
```
主色（Primary）：   #1E3A8A  (深蓝色) - 专业、稳重
辅助色（Secondary）：#3B82F6  (蓝色)   - 活力、科技感
成功色（Success）：  #10B981  (绿色)   - 成功、确认
警告色（Warning）：  #F59E0B  (橙色)   - 警告、注意
错误色（Error）：    #EF4444  (红色)   - 错误、危险
```

### 2.2 中性色
```
文字主色：  #1F2937  (深灰)
文字副色：  #6B7280  (中灰)
文字辅助：  #9CA3AF  (浅灰)
边框色：    #E5E7EB  (浅灰边框)
背景色：    #F9FAFB  (浅灰背景)
纯白：      #FFFFFF
```

### 2.3 功能色彩应用
- **入库操作**：绿色系 (#10B981)
- **出库操作**：蓝色系 (#3B82F6)
- **查询功能**：中性色系 (#6B7280)
- **系统管理**：深蓝色系 (#1E3A8A)

## 3. 字体系统

### 3.1 字体选择
```
主字体：Microsoft YaHei UI (微软雅黑UI)
英文字体：Segoe UI
数字字体：Consolas (等宽字体，用于数据显示)
```

### 3.2 字体层级
```
标题1 (H1)：28px, Bold, #1F2937
标题2 (H2)：24px, Bold, #1F2937
标题3 (H3)：20px, Bold, #1F2937
标题4 (H4)：18px, Bold, #1F2937
正文大 (Body Large)：16px, Regular, #1F2937
正文 (Body)：14px, Regular, #1F2937
正文小 (Body Small)：12px, Regular, #6B7280
说明文字 (Caption)：11px, Regular, #9CA3AF
```

## 4. 布局系统

### 4.1 网格系统
- **基础单位**：8px
- **组件间距**：16px, 24px, 32px
- **页面边距**：32px
- **卡片内边距**：24px

### 4.2 页面布局
```
┌─────────────────────────────────────────────────────────┐
│  顶部导航栏 (64px)                                        │
├─────────────────────────────────────────────────────────┤
│  侧边栏    │                主内容区域                    │
│  (240px)   │                                           │
│           │                                           │
│  导航菜单   │              功能操作区                     │
│           │                                           │
│           │                                           │
│           │                                           │
└─────────────────────────────────────────────────────────┘
```

### 4.3 响应式设计
- **大屏幕** (≥1920px)：完整布局
- **标准屏** (1366-1919px)：紧凑布局
- **小屏幕** (1024-1365px)：简化布局

## 5. 组件设计规范

### 5.1 按钮设计
```python
# 主要按钮 (Primary Button)
background: #1E3A8A
color: #FFFFFF
border-radius: 6px
padding: 12px 24px
font-size: 14px
font-weight: 500

# 次要按钮 (Secondary Button)
background: #FFFFFF
color: #1E3A8A
border: 1px solid #1E3A8A
border-radius: 6px
padding: 12px 24px

# 危险按钮 (Danger Button)
background: #EF4444
color: #FFFFFF
border-radius: 6px
padding: 12px 24px
```

### 5.2 输入框设计
```python
# 标准输入框
border: 1px solid #E5E7EB
border-radius: 6px
padding: 12px 16px
font-size: 14px
background: #FFFFFF

# 聚焦状态
border-color: #3B82F6
box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1)

# 错误状态
border-color: #EF4444
box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1)
```

### 5.3 卡片设计
```python
# 标准卡片
background: #FFFFFF
border: 1px solid #E5E7EB
border-radius: 8px
padding: 24px
box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1)
```

## 6. 图标系统

### 6.1 图标风格
- **风格**：线性图标 (Outline Icons)
- **粗细**：1.5px
- **尺寸**：16px, 20px, 24px, 32px
- **颜色**：继承文字颜色

### 6.2 功能图标
```
入库：📦 (package-plus)
出库：📤 (package-minus)
查询：🔍 (search)
用户：👤 (user)
设置：⚙️ (settings)
报表：📊 (chart-bar)
打印：🖨️ (printer)
扫码：📱 (qr-code)
```

## 7. 交互设计

### 7.1 状态反馈
- **加载状态**：进度条 + 文字说明
- **成功状态**：绿色提示 + 图标
- **错误状态**：红色提示 + 图标
- **警告状态**：橙色提示 + 图标

### 7.2 动画效果
- **页面切换**：淡入淡出 (300ms)
- **按钮点击**：轻微缩放 (150ms)
- **弹窗显示**：从中心放大 (200ms)
- **列表加载**：从上到下依次显示

## 8. 特殊界面设计

### 8.1 登录界面
- **布局**：居中卡片式
- **背景**：企业品牌色渐变
- **元素**：Logo + 登录表单 + 版权信息

### 8.2 主控台界面
- **布局**：仪表板式
- **元素**：快捷操作卡片 + 数据统计 + 最近操作

### 8.3 操作界面
- **布局**：左右分栏
- **左侧**：操作表单
- **右侧**：实时预览/结果显示

## 9. 无障碍设计

### 9.1 颜色对比度
- **正文文字**：对比度 ≥ 4.5:1
- **大字体**：对比度 ≥ 3:1
- **图标**：对比度 ≥ 3:1

### 9.2 键盘导航
- **Tab键**：逻辑顺序导航
- **Enter键**：确认操作
- **Esc键**：取消/关闭

### 9.3 触摸友好
- **最小点击区域**：44px × 44px
- **间距**：相邻可点击元素间距 ≥ 8px

## 10. 实现示例

### 10.1 运行UI演示
```bash
# 进入项目目录
cd wms-front

# 安装依赖
pip install PyQt6

# 运行UI演示
python examples/ui_demo.py
```

### 10.2 主要文件说明
- `src/ui/styles/theme.py` - 主题配置文件
- `src/ui/components/base_components.py` - 基础UI组件
- `src/ui/windows/login_window.py` - 登录窗口
- `src/ui/windows/main_window.py` - 主窗口
- `examples/ui_demo.py` - UI演示程序

### 10.3 设计特点
1. **企业级色彩方案**：以深蓝色为主色调，体现专业性
2. **简约布局设计**：去除冗余元素，突出核心功能
3. **大气视觉效果**：合理留白，宽敞的界面布局
4. **一致性体验**：统一的组件样式和交互模式

### 10.4 自定义主题
```python
# 修改主题颜色
from src.ui.styles.theme import theme

# 自定义主色调
theme.colors.PRIMARY = "#2563EB"  # 新的主色调
theme.colors.SECONDARY = "#3B82F6"  # 新的辅助色

# 自定义字体大小
theme.typography.BODY_SIZE = 16  # 增大正文字体
```

## 11. 最佳实践

### 11.1 组件使用
- 优先使用基础组件库中的组件
- 保持组件样式的一致性
- 合理使用颜色和间距

### 11.2 布局设计
- 遵循8px网格系统
- 保持适当的留白空间
- 使用卡片式布局组织内容

### 11.3 交互设计
- 提供清晰的状态反馈
- 使用适当的动画效果
- 确保操作的可预测性
