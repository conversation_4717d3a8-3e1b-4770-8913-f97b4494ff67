"""
WMS客户端系统 - 报表分析窗口
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QTableWidget, QTableWidgetItem,
    QDateEdit, QComboBox, QTabWidget, QApplication, QScrollArea,
    QMessageBox, QGroupBox, QHeaderView
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont

from ..components.base_components import (
    BaseCard, BaseTable, PrimaryButton, SecondaryButton,
    TitleLabel, BodyLabel, UnifiedToolbar
)
from ..styles.theme import theme


class ReportsWindow(QMainWindow):
    """报表分析窗口"""
    
    # 信号定义
    back_to_dashboard = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        self._load_sample_data()
    
    def _setup_ui(self):
        """设置UI - 触摸屏优化"""
        self.setWindowTitle("报表分析")
        
        # 获取屏幕信息
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        
        # 设置全屏
        self.setGeometry(screen_geometry)
        self.showFullScreen()
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:0.5 #2563eb, stop:1 #3b82f6);
            }
        """)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        margin = max(15, min(25, int(screen_geometry.width() * 0.018)))
        spacing = max(10, min(18, int(screen_geometry.height() * 0.015)))
        layout.setContentsMargins(margin, margin, margin, margin)
        layout.setSpacing(spacing)
        
        # 工具栏 - 与智能查询样式完全一致
        toolbar_widget = QWidget()
        toolbar_widget.setFixedHeight(80)
        toolbar_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:1 rgba(248, 250, 252, 0.95));
                border-radius: 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)

        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(30, 20, 30, 20)
        toolbar_layout.setSpacing(20)

        # 返回按钮
        back_btn = QPushButton("← 返回主界面")
        back_btn.setFixedSize(140, 40)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 600;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        back_btn.clicked.connect(self.back_to_dashboard.emit)
        toolbar_layout.addWidget(back_btn)

        # 标题
        title_label = QLabel("📊 报表分析")
        title_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 24px;
                font-weight: 700;
                background: transparent;
                border: none;
            }
        """)
        toolbar_layout.addWidget(title_label)

        toolbar_layout.addStretch()

        # 导出数据按钮
        export_btn = QPushButton("📤 导出数据")
        export_btn.setFixedSize(120, 40)
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 600;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        export_btn.clicked.connect(self._export_report)
        toolbar_layout.addWidget(export_btn)

        layout.addWidget(toolbar_widget)
        
        # 内容区域 - 与快速查询样式完全一致
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:1 rgba(248, 250, 252, 0.95));
                border-radius: 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)

        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(30, 30, 30, 30)
        content_layout.setSpacing(20)

        # 报表选项卡 - 与快速查询样式完全一致
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8fafc;
                color: #374151;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: 600;
            }
            QTabBar::tab:selected {
                background-color: #3b82f6;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #e5e7eb;
            }
        """)
        
        # 添加选项卡
        self._create_inventory_report_tab()
        self._create_transaction_report_tab()
        self._create_performance_report_tab()

        content_layout.addWidget(self.tab_widget)
        layout.addWidget(content_widget)
    
    def _export_report(self):
        """导出报表"""
        QMessageBox.information(self, "导出报表", "报表导出功能开发中...")

    def _get_group_style(self):
        """获取分组样式 - 与快速查询风格完全一致"""
        return f"""
            QGroupBox {{
                font-size: {theme.typography.BODY_SIZE}px;
                font-weight: {theme.typography.WEIGHT_MEDIUM};
                color: {theme.colors.TEXT_PRIMARY};
                border: 2px solid {theme.colors.BORDER};
                border-radius: {theme.border_radius.LG}px;
                margin-top: {theme.spacing.SM}px;
                padding-top: {theme.spacing.SM}px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {theme.spacing.SM}px;
                padding: 0 {theme.spacing.SM}px 0 {theme.spacing.SM}px;
            }}
        """

    def _get_button_style(self, color):
        """获取按钮样式 - 与快速查询风格完全一致"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: {theme.colors.WHITE};
                border: none;
                border-radius: {theme.border_radius.LG}px;
                font-size: {theme.typography.BODY_SIZE}px;
                font-weight: {theme.typography.WEIGHT_MEDIUM};
                padding: {theme.spacing.SM}px {theme.spacing.MD}px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
            QPushButton:pressed {{
                background-color: {color}bb;
            }}
        """

    def _style_input(self, widget):
        """设置输入控件样式 - 与快速查询风格完全一致"""
        widget.setStyleSheet(f"""
            QLineEdit, QComboBox, QDateEdit {{
                background-color: {theme.colors.WHITE};
                border: 2px solid {theme.colors.BORDER};
                border-radius: {theme.border_radius.LG}px;
                padding: {theme.spacing.SM}px {theme.spacing.MD}px;
                font-size: {theme.typography.BODY_SIZE}px;
                color: {theme.colors.TEXT_PRIMARY};
                min-height: 20px;
            }}
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {{
                border-color: {theme.colors.SECONDARY};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 25px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid {theme.colors.TEXT_SECONDARY};
                margin-right: {theme.spacing.SM}px;
            }}
        """)

    def _style_table(self, table):
        """设置表格样式 - 与快速查询风格完全一致"""
        table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {theme.colors.WHITE};
                border: 1px solid {theme.colors.BORDER};
                border-radius: {theme.border_radius.MD}px;
                gridline-color: {theme.colors.BORDER};
                font-size: {theme.typography.BODY_SIZE}px;
            }}
            QTableWidget::item {{
                padding: {theme.spacing.SM}px;
                border-bottom: 1px solid {theme.colors.BORDER};
            }}
            QTableWidget::item:selected {{
                background-color: {theme.colors.SECONDARY};
                color: {theme.colors.WHITE};
            }}
            QHeaderView::section {{
                background-color: {theme.colors.BACKGROUND};
                color: {theme.colors.TEXT_PRIMARY};
                padding: {theme.spacing.MD}px;
                border: none;
                border-bottom: 2px solid {theme.colors.PRIMARY};
                font-weight: {theme.typography.WEIGHT_MEDIUM};
            }}
        """)

        # 设置列宽自适应
        header = table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

    def _load_inventory_report_data(self):
        """加载库存报表数据"""
        data = [
            ["P001", "苹果 iPhone 15", "128GB 蓝色", "台", "50", "¥5,999", "¥299,950"],
            ["P002", "华为 Mate 60", "256GB 白色", "台", "30", "¥6,999", "¥209,970"],
            ["P003", "小米 14", "256GB 黑色", "台", "25", "¥3,999", "¥99,975"],
            ["P004", "OPPO Find X7", "512GB 金色", "台", "15", "¥4,999", "¥74,985"],
            ["P005", "vivo X100", "256GB 蓝色", "台", "40", "¥4,299", "¥171,960"],
        ]

        self.inventory_table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(cell_data)
                self.inventory_table.setItem(row, col, item)

    def _load_transaction_report_data(self):
        """加载进出库报表数据"""
        data = [
            ["2024-01-30", "入库", "P001", "苹果 iPhone 15", "20", "张三"],
            ["2024-01-30", "出库", "P002", "华为 Mate 60", "5", "李四"],
            ["2024-01-30", "入库", "P003", "小米 14", "15", "王五"],
            ["2024-01-29", "出库", "P004", "OPPO Find X7", "8", "张三"],
            ["2024-01-29", "入库", "P005", "vivo X100", "25", "李四"],
        ]

        self.transaction_table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(cell_data)
                self.transaction_table.setItem(row, col, item)

    def _load_performance_report_data(self):
        """加载绩效报表数据"""
        data = [
            ["1", "张三", "156", "99.5%", "2.1分钟"],
            ["2", "李四", "142", "98.8%", "2.3分钟"],
            ["3", "王五", "138", "99.2%", "2.0分钟"],
            ["4", "赵六", "125", "97.9%", "2.5分钟"],
            ["5", "钱七", "118", "98.5%", "2.2分钟"],
        ]

        self.performance_table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(cell_data)
                self.performance_table.setItem(row, col, item)
    
    def _create_inventory_report_tab(self):
        """创建库存报表选项卡 - 与快速查询样式完全一致"""
        inventory_widget = QWidget()
        layout = QVBoxLayout(inventory_widget)
        layout.setSpacing(20)

        # 查询条件 - 与快速查询样式完全一致
        search_group = QGroupBox("查询条件")
        search_group.setStyleSheet(self._get_group_style())
        search_layout = QHBoxLayout(search_group)
        search_layout.setSpacing(15)

        # 查询日期
        self.inventory_date = QDateEdit()
        self.inventory_date.setDate(QDate.currentDate())
        self.inventory_date.setFixedHeight(35)
        self._style_input(self.inventory_date)
        search_layout.addWidget(QLabel("查询日期:"))
        search_layout.addWidget(self.inventory_date)

        # 商品类别
        self.inventory_category = QComboBox()
        self.inventory_category.addItems(["全部", "电子产品", "办公用品", "生活用品", "其他"])
        self.inventory_category.setFixedHeight(35)
        self._style_input(self.inventory_category)
        search_layout.addWidget(QLabel("商品类别:"))
        search_layout.addWidget(self.inventory_category)

        # 查询按钮
        search_btn = QPushButton("🔍 查询")
        search_btn.setFixedSize(100, 35)
        search_btn.setStyleSheet(self._get_button_style("#3b82f6"))
        search_btn.clicked.connect(self._query_inventory_report)
        search_layout.addWidget(search_btn)

        # 导出按钮
        export_btn = QPushButton("📊 导出")
        export_btn.setFixedSize(100, 35)
        export_btn.setStyleSheet(self._get_button_style("#10b981"))
        export_btn.clicked.connect(self._export_inventory_report)
        search_layout.addWidget(export_btn)

        layout.addWidget(search_group)
        
        # 结果表格 - 与快速查询样式完全一致
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(7)
        self.inventory_table.setHorizontalHeaderLabels([
            "商品编码", "商品名称", "规格型号", "单位", "库存数量", "单价", "库存金额"
        ])
        self._style_table(self.inventory_table)

        # 添加示例数据
        self._load_inventory_report_data()

        layout.addWidget(self.inventory_table)
        
        self.tab_widget.addTab(inventory_widget, "📦 库存报表")
    
    def _create_transaction_report_tab(self):
        """创建进出库报表选项卡 - 与快速查询样式完全一致"""
        transaction_widget = QWidget()
        layout = QVBoxLayout(transaction_widget)
        layout.setSpacing(20)

        # 查询条件 - 与快速查询样式完全一致
        search_group = QGroupBox("查询条件")
        search_group.setStyleSheet(self._get_group_style())
        search_layout = QHBoxLayout(search_group)
        search_layout.setSpacing(15)

        # 开始日期
        self.trans_start_date = QDateEdit()
        self.trans_start_date.setDate(QDate.currentDate().addDays(-7))
        self.trans_start_date.setFixedHeight(35)
        self._style_input(self.trans_start_date)
        search_layout.addWidget(QLabel("开始日期:"))
        search_layout.addWidget(self.trans_start_date)

        # 结束日期
        self.trans_end_date = QDateEdit()
        self.trans_end_date.setDate(QDate.currentDate())
        self.trans_end_date.setFixedHeight(35)
        self._style_input(self.trans_end_date)
        search_layout.addWidget(QLabel("结束日期:"))
        search_layout.addWidget(self.trans_end_date)

        # 操作类型
        self.trans_type = QComboBox()
        self.trans_type.addItems(["全部", "入库", "出库"])
        self.trans_type.setFixedHeight(35)
        self._style_input(self.trans_type)
        search_layout.addWidget(QLabel("操作类型:"))
        search_layout.addWidget(self.trans_type)

        # 查询按钮
        search_btn = QPushButton("🔍 查询")
        search_btn.setFixedSize(100, 35)
        search_btn.setStyleSheet(self._get_button_style("#3b82f6"))
        search_btn.clicked.connect(self._query_transaction_report)
        search_layout.addWidget(search_btn)

        layout.addWidget(search_group)
        
        # 结果表格 - 与快速查询样式完全一致
        self.transaction_table = QTableWidget()
        self.transaction_table.setColumnCount(6)
        self.transaction_table.setHorizontalHeaderLabels([
            "日期", "操作类型", "商品编码", "商品名称", "数量", "操作员"
        ])
        self._style_table(self.transaction_table)

        # 添加示例数据
        self._load_transaction_report_data()

        layout.addWidget(self.transaction_table)

        self.tab_widget.addTab(transaction_widget, "📊 进出库报表")
    
    def _create_performance_report_tab(self):
        """创建绩效报表选项卡 - 与快速查询样式完全一致"""
        performance_widget = QWidget()
        layout = QVBoxLayout(performance_widget)
        layout.setSpacing(20)

        # 查询条件 - 与快速查询样式完全一致
        search_group = QGroupBox("查询条件")
        search_group.setStyleSheet(self._get_group_style())
        search_layout = QHBoxLayout(search_group)
        search_layout.setSpacing(15)

        # 统计周期
        self.perf_period = QComboBox()
        self.perf_period.addItems(["今日", "本周", "本月", "本季度"])
        self.perf_period.setFixedHeight(35)
        self._style_input(self.perf_period)
        search_layout.addWidget(QLabel("统计周期:"))
        search_layout.addWidget(self.perf_period)

        # 操作员
        self.perf_operator = QComboBox()
        self.perf_operator.addItems(["全部", "张三", "李四", "王五"])
        self.perf_operator.setFixedHeight(35)
        self._style_input(self.perf_operator)
        search_layout.addWidget(QLabel("操作员:"))
        search_layout.addWidget(self.perf_operator)

        # 查询按钮
        search_btn = QPushButton("🔍 查询")
        search_btn.setFixedSize(100, 35)
        search_btn.setStyleSheet(self._get_button_style("#3b82f6"))
        search_btn.clicked.connect(self._query_performance_report)
        search_layout.addWidget(search_btn)

        layout.addWidget(search_group)

        # 结果表格 - 与快速查询样式完全一致
        self.performance_table = QTableWidget()
        self.performance_table.setColumnCount(5)
        self.performance_table.setHorizontalHeaderLabels([
            "排名", "操作员", "操作次数", "准确率", "平均时间"
        ])
        self._style_table(self.performance_table)

        # 添加示例数据
        self._load_performance_report_data()

        layout.addWidget(self.performance_table)

        self.tab_widget.addTab(performance_widget, "🏆 绩效报表")
    

    
    def _query_inventory_report(self):
        """查询库存报表"""
        QMessageBox.information(self, "查询", "库存报表查询功能开发中...")
    
    def _export_inventory_report(self):
        """导出库存报表"""
        QMessageBox.information(self, "导出", "报表导出功能开发中...")
    
    def _query_transaction_report(self):
        """查询进出库报表"""
        QMessageBox.information(self, "查询", "进出库报表查询功能开发中...")

    def _query_performance_report(self):
        """查询绩效报表"""
        QMessageBox.information(self, "查询", "绩效报表查询功能开发中...")

    def _load_sample_data(self):
        """加载示例数据"""
        # 这个方法保持为空，因为数据已经在各个选项卡创建时加载了
        pass

    def _style_input_control(self, widget):
        """设置输入控件统一样式"""
        widget.setStyleSheet(f"""
            QLineEdit, QComboBox, QDateEdit {{
                background-color: {theme.colors.WHITE};
                border: 2px solid {theme.colors.BORDER};
                border-radius: {theme.border_radius.LG}px;
                padding: {theme.spacing.SM}px {theme.spacing.MD}px;
                font-size: {theme.typography.BODY_SIZE}px;
                color: {theme.colors.TEXT_PRIMARY};
                min-height: 20px;
            }}
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {{
                border-color: {theme.colors.SECONDARY};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 25px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid {theme.colors.TEXT_SECONDARY};
                margin-right: {theme.spacing.SM}px;
            }}
            QComboBox QAbstractItemView {{
                background: {theme.colors.WHITE};
                border: 1px solid {theme.colors.BORDER};
                border-radius: {theme.border_radius.MD}px;
                color: {theme.colors.TEXT_PRIMARY};
                selection-background-color: {theme.colors.SECONDARY};
                selection-color: {theme.colors.WHITE};
            }}
        """)
